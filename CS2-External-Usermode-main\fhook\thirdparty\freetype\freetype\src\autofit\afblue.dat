//  afblue.dat
//
//    Auto-fitter data for blue strings.
//
//  Copyright 2013-2018 by
//  <PERSON>, <PERSON>, and <PERSON>.
//
//  This file is part of the FreeType project, and may only be used,
//  modified, and distributed under the terms of the FreeType project
//  license, LICENSE.TXT.  By continuing to use, modify, or distribute
//  this file you indicate that you have read the license and
//  understand and accept it fully.


// This file contains data specific to blue zones.  It gets processed by
// a script to simulate `jagged arrays', with enumeration values holding
// offsets into the arrays.
//
// The format of the file is rather simple:  A section starts with three
// labels separated by whitespace and followed by a colon (everything in a
// single line); the first label gives the name of the enumeration template,
// the second the name of the array template, and the third the name of the
// `maximum' template.  The script then fills the corresponding templates
// (indicated by `@' characters around the name).
//
// A section contains one or more data records.  Each data record consists
// of two or more lines.  The first line holds the enumeration name, and the
// remaining lines the corresponding array data.
//
// There are two possible representations for array data.
//
// - A string of characters or character clusters (for example, representing
//   Aksharas, Devanagari syllables) in UTF-8 encoding enclosed in double
//   quotes, using C syntax, where the elements are separated by spaces.
//   There can be only one string per line, thus the starting and ending
//   double quote must be the first and last character in the line,
//   respectively, ignoring whitespace before and after the string.  If
//   there are multiple strings (in multiple lines), they are concatenated
//   to a single string.  In the output, a string gets represented as a
//   series of singles bytes, followed by a zero byte.  The enumeration
//   values simply hold byte offsets to the start of the corresponding
//   strings.
//
//   For strings, the `maximum' template holds the maximum number of
//   non-space characters in all strings.
//
// - Data blocks enclosed in balanced braces, which get copied verbatim and
//   which can span multiple lines.  The opening brace of a block must be
//   the first character of a line (ignoring whitespace), and the closing
//   brace the last (ignoring whitespace also).  The script appends a comma
//   character after each block and counts the number of blocks to set the
//   enumeration values.
//
//   For data blocks, the `maximum' template holds the maximum number of
//   array elements.
//
// A section can contain either strings only or data blocks only.
//
// A comment line starts with `//'; it gets removed.  A preprocessor
// directive line (using the standard syntax of `cpp') starts with `#' and
// gets copied verbatim to both the enumeration and the array.  Whitespace
// outside of a string is insignificant.
//
// Preprocessor directives are ignored while the script computes maximum
// values; this essentially means that the maximum values can easily be too
// large.  Given that the purpose of those values is to create local
// fixed-size arrays at compile time for further processing of the blue zone
// data, this isn't a problem.  Note the final zero byte of a string is not
// counted.  Note also that the count holds the number of UTF-8 encoded
// characters, not bytes.


// The blue zone string data, to be used in the blue stringsets below.

AF_BLUE_STRING_ENUM AF_BLUE_STRINGS_ARRAY AF_BLUE_STRING_MAX_LEN:

  AF_BLUE_STRING_ADLAM_CAPITAL_TOP
    "𞤌 𞤅 𞤈 𞤏 𞤔 𞤚"
  AF_BLUE_STRING_ADLAM_CAPITAL_BOTTOM
    "𞤂 𞤖"
  AF_BLUE_STRING_ADLAM_SMALL_TOP
    "𞤬 𞤮 𞤻 𞤼 𞤾"
  AF_BLUE_STRING_ADLAM_SMALL_BOTTOM
    "𞤤 𞤨 𞤩 𞤭 𞤴 𞤸 𞤺 𞥀"

  AF_BLUE_STRING_ARABIC_TOP
    "ا إ ل ك ط ظ"
  AF_BLUE_STRING_ARABIC_BOTTOM
    "ت ث ط ظ ك"
  // We don't necessarily have access to medial forms via Unicode in case
  // Arabic presentational forms are missing.  The only character that is
  // guaranteed to have the same vertical position with joining (this is,
  // non-isolated) forms is U+0640, ARABIC TATWEEL, which must join both
  // round and flat curves.
  AF_BLUE_STRING_ARABIC_JOIN
    "ـ"

  AF_BLUE_STRING_ARMENIAN_CAPITAL_TOP
    "Ա Մ Ւ Ս Բ Գ Դ Օ"
  AF_BLUE_STRING_ARMENIAN_CAPITAL_BOTTOM
    "Ւ Ո Դ Ճ Շ Ս Տ Օ"
  AF_BLUE_STRING_ARMENIAN_SMALL_ASCENDER
    "ե է ի մ վ ֆ ճ"
  AF_BLUE_STRING_ARMENIAN_SMALL_TOP
    "ա յ ւ ս գ շ ր օ"
  AF_BLUE_STRING_ARMENIAN_SMALL_BOTTOM
    "հ ո ճ ա ե ծ ս օ"
  AF_BLUE_STRING_ARMENIAN_SMALL_DESCENDER
    "բ ը ի լ ղ պ փ ց"

  AF_BLUE_STRING_AVESTAN_TOP
    "𐬀 𐬁 𐬐 𐬛"
  AF_BLUE_STRING_AVESTAN_BOTTOM
    "𐬀 𐬁"

  AF_BLUE_STRING_BAMUM_TOP
    "ꚧ ꚨ ꛛ ꛉ ꛁ ꛈ ꛫ ꛯ"
  AF_BLUE_STRING_BAMUM_BOTTOM
    "ꚭ ꚳ ꚶ ꛬ ꚢ ꚽ ꛯ ꛲"

  AF_BLUE_STRING_BENGALI_BASE
    "অ ড ত ন ব ভ ল ক"
  AF_BLUE_STRING_BENGALI_TOP
    "ই ট ঠ ি ী ৈ ৗ"
  AF_BLUE_STRING_BENGALI_HEAD
    "ও এ ড ত ন ব ল ক"

  AF_BLUE_STRING_BUHID_TOP
    "ᝐ ᝈ"
  AF_BLUE_STRING_BUHID_LARGE
    "ᝅ ᝊ ᝎ"
  AF_BLUE_STRING_BUHID_SMALL
    "ᝂ ᝃ ᝉ ᝌ"
  AF_BLUE_STRING_BUHID_BOTTOM
    "ᝀ ᝃ ᝆ ᝉ ᝋ ᝏ ᝑ"

  AF_BLUE_STRING_CANADIAN_SYLLABICS_TOP
    "ᗜ ᖴ ᐁ ᒣ ᑫ ᑎ ᔑ ᗰ"
  AF_BLUE_STRING_CANADIAN_SYLLABICS_BOTTOM
    "ᗶ ᖵ ᒧ ᐃ ᑌ ᒍ ᔑ ᗢ"
  AF_BLUE_STRING_CANADIAN_SYLLABICS_SMALL_TOP
    "ᓓ ᓕ ᓀ ᓂ ᓄ ᕄ ᕆ ᘣ"
  AF_BLUE_STRING_CANADIAN_SYLLABICS_SMALL_BOTTOM
    "ᕃ ᓂ ᓀ ᕂ ᓗ ᓚ ᕆ ᘣ"
  AF_BLUE_STRING_CANADIAN_SYLLABICS_SUPS_TOP
    "ᐪ ᙆ ᣘ ᐢ ᒾ ᣗ ᔆ"
  AF_BLUE_STRING_CANADIAN_SYLLABICS_SUPS_BOTTOM
    "ᙆ ᗮ ᒻ ᐞ ᔆ ᒡ ᒢ ᓑ"

  AF_BLUE_STRING_CARIAN_TOP
    "𐊧 𐊫 𐊬 𐊭 𐊱 𐊺 𐊼 𐊿"
  AF_BLUE_STRING_CARIAN_BOTTOM
    "𐊣 𐊧 𐊷 𐋀 𐊫 𐊸 𐋉"

  AF_BLUE_STRING_CHAKMA_TOP
    "𑄃 𑄅 𑄉 𑄙 𑄗"
  AF_BLUE_STRING_CHAKMA_BOTTOM
    "𑄅 𑄛 𑄝 𑄗 𑄓"
  AF_BLUE_STRING_CHAKMA_DESCENDER
    "𑄖𑄳𑄢 𑄘𑄳𑄢 𑄙𑄳𑄢 𑄤𑄳𑄢 𑄥𑄳𑄢"

  AF_BLUE_STRING_CHEROKEE_CAPITAL
    "Ꮖ Ꮋ Ꭼ Ꮓ Ꭴ Ꮳ Ꭶ Ꮥ"
  AF_BLUE_STRING_CHEROKEE_SMALL_ASCENDER
    "ꮒ ꮤ ꮶ ꭴ ꭾ ꮗ ꮝ ꮿ"
  AF_BLUE_STRING_CHEROKEE_SMALL
    "ꮖ ꭼ ꮓ ꮠ ꮳ ꭶ ꮥ ꮻ"
  AF_BLUE_STRING_CHEROKEE_SMALL_DESCENDER
    "ᏸ ꮐ ꭹ ꭻ"

  AF_BLUE_STRING_COPTIC_CAPITAL_TOP
    "Ⲍ Ⲏ Ⲡ Ⳟ Ⲟ Ⲑ Ⲥ Ⳋ"
  AF_BLUE_STRING_COPTIC_CAPITAL_BOTTOM
    "Ⳑ Ⳙ Ⳟ Ⲏ Ⲟ Ⲑ Ⳝ Ⲱ"
  AF_BLUE_STRING_COPTIC_SMALL_TOP
    "ⲍ ⲏ ⲡ ⳟ ⲟ ⲑ ⲥ ⳋ"
  AF_BLUE_STRING_COPTIC_SMALL_BOTTOM
    "ⳑ ⳙ ⳟ ⲏ ⲟ ⲑ ⳝ Ⳓ"

  AF_BLUE_STRING_CYPRIOT_TOP
    "𐠍 𐠙 𐠳 𐠱 𐠅 𐠓 𐠣 𐠦"
  AF_BLUE_STRING_CYPRIOT_BOTTOM
    "𐠃 𐠊 𐠛 𐠣 𐠳 𐠵 𐠐"
  AF_BLUE_STRING_CYPRIOT_SMALL
    "𐠈 𐠏 𐠖"

  AF_BLUE_STRING_CYRILLIC_CAPITAL_TOP
    "Б В Е П З О С Э"
  AF_BLUE_STRING_CYRILLIC_CAPITAL_BOTTOM
    "Б В Е Ш З О С Э"
  AF_BLUE_STRING_CYRILLIC_SMALL
    "х п н ш е з о с"
  AF_BLUE_STRING_CYRILLIC_SMALL_DESCENDER
    "р у ф"

  AF_BLUE_STRING_DESERET_CAPITAL_TOP
    "𐐂 𐐄 𐐋 𐐗 𐐑"
  AF_BLUE_STRING_DESERET_CAPITAL_BOTTOM
    "𐐀 𐐂 𐐄 𐐗 𐐛"
  AF_BLUE_STRING_DESERET_SMALL_TOP
    "𐐪 𐐬 𐐳 𐐿 𐐹"
  AF_BLUE_STRING_DESERET_SMALL_BOTTOM
    "𐐨 𐐪 𐐬 𐐿 𐑃"

  AF_BLUE_STRING_DEVANAGARI_BASE
    "क म अ आ थ ध भ श"
  AF_BLUE_STRING_DEVANAGARI_TOP
    "ई ऐ ओ औ ि ी ो ौ"
  // note that some fonts have extreme variation in the height of the
  // round head elements; for this reason we also define the `base'
  // blue zone, which must be always present
  AF_BLUE_STRING_DEVANAGARI_HEAD
    "क म अ आ थ ध भ श"
  AF_BLUE_STRING_DEVANAGARI_BOTTOM
    "ु ृ"

  AF_BLUE_STRING_ETHIOPIC_TOP
    "ሀ ሃ ዘ ፐ ማ በ ዋ ዐ"
  AF_BLUE_STRING_ETHIOPIC_BOTTOM
    "ለ ሐ በ ዘ ሀ ሪ ዐ ጨ"

  AF_BLUE_STRING_GEORGIAN_MKHEDRULI_TOP
    "გ დ ე ვ თ ი ო ღ"
  AF_BLUE_STRING_GEORGIAN_MKHEDRULI_BOTTOM
    "ა ზ მ ს შ ძ ხ პ"
  AF_BLUE_STRING_GEORGIAN_MKHEDRULI_ASCENDER
    "ს ხ ქ ზ მ შ ჩ წ"
  AF_BLUE_STRING_GEORGIAN_MKHEDRULI_DESCENDER
    "ე ვ ჟ ტ უ ფ ქ ყ"

  AF_BLUE_STRING_GEORGIAN_ASOMTAVRULI_TOP
    "Ⴑ Ⴇ Ⴙ Ⴜ Ⴄ Ⴅ Ⴓ Ⴚ"
  AF_BLUE_STRING_GEORGIAN_ASOMTAVRULI_BOTTOM
    "Ⴄ Ⴅ Ⴇ Ⴈ Ⴆ Ⴑ Ⴊ Ⴋ"

  AF_BLUE_STRING_GEORGIAN_NUSKHURI_TOP
    "ⴁ ⴗ ⴂ ⴄ ⴅ ⴇ ⴔ ⴖ"
  AF_BLUE_STRING_GEORGIAN_NUSKHURI_BOTTOM
    "ⴈ ⴌ ⴖ ⴎ ⴃ ⴆ ⴋ ⴢ"
  AF_BLUE_STRING_GEORGIAN_NUSKHURI_ASCENDER
    "ⴐ ⴑ ⴓ ⴕ ⴙ ⴛ ⴡ ⴣ"
  AF_BLUE_STRING_GEORGIAN_NUSKHURI_DESCENDER
    "ⴄ ⴅ ⴔ ⴕ ⴁ ⴂ ⴘ ⴝ"

  AF_BLUE_STRING_GEORGIAN_MTAVRULI_TOP
    "Ნ Ჟ Ჳ Ჸ Გ Ე Ო Ჴ"
  AF_BLUE_STRING_GEORGIAN_MTAVRULI_BOTTOM
    "Ი Ჲ Ო Ჩ Მ Შ Ჯ Ჽ"

  AF_BLUE_STRING_GLAGOLITIC_CAPITAL_TOP
    "Ⰵ Ⱄ Ⱚ Ⰴ Ⰲ Ⰺ Ⱛ Ⰻ"
  AF_BLUE_STRING_GLAGOLITIC_CAPITAL_BOTTOM
    "Ⰵ Ⰴ Ⰲ Ⱚ Ⱎ Ⱑ Ⰺ Ⱄ"
  AF_BLUE_STRING_GLAGOLITIC_SMALL_TOP
    "ⰵ ⱄ ⱚ ⰴ ⰲ ⰺ ⱛ ⰻ"
  AF_BLUE_STRING_GLAGOLITIC_SMALL_BOTTOM
    "ⰵ ⰴ ⰲ ⱚ ⱎ ⱑ ⰺ ⱄ"

  AF_BLUE_STRING_GOTHIC_TOP
    "𐌲 𐌶 𐍀 𐍄 𐌴 𐍃 𐍈 𐌾"
  AF_BLUE_STRING_GOTHIC_BOTTOM
    "𐌶 𐌴 𐍃 𐍈"

  AF_BLUE_STRING_GREEK_CAPITAL_TOP
    "Γ Β Ε Ζ Θ Ο Ω"
  AF_BLUE_STRING_GREEK_CAPITAL_BOTTOM
    "Β Δ Ζ Ξ Θ Ο"
  AF_BLUE_STRING_GREEK_SMALL_BETA_TOP
    "β θ δ ζ λ ξ"
  AF_BLUE_STRING_GREEK_SMALL
    "α ε ι ο π σ τ ω"
  AF_BLUE_STRING_GREEK_SMALL_DESCENDER
    "β γ η μ ρ φ χ ψ"

  AF_BLUE_STRING_GUJARATI_TOP
    "ત ન ઋ ઌ છ ટ ર ૦"
  AF_BLUE_STRING_GUJARATI_BOTTOM
    "ખ ગ ઘ ઞ ઇ ઈ ઠ જ"
  AF_BLUE_STRING_GUJARATI_ASCENDER
    "ઈ ઊ િ ી લી શ્ચિ જિ સી"
  AF_BLUE_STRING_GUJARATI_DESCENDER
    "ુ ૃ ૄ ખુ છૃ છૄ"
  AF_BLUE_STRING_GUJARATI_DIGIT_TOP
    "૦ ૧ ૨ ૩ ૭"

  AF_BLUE_STRING_GURMUKHI_BASE
    "ਕ ਗ ਙ ਚ ਜ ਤ ਧ ਸ"
  AF_BLUE_STRING_GURMUKHI_HEAD
    "ਕ ਗ ਙ ਚ ਜ ਤ ਧ ਸ"
  AF_BLUE_STRING_GURMUKHI_TOP
    "ਇ ਈ ਉ ਏ ਓ ੳ ਿ ੀ"
  AF_BLUE_STRING_GURMUKHI_BOTTOM
    "ਅ ਏ ਓ ਗ ਜ ਠ ਰ ਸ"
  AF_BLUE_STRING_GURMUKHI_DIGIT_TOP
    "੦ ੧ ੨ ੩ ੭"

  AF_BLUE_STRING_HEBREW_TOP
    "ב ד ה ח ך כ ם ס"
  AF_BLUE_STRING_HEBREW_BOTTOM
    "ב ט כ ם ס צ"
  AF_BLUE_STRING_HEBREW_DESCENDER
    "ק ך ן ף ץ"

  AF_BLUE_STRING_KANNADA_TOP
    "ಇ ಊ ಐ ಣ ಸಾ ನಾ ದಾ ರಾ"
  AF_BLUE_STRING_KANNADA_BOTTOM
    "ಅ ಉ ಎ ಲ ೦ ೨ ೬ ೭"

  AF_BLUE_STRING_KAYAH_LI_TOP
    "꤅ ꤏ ꤁ ꤋ ꤀ ꤍ"
  AF_BLUE_STRING_KAYAH_LI_BOTTOM
    "꤈ ꤘ ꤀ ꤍ ꤢ"
  AF_BLUE_STRING_KAYAH_LI_ASCENDER
    "ꤖ ꤡ"
  AF_BLUE_STRING_KAYAH_LI_DESCENDER
    "ꤑ ꤜ ꤞ"
  AF_BLUE_STRING_KAYAH_LI_LARGE_DESCENDER
    "ꤑ꤬ ꤜ꤭ ꤔ꤬"

  AF_BLUE_STRING_KHMER_TOP
    "ខ ទ ន ឧ ឩ ា"
  AF_BLUE_STRING_KHMER_SUBSCRIPT_TOP
    "ក្ក ក្ខ ក្គ ក្ថ"
  AF_BLUE_STRING_KHMER_BOTTOM
    "ខ ឃ ច ឋ ប ម យ ឲ"
  AF_BLUE_STRING_KHMER_DESCENDER
    "ត្រ រៀ ឲ្យ អឿ"
  AF_BLUE_STRING_KHMER_LARGE_DESCENDER
    "ន្ត្រៃ ង្ខ្យ ក្បៀ ច្រៀ ន្តឿ ល្បឿ"

  AF_BLUE_STRING_KHMER_SYMBOLS_WAXING_TOP
    "᧠ ᧡"
  AF_BLUE_STRING_KHMER_SYMBOLS_WANING_BOTTOM
    "᧶ ᧹"

  AF_BLUE_STRING_LAO_TOP
    "າ ດ ອ ມ ລ ວ ຣ ງ"
  AF_BLUE_STRING_LAO_BOTTOM
    "າ ອ ບ ຍ ຣ ຮ ວ ຢ"
  AF_BLUE_STRING_LAO_ASCENDER
    "ປ ຢ ຟ ຝ"
  AF_BLUE_STRING_LAO_LARGE_ASCENDER
    "ໂ ໄ ໃ"
  AF_BLUE_STRING_LAO_DESCENDER
    "ງ ຊ ຖ ຽ ໆ ຯ"

  AF_BLUE_STRING_LATIN_CAPITAL_TOP
    "T H E Z O C Q S"
  AF_BLUE_STRING_LATIN_CAPITAL_BOTTOM
    "H E Z L O C U S"
  AF_BLUE_STRING_LATIN_SMALL_F_TOP
    "f i j k d b h"
  AF_BLUE_STRING_LATIN_SMALL_TOP
    "u v x z o e s c"
  AF_BLUE_STRING_LATIN_SMALL_BOTTOM
    "n r x z o e s c"
  AF_BLUE_STRING_LATIN_SMALL_DESCENDER
    "p q g j y"

  // we assume that both the subscript and superscript ranges
  // don't contain oldstyle digits (actually, most fonts probably
  // have digits only in those ranges)
  AF_BLUE_STRING_LATIN_SUBS_CAPITAL_TOP
    "₀ ₃ ₅ ₇ ₈"
  AF_BLUE_STRING_LATIN_SUBS_CAPITAL_BOTTOM
    "₀ ₁ ₂ ₃ ₈"
  AF_BLUE_STRING_LATIN_SUBS_SMALL_F_TOP
    "ᵢ ⱼ ₕ ₖ ₗ"
  AF_BLUE_STRING_LATIN_SUBS_SMALL
    "ₐ ₑ ₒ ₓ ₙ ₛ ᵥ ᵤ ᵣ"
  AF_BLUE_STRING_LATIN_SUBS_SMALL_DESCENDER
    "ᵦ ᵧ ᵨ ᵩ ₚ"

  AF_BLUE_STRING_LATIN_SUPS_CAPITAL_TOP
    "⁰ ³ ⁵ ⁷ ᵀ ᴴ ᴱ ᴼ"
  AF_BLUE_STRING_LATIN_SUPS_CAPITAL_BOTTOM
    "⁰ ¹ ² ³ ᴱ ᴸ ᴼ ᵁ"
  AF_BLUE_STRING_LATIN_SUPS_SMALL_F_TOP
    "ᵇ ᵈ ᵏ ʰ ʲ ᶠ ⁱ"
  AF_BLUE_STRING_LATIN_SUPS_SMALL
    "ᵉ ᵒ ʳ ˢ ˣ ᶜ ᶻ"
  AF_BLUE_STRING_LATIN_SUPS_SMALL_DESCENDER
    "ᵖ ʸ ᵍ"

  AF_BLUE_STRING_LISU_TOP
    "ꓡ ꓧ ꓱ ꓶ ꓩ ꓚ ꓵ ꓳ"
  AF_BLUE_STRING_LISU_BOTTOM
    "ꓕ ꓜ ꓞ ꓡ ꓛ ꓢ ꓳ ꓴ"

  AF_BLUE_STRING_MALAYALAM_TOP
    "ഒ ട ഠ റ ച പ ച്ച പ്പ"
  AF_BLUE_STRING_MALAYALAM_BOTTOM
    "ട ഠ ധ ശ ഘ ച ഥ ല"

  AF_BLUE_STRING_MYANMAR_TOP
    "ခ ဂ င ဒ ဝ ၥ ၊ ။"
  AF_BLUE_STRING_MYANMAR_BOTTOM
    "င ဎ ဒ ပ ဗ ဝ ၊ ။"
  AF_BLUE_STRING_MYANMAR_ASCENDER
    "ဩ ြ ၍ ၏ ၆ ါ ိ"
  AF_BLUE_STRING_MYANMAR_DESCENDER
    "ဉ ည ဥ ဩ ဨ ၂ ၅ ၉"

  AF_BLUE_STRING_NKO_TOP
    "ߐ ߉ ߒ ߟ ߖ ߜ ߠ ߥ"
  AF_BLUE_STRING_NKO_BOTTOM
    "߀ ߘ ߡ ߠ ߥ"
  AF_BLUE_STRING_NKO_SMALL_TOP
    "ߏ ߛ ߋ"
  AF_BLUE_STRING_NKO_SMALL_BOTTOM
    "ߎ ߏ ߛ ߋ"

  AF_BLUE_STRING_OL_CHIKI
    "ᱛ ᱜ ᱝ ᱡ ᱢ ᱥ"

  AF_BLUE_STRING_OLD_TURKIC_TOP
    "𐰗 𐰘 𐰧"
  AF_BLUE_STRING_OLD_TURKIC_BOTTOM
    "𐰉 𐰗 𐰦 𐰧"

  AF_BLUE_STRING_OSAGE_CAPITAL_TOP
    "𐒾 𐓍 𐓒 𐓓 𐒻 𐓂 𐒵 𐓆"
  AF_BLUE_STRING_OSAGE_CAPITAL_BOTTOM
    "𐒰 𐓍 𐓂 𐒿 𐓎 𐒹"
  AF_BLUE_STRING_OSAGE_CAPITAL_DESCENDER
    "𐒼 𐒽 𐒾"
  AF_BLUE_STRING_OSAGE_SMALL_TOP
    "𐓵 𐓶 𐓺 𐓻 𐓝 𐓣 𐓪 𐓮"
  AF_BLUE_STRING_OSAGE_SMALL_BOTTOM
    "𐓘 𐓚 𐓣 𐓵 𐓡 𐓧 𐓪 𐓶"
  AF_BLUE_STRING_OSAGE_SMALL_ASCENDER
    "𐓤 𐓦 𐓸 𐓹 𐓛"
  AF_BLUE_STRING_OSAGE_SMALL_DESCENDER
    "𐓤 𐓥 𐓦"

  AF_BLUE_STRING_OSMANYA_TOP
    "𐒆 𐒉 𐒐 𐒒 𐒘 𐒛 𐒠 𐒣"
  AF_BLUE_STRING_OSMANYA_BOTTOM
    "𐒀 𐒂 𐒆 𐒈 𐒊 𐒒 𐒠 𐒩"

  AF_BLUE_STRING_SAURASHTRA_TOP
    "ꢜ ꢞ ꢳ ꢂ ꢖ ꢒ ꢝ ꢛ"
  AF_BLUE_STRING_SAURASHTRA_BOTTOM
    "ꢂ ꢨ ꢺ ꢤ ꢎ"

  AF_BLUE_STRING_SHAVIAN_TOP
    "𐑕 𐑙"
  AF_BLUE_STRING_SHAVIAN_BOTTOM
    "𐑔 𐑖 𐑗 𐑹 𐑻"
  AF_BLUE_STRING_SHAVIAN_DESCENDER
    "𐑟 𐑣"
  AF_BLUE_STRING_SHAVIAN_SMALL_TOP
    "𐑱 𐑲 𐑳 𐑴 𐑸 𐑺 𐑼"
  AF_BLUE_STRING_SHAVIAN_SMALL_BOTTOM
    "𐑴 𐑻 𐑹"

  AF_BLUE_STRING_SINHALA_TOP
    "ඉ ක ඝ ඳ ප ය ල ෆ"
  AF_BLUE_STRING_SINHALA_BOTTOM
    "එ ඔ ඝ ජ ට ථ ධ ර"
  AF_BLUE_STRING_SINHALA_DESCENDER
    "ද ඳ උ ල තූ තු බු දු"

  AF_BLUE_STRING_SUNDANESE_TOP
    "ᮋ ᮞ ᮮ ᮽ ᮰ ᮈ"
  AF_BLUE_STRING_SUNDANESE_BOTTOM
    "ᮄ ᮔ ᮕ ᮗ ᮰ ᮆ ᮈ ᮉ"
  AF_BLUE_STRING_SUNDANESE_DESCENDER
    "ᮼ ᳄"

  AF_BLUE_STRING_TAI_VIET_TOP
    "ꪆ ꪔ ꪒ ꪖ ꪫ"
  AF_BLUE_STRING_TAI_VIET_BOTTOM
    "ꪉ ꪫ ꪮ"

  AF_BLUE_STRING_TAMIL_TOP
    "உ ஒ ஓ ற ஈ க ங ச"
  AF_BLUE_STRING_TAMIL_BOTTOM
    "க ச ல ஶ உ ங ட ப"

  AF_BLUE_STRING_TELUGU_TOP
    "ఇ ఌ ఙ ఞ ణ ఱ ౯"
  AF_BLUE_STRING_TELUGU_BOTTOM
    "అ క చ ర ఽ ౨ ౬"

  AF_BLUE_STRING_THAI_TOP
    "บ เ แ อ ก า"
  AF_BLUE_STRING_THAI_BOTTOM
    "บ ป ษ ฯ อ ย ฮ"
  AF_BLUE_STRING_THAI_ASCENDER
    "ป ฝ ฟ"
  AF_BLUE_STRING_THAI_LARGE_ASCENDER
    "โ ใ ไ"
  AF_BLUE_STRING_THAI_DESCENDER
    "ฎ ฏ ฤ ฦ"
  AF_BLUE_STRING_THAI_LARGE_DESCENDER
    "ญ ฐ"
  AF_BLUE_STRING_THAI_DIGIT_TOP
    "๐ ๑ ๓"

  AF_BLUE_STRING_TIFINAGH
    "ⵔ ⵙ ⵛ ⵞ ⴵ ⴼ ⴹ ⵎ"

  AF_BLUE_STRING_VAI_TOP
    "ꗍ ꘖ ꘙ ꘜ ꖜ ꖝ ꔅ ꕢ"
  AF_BLUE_STRING_VAI_BOTTOM
    "ꗍ ꘖ ꘙ ꗞ ꔅ ꕢ ꖜ ꔆ"


#ifdef AF_CONFIG_OPTION_CJK

  AF_BLUE_STRING_CJK_TOP
    "他 们 你 來 們 到 和 地"
    " 对 對 就 席 我 时 時 會"
    " 来 為 能 舰 說 说 这 這"
    " 齊 |"
    " 军 同 已 愿 既 星 是 景"
    " 民 照 现 現 理 用 置 要"
    " 軍 那 配 里 開 雷 露 面"
    " 顾"
  AF_BLUE_STRING_CJK_BOTTOM
    "个 为 人 他 以 们 你 來"
    " 個 們 到 和 大 对 對 就"
    " 我 时 時 有 来 為 要 說"
    " 说 |"
    " 主 些 因 它 想 意 理 生"
    " 當 看 着 置 者 自 著 裡"
    " 过 还 进 進 過 道 還 里"
    " 面"

#ifdef AF_CONFIG_OPTION_CJK_BLUE_HANI_VERT

  AF_BLUE_STRING_CJK_LEFT
    " 些 们 你 來 們 到 和 地"
    " 她 将 將 就 年 得 情 最"
    " 样 樣 理 能 說 说 这 這"
    " 通 |"
    " 即 吗 吧 听 呢 品 响 嗎"
    " 师 師 收 断 斷 明 眼 間"
    " 间 际 陈 限 除 陳 随 際"
    " 隨"
  AF_BLUE_STRING_CJK_RIGHT
    "事 前 學 将 將 情 想 或"
    " 政 斯 新 样 樣 民 沒 没"
    " 然 特 现 現 球 第 經 谁"
    " 起 |"
    " 例 別 别 制 动 動 吗 嗎"
    " 增 指 明 朝 期 构 物 确"
    " 种 調 调 費 费 那 都 間"
    " 间"

#endif /* AF_CONFIG_OPTION_CJK_BLUE_HANI_VERT */

#endif /* AF_CONFIG_OPTION_CJK                */


// The blue zone stringsets, as used in the script styles, cf. `afstyles.h'.
//
// The AF_BLUE_PROPERTY_XXX flags are defined in `afblue.h'; here some
// explanations.
//
// A blue zone in general is defined by a reference and an overshoot line.
// During the hinting process, all coordinate values between those two lines
// are set equal to the reference value, provided that the blue zone is not
// wider than 0.75 pixels (otherwise the blue zone gets ignored).  All
// entries must have `AF_BLUE_STRING_MAX' as the final line.
//
// During the glyph analysis, edges are sorted from bottom to top, and then
// sequentially checked, edge by edge, against the blue zones in the order
// given below.
//
//
// latin auto-hinter
// -----------------
//
// Characters in a blue string are automatically classified as having a flat
// (reference) or a round (overshoot) extremum.  The blue zone is then set
// up by the mean values of all flat extrema and all round extrema,
// respectively.  Only horizontal blue zones (i.e., adjusting vertical
// coordinate values) are supported.
//
// Some scripts like Khmer need character composition to get all necessary
// blue zones, since Unicode only provides an abstract data model that
// doesn't represent all possible glyph shapes.  For such character
// clusters, the HarfBuzz library is used to convert them into the
// corresponding glyphs.  The largest glyph element (where `largest' can be
// either `largest ascender' or `largest descender') then defines the
// corresponding flat or round extremum.
//
// For the latin auto-hinter, the overshoot should be larger than the
// reference for top zones, and vice versa for bottom zones.
//
//   LATIN_TOP
//     Take the maximum flat and round coordinate values of the blue string
//     characters for computing the blue zone's reference and overshoot
//     values.
//
//     If not set, take the minimum values.
//
//     Mutually exclusive with `LATIN_SUB_TOP'.
//
//   LATIN_SUB_TOP
//     For all glyphs of a character cluster, compute the maximum flat
//     and round coordinate values of each component, then take the
//     smallest of the maximum values.  The idea is to get the top of
//     subscript glyphs, as used in Khmer, for example.  Note that
//     this mechanism doesn't work for ordinary ligatures.
//
//     This flags indicates a secondary blue zone: It gets removed if
//     there is a non-LATIN_SUB_TOP blue zone at the same coordinate
//     value (after scaling).
//
//     Mutually exclusive with `LATIN_TOP'.
//
//   LATIN_NEUTRAL
//     Ignore round extrema and define the blue zone with flat values only.
//     Both top and bottom of contours can match.  This is useful for
//     scripts like Devanagari where vowel signs attach to the base
//     character and are implemented as components of composite glyphs.
//
//     If not set, both round and flat extrema are taken into account.
//     Additionally, only the top or the bottom of a contour can match,
//     depending on the LATIN_TOP flag.
//
//     Neutral blue zones should always follow non-neutral blue zones.
//
//   LATIN_X_HEIGHT
//     Scale all glyphs vertically from the corresponding script to make the
//     reference line of this blue zone align on the grid.  The scaling
//     takes place before all other blue zones get aligned to the grid.
//     Only one blue character string of a script style can have this flag.
//
//   LATIN_LONG
//     Apply an additional constraint for blue zone values: Don't
//     necessarily use the extremum as-is but a segment of the topmost (or
//     bottommost) contour that is longer than a heuristic threshold, and
//     which is not too far away vertically from the real extremum.  This
//     ensures that small bumps in the outline are ignored (for example, the
//     `vertical serifs' found in many Hebrew glyph designs).
//
//     The segment must be at least EM/25 font units long, and the distance
//     to the extremum must be smaller than EM/4.
//
//
// cjk auto-hinter
// ---------------
//
// Characters in a blue string are *not* automatically classified.  Instead,
// first come the characters used for the overshoot value, then the
// character `|', then the characters used for the reference value
// (everything separated by space characters).  The blue zone is then set up
// by the mean values of all reference values and all overshoot values,
// respectively.  Both horizontal and vertical blue zones (i.e., adjusting
// vertical and horizontal coordinate values, respectively) are supported.
//
// For the cjk auto-hinter, the overshoot should be smaller than the
// reference for top zones, and vice versa for bottom zones.
//
//   CJK_TOP
//     Take the maximum flat and round coordinate values of the blue string
//     characters.  If not set, take the minimum values.
//
//   CJK_RIGHT
//     A synonym for CJK_TOP.  If CJK_HORIZ is set, this flag indicates the
//     right blue zone, taking horizontal maximum values.
//
//   CJK_HORIZ
//     Define a blue zone for horizontal hinting (i.e., vertical blue
//     zones).  If not set, this is a blue zone for vertical hinting.


AF_BLUE_STRINGSET_ENUM AF_BLUE_STRINGSETS_ARRAY AF_BLUE_STRINGSET_MAX_LEN:

  AF_BLUE_STRINGSET_ADLM
    { AF_BLUE_STRING_ADLAM_CAPITAL_TOP,    AF_BLUE_PROPERTY_LATIN_TOP        }
    { AF_BLUE_STRING_ADLAM_CAPITAL_BOTTOM, 0                                 }
    { AF_BLUE_STRING_ADLAM_SMALL_TOP,      AF_BLUE_PROPERTY_LATIN_TOP      |
                                           AF_BLUE_PROPERTY_LATIN_X_HEIGHT   }
    { AF_BLUE_STRING_ADLAM_SMALL_BOTTOM,   0                                 }
    { AF_BLUE_STRING_MAX,                  0                                 }

  AF_BLUE_STRINGSET_ARAB
    { AF_BLUE_STRING_ARABIC_TOP,    AF_BLUE_PROPERTY_LATIN_TOP     }
    { AF_BLUE_STRING_ARABIC_BOTTOM, 0                              }
    { AF_BLUE_STRING_ARABIC_JOIN,   AF_BLUE_PROPERTY_LATIN_NEUTRAL }
    { AF_BLUE_STRING_MAX,           0                              }

  AF_BLUE_STRINGSET_ARMN
    { AF_BLUE_STRING_ARMENIAN_CAPITAL_TOP,     AF_BLUE_PROPERTY_LATIN_TOP        }
    { AF_BLUE_STRING_ARMENIAN_CAPITAL_BOTTOM,  0                                 }
    { AF_BLUE_STRING_ARMENIAN_SMALL_ASCENDER,  AF_BLUE_PROPERTY_LATIN_TOP        }
    { AF_BLUE_STRING_ARMENIAN_SMALL_TOP,       AF_BLUE_PROPERTY_LATIN_TOP      |
                                               AF_BLUE_PROPERTY_LATIN_X_HEIGHT   }
    { AF_BLUE_STRING_ARMENIAN_SMALL_BOTTOM,    0                                 }
    { AF_BLUE_STRING_ARMENIAN_SMALL_DESCENDER, 0                                 }
    { AF_BLUE_STRING_MAX,                      0                                 }

  AF_BLUE_STRINGSET_AVST
    { AF_BLUE_STRING_AVESTAN_TOP,    AF_BLUE_PROPERTY_LATIN_TOP }
    { AF_BLUE_STRING_AVESTAN_BOTTOM, 0                          }
    { AF_BLUE_STRING_MAX,            0                          }

  AF_BLUE_STRINGSET_BAMU
    { AF_BLUE_STRING_BAMUM_TOP,    AF_BLUE_PROPERTY_LATIN_TOP }
    { AF_BLUE_STRING_BAMUM_BOTTOM, 0                          }
    { AF_BLUE_STRING_MAX,          0                          }

  AF_BLUE_STRINGSET_BENG
    { AF_BLUE_STRING_BENGALI_TOP,  AF_BLUE_PROPERTY_LATIN_TOP        }
    { AF_BLUE_STRING_BENGALI_HEAD, AF_BLUE_PROPERTY_LATIN_TOP        }
    { AF_BLUE_STRING_BENGALI_BASE, AF_BLUE_PROPERTY_LATIN_TOP      |
                                   AF_BLUE_PROPERTY_LATIN_NEUTRAL  |
                                   AF_BLUE_PROPERTY_LATIN_X_HEIGHT   }
    { AF_BLUE_STRING_BENGALI_BASE, 0                                 }
    { AF_BLUE_STRING_MAX,          0                                 }

  AF_BLUE_STRINGSET_BUHD
    { AF_BLUE_STRING_BUHID_TOP,    AF_BLUE_PROPERTY_LATIN_TOP        }
    { AF_BLUE_STRING_BUHID_LARGE,  AF_BLUE_PROPERTY_LATIN_TOP        }
    { AF_BLUE_STRING_BUHID_SMALL,  AF_BLUE_PROPERTY_LATIN_TOP      |
                                   AF_BLUE_PROPERTY_LATIN_X_HEIGHT   }
    { AF_BLUE_STRING_BUHID_BOTTOM, 0                                 }
    { AF_BLUE_STRING_MAX,          0                                 }

  AF_BLUE_STRINGSET_CAKM
    { AF_BLUE_STRING_CHAKMA_TOP,       AF_BLUE_PROPERTY_LATIN_TOP }
    { AF_BLUE_STRING_CHAKMA_BOTTOM,    0                          }
    { AF_BLUE_STRING_CHAKMA_DESCENDER, 0                          }
    { AF_BLUE_STRING_MAX,              0                          }

  AF_BLUE_STRINGSET_CANS
    { AF_BLUE_STRING_CANADIAN_SYLLABICS_TOP,             AF_BLUE_PROPERTY_LATIN_TOP        }
    { AF_BLUE_STRING_CANADIAN_SYLLABICS_BOTTOM,          0                                 }
    { AF_BLUE_STRING_CANADIAN_SYLLABICS_SMALL_TOP,       AF_BLUE_PROPERTY_LATIN_TOP      |
                                                         AF_BLUE_PROPERTY_LATIN_X_HEIGHT   }
    { AF_BLUE_STRING_CANADIAN_SYLLABICS_SMALL_BOTTOM,    0                                 }
    { AF_BLUE_STRING_CANADIAN_SYLLABICS_SUPS_TOP,        AF_BLUE_PROPERTY_LATIN_TOP        }
    { AF_BLUE_STRING_CANADIAN_SYLLABICS_SUPS_BOTTOM,     0                                 }
    { AF_BLUE_STRING_MAX,                                0                                 }

  AF_BLUE_STRINGSET_CARI
    { AF_BLUE_STRING_CARIAN_TOP,    AF_BLUE_PROPERTY_LATIN_TOP }
    { AF_BLUE_STRING_CARIAN_BOTTOM, 0                          }
    { AF_BLUE_STRING_MAX,           0                          }

  AF_BLUE_STRINGSET_CHER
    { AF_BLUE_STRING_CHEROKEE_CAPITAL,         AF_BLUE_PROPERTY_LATIN_TOP        }
    { AF_BLUE_STRING_CHEROKEE_CAPITAL,         0                                 }
    { AF_BLUE_STRING_CHEROKEE_SMALL_ASCENDER,  AF_BLUE_PROPERTY_LATIN_TOP        }
    { AF_BLUE_STRING_CHEROKEE_SMALL,           AF_BLUE_PROPERTY_LATIN_TOP      |
                                               AF_BLUE_PROPERTY_LATIN_X_HEIGHT   }
    { AF_BLUE_STRING_CHEROKEE_SMALL,           0                                 }
    { AF_BLUE_STRING_CHEROKEE_SMALL_DESCENDER, 0                                 }
    { AF_BLUE_STRING_MAX,                      0                                 }

  AF_BLUE_STRINGSET_COPT
    { AF_BLUE_STRING_COPTIC_CAPITAL_TOP,    AF_BLUE_PROPERTY_LATIN_TOP        }
    { AF_BLUE_STRING_COPTIC_CAPITAL_BOTTOM, 0                                 }
    { AF_BLUE_STRING_COPTIC_SMALL_TOP,      AF_BLUE_PROPERTY_LATIN_TOP      |
                                            AF_BLUE_PROPERTY_LATIN_X_HEIGHT   }
    { AF_BLUE_STRING_COPTIC_SMALL_BOTTOM,   0                                 }
    { AF_BLUE_STRING_MAX,                   0                                 }

  AF_BLUE_STRINGSET_CPRT
    { AF_BLUE_STRING_CYPRIOT_TOP,    AF_BLUE_PROPERTY_LATIN_TOP }
    { AF_BLUE_STRING_CYPRIOT_BOTTOM, 0                          }
    { AF_BLUE_STRING_CYPRIOT_SMALL,  AF_BLUE_PROPERTY_LATIN_TOP }
    { AF_BLUE_STRING_CYPRIOT_SMALL,  0                          }
    { AF_BLUE_STRING_MAX,            0                          }

  AF_BLUE_STRINGSET_CYRL
    { AF_BLUE_STRING_CYRILLIC_CAPITAL_TOP,     AF_BLUE_PROPERTY_LATIN_TOP        }
    { AF_BLUE_STRING_CYRILLIC_CAPITAL_BOTTOM,  0                                 }
    { AF_BLUE_STRING_CYRILLIC_SMALL,           AF_BLUE_PROPERTY_LATIN_TOP      |
                                               AF_BLUE_PROPERTY_LATIN_X_HEIGHT   }
    { AF_BLUE_STRING_CYRILLIC_SMALL,           0                                 }
    { AF_BLUE_STRING_CYRILLIC_SMALL_DESCENDER, 0                                 }
    { AF_BLUE_STRING_MAX,                      0                                 }

  AF_BLUE_STRINGSET_DEVA
    { AF_BLUE_STRING_DEVANAGARI_TOP,    AF_BLUE_PROPERTY_LATIN_TOP        }
    { AF_BLUE_STRING_DEVANAGARI_HEAD,   AF_BLUE_PROPERTY_LATIN_TOP        }
    { AF_BLUE_STRING_DEVANAGARI_BASE,   AF_BLUE_PROPERTY_LATIN_TOP      |
                                        AF_BLUE_PROPERTY_LATIN_NEUTRAL  |
                                        AF_BLUE_PROPERTY_LATIN_X_HEIGHT   }
    { AF_BLUE_STRING_DEVANAGARI_BASE,   0                                 }
    { AF_BLUE_STRING_DEVANAGARI_BOTTOM, 0                                 }
    { AF_BLUE_STRING_MAX,               0                                 }

  AF_BLUE_STRINGSET_DSRT
    { AF_BLUE_STRING_DESERET_CAPITAL_TOP,    AF_BLUE_PROPERTY_LATIN_TOP        }
    { AF_BLUE_STRING_DESERET_CAPITAL_BOTTOM, 0                                 }
    { AF_BLUE_STRING_DESERET_SMALL_TOP,      AF_BLUE_PROPERTY_LATIN_TOP      |
                                             AF_BLUE_PROPERTY_LATIN_X_HEIGHT   }
    { AF_BLUE_STRING_DESERET_SMALL_BOTTOM,   0                                 }
    { AF_BLUE_STRING_MAX,                    0                                 }

  AF_BLUE_STRINGSET_ETHI
    { AF_BLUE_STRING_ETHIOPIC_TOP,    AF_BLUE_PROPERTY_LATIN_TOP }
    { AF_BLUE_STRING_ETHIOPIC_BOTTOM, 0                          }
    { AF_BLUE_STRING_MAX,             0                          }

  AF_BLUE_STRINGSET_GEOR
    { AF_BLUE_STRING_GEORGIAN_MKHEDRULI_TOP,       AF_BLUE_PROPERTY_LATIN_TOP      |
                                                   AF_BLUE_PROPERTY_LATIN_X_HEIGHT   }
    { AF_BLUE_STRING_GEORGIAN_MKHEDRULI_BOTTOM,    0                                 }
    { AF_BLUE_STRING_GEORGIAN_MKHEDRULI_ASCENDER,  AF_BLUE_PROPERTY_LATIN_TOP        }
    { AF_BLUE_STRING_GEORGIAN_MKHEDRULI_DESCENDER, 0                                 }
    { AF_BLUE_STRING_GEORGIAN_MTAVRULI_TOP,        AF_BLUE_PROPERTY_LATIN_TOP        }
    { AF_BLUE_STRING_GEORGIAN_MTAVRULI_BOTTOM,     0                                 }
    { AF_BLUE_STRING_MAX,                          0                                 }

  AF_BLUE_STRINGSET_GEOK
    { AF_BLUE_STRING_GEORGIAN_ASOMTAVRULI_TOP,    AF_BLUE_PROPERTY_LATIN_TOP        }
    { AF_BLUE_STRING_GEORGIAN_ASOMTAVRULI_BOTTOM, 0                                 }
    { AF_BLUE_STRING_GEORGIAN_NUSKHURI_TOP,       AF_BLUE_PROPERTY_LATIN_TOP      |
                                                  AF_BLUE_PROPERTY_LATIN_X_HEIGHT   }
    { AF_BLUE_STRING_GEORGIAN_NUSKHURI_BOTTOM,    0                                 }
    { AF_BLUE_STRING_GEORGIAN_NUSKHURI_ASCENDER,  AF_BLUE_PROPERTY_LATIN_TOP        }
    { AF_BLUE_STRING_GEORGIAN_NUSKHURI_DESCENDER, 0                                 }
    { AF_BLUE_STRING_MAX,                         0                                 }

  AF_BLUE_STRINGSET_GLAG
    { AF_BLUE_STRING_GLAGOLITIC_CAPITAL_TOP,    AF_BLUE_PROPERTY_LATIN_TOP        }
    { AF_BLUE_STRING_GLAGOLITIC_CAPITAL_BOTTOM, 0                                 }
    { AF_BLUE_STRING_GLAGOLITIC_SMALL_TOP,      AF_BLUE_PROPERTY_LATIN_TOP      |
                                                AF_BLUE_PROPERTY_LATIN_X_HEIGHT   }
    { AF_BLUE_STRING_GLAGOLITIC_SMALL_BOTTOM,   0                                 }
    { AF_BLUE_STRING_MAX,                       0                                 }

  AF_BLUE_STRINGSET_GOTH
    { AF_BLUE_STRING_GOTHIC_TOP,    AF_BLUE_PROPERTY_LATIN_TOP }
    { AF_BLUE_STRING_GOTHIC_BOTTOM, 0                          }
    { AF_BLUE_STRING_MAX,           0                          }

  AF_BLUE_STRINGSET_GREK
    { AF_BLUE_STRING_GREEK_CAPITAL_TOP,     AF_BLUE_PROPERTY_LATIN_TOP        }
    { AF_BLUE_STRING_GREEK_CAPITAL_BOTTOM,  0                                 }
    { AF_BLUE_STRING_GREEK_SMALL_BETA_TOP,  AF_BLUE_PROPERTY_LATIN_TOP        }
    { AF_BLUE_STRING_GREEK_SMALL,           AF_BLUE_PROPERTY_LATIN_TOP      |
                                            AF_BLUE_PROPERTY_LATIN_X_HEIGHT   }
    { AF_BLUE_STRING_GREEK_SMALL,           0                                 }
    { AF_BLUE_STRING_GREEK_SMALL_DESCENDER, 0                                 }
    { AF_BLUE_STRING_MAX,                   0                                 }

  AF_BLUE_STRINGSET_GUJR
    { AF_BLUE_STRING_GUJARATI_TOP,       AF_BLUE_PROPERTY_LATIN_TOP      |
                                         AF_BLUE_PROPERTY_LATIN_X_HEIGHT   }
    { AF_BLUE_STRING_GUJARATI_BOTTOM,    0                                 }
    { AF_BLUE_STRING_GUJARATI_ASCENDER,  AF_BLUE_PROPERTY_LATIN_TOP        }
    { AF_BLUE_STRING_GUJARATI_DESCENDER, 0                                 }
    { AF_BLUE_STRING_GUJARATI_DIGIT_TOP, AF_BLUE_PROPERTY_LATIN_TOP        }
    { AF_BLUE_STRING_MAX,                0                                 }

  AF_BLUE_STRINGSET_GURU
    { AF_BLUE_STRING_GURMUKHI_TOP,       AF_BLUE_PROPERTY_LATIN_TOP        }
    { AF_BLUE_STRING_GURMUKHI_HEAD,      AF_BLUE_PROPERTY_LATIN_TOP        }
    { AF_BLUE_STRING_GURMUKHI_BASE,      AF_BLUE_PROPERTY_LATIN_TOP      |
                                         AF_BLUE_PROPERTY_LATIN_NEUTRAL  |
                                         AF_BLUE_PROPERTY_LATIN_X_HEIGHT   }
    { AF_BLUE_STRING_GURMUKHI_BOTTOM,    0                                 }
    { AF_BLUE_STRING_GURMUKHI_DIGIT_TOP, AF_BLUE_PROPERTY_LATIN_TOP        }
    { AF_BLUE_STRING_MAX,                0                                 }

  AF_BLUE_STRINGSET_HEBR
    { AF_BLUE_STRING_HEBREW_TOP,       AF_BLUE_PROPERTY_LATIN_TOP  |
                                       AF_BLUE_PROPERTY_LATIN_LONG   }
    { AF_BLUE_STRING_HEBREW_BOTTOM,    0                             }
    { AF_BLUE_STRING_HEBREW_DESCENDER, 0                             }
    { AF_BLUE_STRING_MAX,              0                             }

  AF_BLUE_STRINGSET_KALI
    { AF_BLUE_STRING_KAYAH_LI_TOP,             AF_BLUE_PROPERTY_LATIN_TOP      |
                                               AF_BLUE_PROPERTY_LATIN_X_HEIGHT   }
    { AF_BLUE_STRING_KAYAH_LI_BOTTOM,          0                                 }
    { AF_BLUE_STRING_KAYAH_LI_ASCENDER,        AF_BLUE_PROPERTY_LATIN_TOP        }
    { AF_BLUE_STRING_KAYAH_LI_DESCENDER,       0                                 }
    { AF_BLUE_STRING_KAYAH_LI_LARGE_DESCENDER, 0                                 }
    { AF_BLUE_STRING_MAX,                      0                                 }

  AF_BLUE_STRINGSET_KHMR
    { AF_BLUE_STRING_KHMER_TOP,             AF_BLUE_PROPERTY_LATIN_TOP      |
                                            AF_BLUE_PROPERTY_LATIN_X_HEIGHT   }
    { AF_BLUE_STRING_KHMER_SUBSCRIPT_TOP,   AF_BLUE_PROPERTY_LATIN_SUB_TOP    }
    { AF_BLUE_STRING_KHMER_BOTTOM,          0                                 }
    { AF_BLUE_STRING_KHMER_DESCENDER,       0                                 }
    { AF_BLUE_STRING_KHMER_LARGE_DESCENDER, 0                                 }
    { AF_BLUE_STRING_MAX,                   0                                 }

  AF_BLUE_STRINGSET_KHMS
    { AF_BLUE_STRING_KHMER_SYMBOLS_WAXING_TOP,    AF_BLUE_PROPERTY_LATIN_TOP      |
                                                  AF_BLUE_PROPERTY_LATIN_X_HEIGHT   }
    { AF_BLUE_STRING_KHMER_SYMBOLS_WANING_BOTTOM, 0                                 }
    { AF_BLUE_STRING_MAX,                         0                                 }

  AF_BLUE_STRINGSET_KNDA
    { AF_BLUE_STRING_KANNADA_TOP,    AF_BLUE_PROPERTY_LATIN_TOP }
    { AF_BLUE_STRING_KANNADA_BOTTOM, 0                          }
    { AF_BLUE_STRING_MAX,            0                          }

  AF_BLUE_STRINGSET_LAO
    { AF_BLUE_STRING_LAO_TOP,            AF_BLUE_PROPERTY_LATIN_TOP      |
                                         AF_BLUE_PROPERTY_LATIN_X_HEIGHT   }
    { AF_BLUE_STRING_LAO_BOTTOM,         0                                 }
    { AF_BLUE_STRING_LAO_ASCENDER,       AF_BLUE_PROPERTY_LATIN_TOP        }
    { AF_BLUE_STRING_LAO_LARGE_ASCENDER, AF_BLUE_PROPERTY_LATIN_TOP        }
    { AF_BLUE_STRING_LAO_DESCENDER,      0                                 }
    { AF_BLUE_STRING_MAX,                0                                 }

  AF_BLUE_STRINGSET_LATN
    { AF_BLUE_STRING_LATIN_CAPITAL_TOP,     AF_BLUE_PROPERTY_LATIN_TOP        }
    { AF_BLUE_STRING_LATIN_CAPITAL_BOTTOM,  0                                 }
    { AF_BLUE_STRING_LATIN_SMALL_F_TOP,     AF_BLUE_PROPERTY_LATIN_TOP        }
    { AF_BLUE_STRING_LATIN_SMALL_TOP,       AF_BLUE_PROPERTY_LATIN_TOP      |
                                            AF_BLUE_PROPERTY_LATIN_X_HEIGHT   }
    { AF_BLUE_STRING_LATIN_SMALL_BOTTOM,    0                                 }
    { AF_BLUE_STRING_LATIN_SMALL_DESCENDER, 0                                 }
    { AF_BLUE_STRING_MAX,                   0                                 }

  AF_BLUE_STRINGSET_LATB
    { AF_BLUE_STRING_LATIN_SUBS_CAPITAL_TOP,     AF_BLUE_PROPERTY_LATIN_TOP        }
    { AF_BLUE_STRING_LATIN_SUBS_CAPITAL_BOTTOM,  0                                 }
    { AF_BLUE_STRING_LATIN_SUBS_SMALL_F_TOP,     AF_BLUE_PROPERTY_LATIN_TOP        }
    { AF_BLUE_STRING_LATIN_SUBS_SMALL,           AF_BLUE_PROPERTY_LATIN_TOP      |
                                                 AF_BLUE_PROPERTY_LATIN_X_HEIGHT   }
    { AF_BLUE_STRING_LATIN_SUBS_SMALL,           0                                 }
    { AF_BLUE_STRING_LATIN_SUBS_SMALL_DESCENDER, 0                                 }
    { AF_BLUE_STRING_MAX,                        0                                 }

  AF_BLUE_STRINGSET_LATP
    { AF_BLUE_STRING_LATIN_SUPS_CAPITAL_TOP,     AF_BLUE_PROPERTY_LATIN_TOP        }
    { AF_BLUE_STRING_LATIN_SUPS_CAPITAL_BOTTOM,  0                                 }
    { AF_BLUE_STRING_LATIN_SUPS_SMALL_F_TOP,     AF_BLUE_PROPERTY_LATIN_TOP        }
    { AF_BLUE_STRING_LATIN_SUPS_SMALL,           AF_BLUE_PROPERTY_LATIN_TOP      |
                                                 AF_BLUE_PROPERTY_LATIN_X_HEIGHT   }
    { AF_BLUE_STRING_LATIN_SUPS_SMALL,           0                                 }
    { AF_BLUE_STRING_LATIN_SUPS_SMALL_DESCENDER, 0                                 }
    { AF_BLUE_STRING_MAX,                        0                                 }

  AF_BLUE_STRINGSET_LISU
    { AF_BLUE_STRING_LISU_TOP,    AF_BLUE_PROPERTY_LATIN_TOP }
    { AF_BLUE_STRING_LISU_BOTTOM, 0                          }
    { AF_BLUE_STRING_MAX,         0                          }

  AF_BLUE_STRINGSET_MLYM
    { AF_BLUE_STRING_MALAYALAM_TOP,    AF_BLUE_PROPERTY_LATIN_TOP }
    { AF_BLUE_STRING_MALAYALAM_BOTTOM, 0                          }
    { AF_BLUE_STRING_MAX,              0                          }

  AF_BLUE_STRINGSET_MYMR
    { AF_BLUE_STRING_MYANMAR_TOP,       AF_BLUE_PROPERTY_LATIN_TOP      |
                                        AF_BLUE_PROPERTY_LATIN_X_HEIGHT   }
    { AF_BLUE_STRING_MYANMAR_BOTTOM,    0                                 }
    { AF_BLUE_STRING_MYANMAR_ASCENDER,  AF_BLUE_PROPERTY_LATIN_TOP        }
    { AF_BLUE_STRING_MYANMAR_DESCENDER, 0                                 }
    { AF_BLUE_STRING_MAX,               0                                 }

  AF_BLUE_STRINGSET_NKOO
    { AF_BLUE_STRING_NKO_TOP,          AF_BLUE_PROPERTY_LATIN_TOP        }
    { AF_BLUE_STRING_NKO_BOTTOM,       0                                 }
    { AF_BLUE_STRING_NKO_SMALL_TOP,    AF_BLUE_PROPERTY_LATIN_TOP      |
                                       AF_BLUE_PROPERTY_LATIN_X_HEIGHT   }
    { AF_BLUE_STRING_NKO_SMALL_BOTTOM, 0                                 }
    { AF_BLUE_STRING_MAX,              0                                 }

  AF_BLUE_STRINGSET_NONE
    { AF_BLUE_STRING_MAX, 0 }

  AF_BLUE_STRINGSET_OLCK
    { AF_BLUE_STRING_OL_CHIKI, AF_BLUE_PROPERTY_LATIN_TOP }
    { AF_BLUE_STRING_OL_CHIKI, 0                          }
    { AF_BLUE_STRING_MAX,      0                          }

  AF_BLUE_STRINGSET_ORKH
    { AF_BLUE_STRING_OLD_TURKIC_TOP,    AF_BLUE_PROPERTY_LATIN_TOP }
    { AF_BLUE_STRING_OLD_TURKIC_BOTTOM, 0                          }
    { AF_BLUE_STRING_MAX,               0                          }

  AF_BLUE_STRINGSET_OSGE
    { AF_BLUE_STRING_OSAGE_CAPITAL_TOP,       AF_BLUE_PROPERTY_LATIN_TOP       }
    { AF_BLUE_STRING_OSAGE_CAPITAL_BOTTOM,    0                                }
    { AF_BLUE_STRING_OSAGE_CAPITAL_DESCENDER, 0                                }
    { AF_BLUE_STRING_OSAGE_SMALL_TOP,         AF_BLUE_PROPERTY_LATIN_TOP     |
                                              AF_BLUE_PROPERTY_LATIN_X_HEIGHT  }
    { AF_BLUE_STRING_OSAGE_SMALL_BOTTOM,      0                                }
    { AF_BLUE_STRING_OSAGE_SMALL_ASCENDER,    AF_BLUE_PROPERTY_LATIN_TOP       }
    { AF_BLUE_STRING_OSAGE_SMALL_DESCENDER,   0                                }
    { AF_BLUE_STRING_MAX,                     0                                }

  AF_BLUE_STRINGSET_OSMA
    { AF_BLUE_STRING_OSMANYA_TOP,    AF_BLUE_PROPERTY_LATIN_TOP }
    { AF_BLUE_STRING_OSMANYA_BOTTOM, 0                          }
    { AF_BLUE_STRING_MAX,            0                          }

  AF_BLUE_STRINGSET_SAUR
    { AF_BLUE_STRING_SAURASHTRA_TOP,    AF_BLUE_PROPERTY_LATIN_TOP }
    { AF_BLUE_STRING_SAURASHTRA_BOTTOM, 0                          }
    { AF_BLUE_STRING_MAX,               0                          }

  AF_BLUE_STRINGSET_SHAW
    { AF_BLUE_STRING_SHAVIAN_TOP,          AF_BLUE_PROPERTY_LATIN_TOP        }
    { AF_BLUE_STRING_SHAVIAN_BOTTOM,       0                                 }
    { AF_BLUE_STRING_SHAVIAN_DESCENDER,    0                                 }
    { AF_BLUE_STRING_SHAVIAN_SMALL_TOP,    AF_BLUE_PROPERTY_LATIN_TOP      |
                                           AF_BLUE_PROPERTY_LATIN_X_HEIGHT   }
    { AF_BLUE_STRING_SHAVIAN_SMALL_BOTTOM, 0                                 }
    { AF_BLUE_STRING_MAX,                  0                                 }

  AF_BLUE_STRINGSET_SINH
    { AF_BLUE_STRING_SINHALA_TOP,       AF_BLUE_PROPERTY_LATIN_TOP }
    { AF_BLUE_STRING_SINHALA_BOTTOM,    0                          }
    { AF_BLUE_STRING_SINHALA_DESCENDER, 0                          }
    { AF_BLUE_STRING_MAX,               0                          }

  AF_BLUE_STRINGSET_SUND
    { AF_BLUE_STRING_SUNDANESE_TOP,       AF_BLUE_PROPERTY_LATIN_TOP }
    { AF_BLUE_STRING_SUNDANESE_BOTTOM,    0                          }
    { AF_BLUE_STRING_SUNDANESE_DESCENDER, 0                          }
    { AF_BLUE_STRING_MAX,                 0                          }

  AF_BLUE_STRINGSET_TAML
    { AF_BLUE_STRING_TAMIL_TOP,    AF_BLUE_PROPERTY_LATIN_TOP }
    { AF_BLUE_STRING_TAMIL_BOTTOM, 0                          }
    { AF_BLUE_STRING_MAX,          0                          }

  AF_BLUE_STRINGSET_TAVT
    { AF_BLUE_STRING_TAI_VIET_TOP,    AF_BLUE_PROPERTY_LATIN_TOP }
    { AF_BLUE_STRING_TAI_VIET_BOTTOM, 0                          }
    { AF_BLUE_STRING_MAX,             0                          }

  AF_BLUE_STRINGSET_TELU
    { AF_BLUE_STRING_TELUGU_TOP,    AF_BLUE_PROPERTY_LATIN_TOP }
    { AF_BLUE_STRING_TELUGU_BOTTOM, 0                          }
    { AF_BLUE_STRING_MAX,           0                          }

  AF_BLUE_STRINGSET_TFNG
    { AF_BLUE_STRING_TIFINAGH, AF_BLUE_PROPERTY_LATIN_TOP }
    { AF_BLUE_STRING_TIFINAGH, 0                          }
    { AF_BLUE_STRING_MAX,      0                          }

  AF_BLUE_STRINGSET_THAI
    { AF_BLUE_STRING_THAI_TOP,             AF_BLUE_PROPERTY_LATIN_TOP      |
                                           AF_BLUE_PROPERTY_LATIN_X_HEIGHT   }
    { AF_BLUE_STRING_THAI_BOTTOM,          0                                 }
    { AF_BLUE_STRING_THAI_ASCENDER,        AF_BLUE_PROPERTY_LATIN_TOP        }
    { AF_BLUE_STRING_THAI_LARGE_ASCENDER,  AF_BLUE_PROPERTY_LATIN_TOP        }
    { AF_BLUE_STRING_THAI_DESCENDER,       0                                 }
    { AF_BLUE_STRING_THAI_LARGE_DESCENDER, 0                                 }
    { AF_BLUE_STRING_THAI_DIGIT_TOP,       0                                 }
    { AF_BLUE_STRING_MAX,                  0                                 }

  AF_BLUE_STRINGSET_VAII
    { AF_BLUE_STRING_VAI_TOP,    AF_BLUE_PROPERTY_LATIN_TOP }
    { AF_BLUE_STRING_VAI_BOTTOM, 0                          }
    { AF_BLUE_STRING_MAX,        0                          }

#ifdef AF_CONFIG_OPTION_CJK

  AF_BLUE_STRINGSET_HANI
    { AF_BLUE_STRING_CJK_TOP,    AF_BLUE_PROPERTY_CJK_TOP     }
    { AF_BLUE_STRING_CJK_BOTTOM, 0                            }
#ifdef AF_CONFIG_OPTION_CJK_BLUE_HANI_VERT
    { AF_BLUE_STRING_CJK_LEFT,   AF_BLUE_PROPERTY_CJK_HORIZ   }
    { AF_BLUE_STRING_CJK_RIGHT,  AF_BLUE_PROPERTY_CJK_HORIZ |
                                 AF_BLUE_PROPERTY_CJK_RIGHT   }
#endif /* AF_CONFIG_OPTION_CJK_BLUE_HANI_VERT */
    { AF_BLUE_STRING_MAX,        0                            }

#endif /* AF_CONFIG_OPTION_CJK                */


// END

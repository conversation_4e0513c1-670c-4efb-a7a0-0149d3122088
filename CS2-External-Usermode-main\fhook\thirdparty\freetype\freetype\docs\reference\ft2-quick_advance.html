<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"https://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>FreeType-2.9.1 API Reference</title>
<style type="text/css">
  a:link { color: #0000EF; }
  a:visited { color: #51188E; }
  a:hover { color: #FF0000; }

  body { font-family: Verdana, Geneva, Arial, Helvetica, serif;
         color: #000000;
         background: #FFFFFF;
         width: 87%;
         margin: auto; }

  div.section { width: 75%;
                margin: auto; }
  div.section hr { margin: 4ex 0 1ex 0; }
  div.section h4 { background-color: #EEEEFF;
                   font-size: medium;
                   font-style: oblique;
                   font-weight: bold;
                   margin: 3ex 0 1.5ex 9%;
                   padding: 0.3ex 0 0.3ex 1%; }
  div.section p { margin: 1.5ex 0 1.5ex 10%; }
  div.section pre { margin: 3ex 0 3ex 9%;
                    background-color: #D6E8FF;
                    padding: 2ex 0 2ex 1%; }
  div.section table.fields { width: 90%;
                             margin: 1.5ex 0 1.5ex 10%; }
  div.section table.toc { width: 95%;
                          margin: 1.5ex 0 1.5ex 5%; }
  div.timestamp { text-align: center;
                  font-size: 69%;
                  margin: 1.5ex 0 1.5ex 0; }

  h1 { text-align: center; }
  h3 { font-size: medium;
       margin: 4ex 0 1.5ex 0; }

  p { text-align: justify; }

  pre.colored { color: blue; }

  span.keyword { font-family: monospace;
                 text-align: left;
                 white-space: pre;
                 color: darkblue; }

  table.fields td.val { font-weight: bold;
                        text-align: right;
                        width: 30%;
                        vertical-align: baseline;
                        padding: 1ex 1em 1ex 0; }
  table.fields td.desc { vertical-align: baseline;
                         padding: 1ex 0 1ex 1em; }
  table.fields td.desc p:first-child { margin: 0; }
  table.fields td.desc p { margin: 1.5ex 0 0 0; }
  table.index { margin: 6ex auto 6ex auto;
                border: 0;
                border-collapse: separate;
                border-spacing: 1em 0.3ex; }
  table.index tr { padding: 0; }
  table.index td { padding: 0; }
  table.index-toc-link { width: 100%;
                         border: 0;
                         border-spacing: 0;
                         margin: 1ex 0 1ex 0; }
  table.index-toc-link td.left { padding: 0 0.5em 0 0.5em;
                                 font-size: 83%;
                                 text-align: left; }
  table.index-toc-link td.middle { padding: 0 0.5em 0 0.5em;
                                   font-size: 83%;
                                   text-align: center; }
  table.index-toc-link td.right { padding: 0 0.5em 0 0.5em;
                                  font-size: 83%;
                                  text-align: right; }
  table.synopsis { margin: 6ex auto 6ex auto;
                   border: 0;
                   border-collapse: separate;
                   border-spacing: 2em 0.6ex; }
  table.synopsis tr { padding: 0; }
  table.synopsis td { padding: 0; }
  table.toc td.link { width: 30%;
                      text-align: right;
                      vertical-align: baseline;
                      padding: 1ex 1em 1ex 0; }
  table.toc td.desc { vertical-align: baseline;
                      padding: 1ex 0 1ex 1em;
                      text-align: left; }
  table.toc td.desc p:first-child { margin: 0;
                                    text-align: left; }
  table.toc td.desc p { margin: 1.5ex 0 0 0;
                        text-align: left; }

</style>
</head>
<body>

<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table>
<h1>FreeType-2.9.1 API Reference</h1>

<h1 id="quick_advance">Quick retrieval of advance values</h1>
<h2>Synopsis</h2>
<table class="synopsis">
<tr><td><a href="#FT_Get_Advance">FT_Get_Advance</a></td><td>&nbsp;</td></tr>
<tr><td><a href="#FT_Get_Advances">FT_Get_Advances</a></td><td><a href="#FT_ADVANCE_FLAG_FAST_ONLY">FT_ADVANCE_FLAG_FAST_ONLY</a></td></tr>
</table>


<p>This section contains functions to quickly extract advance values without handling glyph outlines, if possible.</p>

<div class="section">
<h3 id="FT_Get_Advance">FT_Get_Advance</h3>
<p>Defined in FT_ADVANCES_H (freetype/ftadvanc.h).</p>
<pre>
  FT_EXPORT( <a href="ft2-basic_types.html#FT_Error">FT_Error</a> )
  <b>FT_Get_Advance</b>( <a href="ft2-base_interface.html#FT_Face">FT_Face</a>    face,
                  <a href="ft2-basic_types.html#FT_UInt">FT_UInt</a>    gindex,
                  <a href="ft2-basic_types.html#FT_Int32">FT_Int32</a>   load_flags,
                  <a href="ft2-basic_types.html#FT_Fixed">FT_Fixed</a>  *padvance );
</pre>

<p>Retrieve the advance value of a given glyph outline in an <a href="ft2-base_interface.html#FT_Face">FT_Face</a>.</p>

<h4>input</h4>
<table class="fields">
<tr><td class="val" id="face">face</td><td class="desc">
<p>The source <a href="ft2-base_interface.html#FT_Face">FT_Face</a> handle.</p>
</td></tr>
<tr><td class="val" id="gindex">gindex</td><td class="desc">
<p>The glyph index.</p>
</td></tr>
<tr><td class="val" id="load_flags">load_flags</td><td class="desc">
<p>A set of bit flags similar to those used when calling <a href="ft2-base_interface.html#FT_Load_Glyph">FT_Load_Glyph</a>, used to determine what kind of advances you need.</p>
</td></tr>
</table>

<h4>output</h4>
<table class="fields">
<tr><td class="val" id="padvance">padvance</td><td class="desc">
<p>The advance value. If scaling is performed (based on the value of &lsquo;load_flags&rsquo;), the advance value is in 16.16 format. Otherwise, it is in font units.</p>
<p>If <a href="ft2-base_interface.html#FT_LOAD_XXX">FT_LOAD_VERTICAL_LAYOUT</a> is set, this is the vertical advance corresponding to a vertical layout. Otherwise, it is the horizontal advance in a horizontal layout.</p>
</td></tr>
</table>

<h4>return</h4>
<p>FreeType error code. 0 means success.</p>

<h4>note</h4>
<p>This function may fail if you use <a href="ft2-quick_advance.html#FT_ADVANCE_FLAG_FAST_ONLY">FT_ADVANCE_FLAG_FAST_ONLY</a> and if the corresponding font backend doesn't have a quick way to retrieve the advances.</p>
<p>A scaled advance is returned in 16.16 format but isn't transformed by the affine transformation specified by <a href="ft2-base_interface.html#FT_Set_Transform">FT_Set_Transform</a>.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_Get_Advances">FT_Get_Advances</h3>
<p>Defined in FT_ADVANCES_H (freetype/ftadvanc.h).</p>
<pre>
  FT_EXPORT( <a href="ft2-basic_types.html#FT_Error">FT_Error</a> )
  <b>FT_Get_Advances</b>( <a href="ft2-base_interface.html#FT_Face">FT_Face</a>    face,
                   <a href="ft2-basic_types.html#FT_UInt">FT_UInt</a>    start,
                   <a href="ft2-basic_types.html#FT_UInt">FT_UInt</a>    count,
                   <a href="ft2-basic_types.html#FT_Int32">FT_Int32</a>   load_flags,
                   <a href="ft2-basic_types.html#FT_Fixed">FT_Fixed</a>  *padvances );
</pre>

<p>Retrieve the advance values of several glyph outlines in an <a href="ft2-base_interface.html#FT_Face">FT_Face</a>.</p>

<h4>input</h4>
<table class="fields">
<tr><td class="val" id="face">face</td><td class="desc">
<p>The source <a href="ft2-base_interface.html#FT_Face">FT_Face</a> handle.</p>
</td></tr>
<tr><td class="val" id="start">start</td><td class="desc">
<p>The first glyph index.</p>
</td></tr>
<tr><td class="val" id="count">count</td><td class="desc">
<p>The number of advance values you want to retrieve.</p>
</td></tr>
<tr><td class="val" id="load_flags">load_flags</td><td class="desc">
<p>A set of bit flags similar to those used when calling <a href="ft2-base_interface.html#FT_Load_Glyph">FT_Load_Glyph</a>.</p>
</td></tr>
</table>

<h4>output</h4>
<table class="fields">
<tr><td class="val" id="padvance">padvance</td><td class="desc">
<p>The advance values. This array, to be provided by the caller, must contain at least &lsquo;count&rsquo; elements.</p>
<p>If scaling is performed (based on the value of &lsquo;load_flags&rsquo;), the advance values are in 16.16 format. Otherwise, they are in font units.</p>
<p>If <a href="ft2-base_interface.html#FT_LOAD_XXX">FT_LOAD_VERTICAL_LAYOUT</a> is set, these are the vertical advances corresponding to a vertical layout. Otherwise, they are the horizontal advances in a horizontal layout.</p>
</td></tr>
</table>

<h4>return</h4>
<p>FreeType error code. 0 means success.</p>

<h4>note</h4>
<p>This function may fail if you use <a href="ft2-quick_advance.html#FT_ADVANCE_FLAG_FAST_ONLY">FT_ADVANCE_FLAG_FAST_ONLY</a> and if the corresponding font backend doesn't have a quick way to retrieve the advances.</p>
<p>Scaled advances are returned in 16.16 format but aren't transformed by the affine transformation specified by <a href="ft2-base_interface.html#FT_Set_Transform">FT_Set_Transform</a>.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_ADVANCE_FLAG_FAST_ONLY">FT_ADVANCE_FLAG_FAST_ONLY</h3>
<p>Defined in FT_ADVANCES_H (freetype/ftadvanc.h).</p>
<pre>
#define <b>FT_ADVANCE_FLAG_FAST_ONLY</b>  0x20000000L
</pre>

<p>A bit-flag to be OR-ed with the &lsquo;flags&rsquo; parameter of the <a href="ft2-quick_advance.html#FT_Get_Advance">FT_Get_Advance</a> and <a href="ft2-quick_advance.html#FT_Get_Advances">FT_Get_Advances</a> functions.</p>
<p>If set, it indicates that you want these functions to fail if the corresponding hinting mode or font driver doesn't allow for very quick advance computation.</p>
<p>Typically, glyphs that are either unscaled, unhinted, bitmapped, or light-hinted can have their advance width computed very quickly.</p>
<p>Normal and bytecode hinted modes that require loading, scaling, and hinting of the glyph outline, are extremely slow by comparison.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

</body>
</html>

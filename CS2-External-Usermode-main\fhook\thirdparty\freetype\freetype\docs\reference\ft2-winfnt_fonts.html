<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"https://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>FreeType-2.9.1 API Reference</title>
<style type="text/css">
  a:link { color: #0000EF; }
  a:visited { color: #51188E; }
  a:hover { color: #FF0000; }

  body { font-family: Verdana, Geneva, Arial, Helvetica, serif;
         color: #000000;
         background: #FFFFFF;
         width: 87%;
         margin: auto; }

  div.section { width: 75%;
                margin: auto; }
  div.section hr { margin: 4ex 0 1ex 0; }
  div.section h4 { background-color: #EEEEFF;
                   font-size: medium;
                   font-style: oblique;
                   font-weight: bold;
                   margin: 3ex 0 1.5ex 9%;
                   padding: 0.3ex 0 0.3ex 1%; }
  div.section p { margin: 1.5ex 0 1.5ex 10%; }
  div.section pre { margin: 3ex 0 3ex 9%;
                    background-color: #D6E8FF;
                    padding: 2ex 0 2ex 1%; }
  div.section table.fields { width: 90%;
                             margin: 1.5ex 0 1.5ex 10%; }
  div.section table.toc { width: 95%;
                          margin: 1.5ex 0 1.5ex 5%; }
  div.timestamp { text-align: center;
                  font-size: 69%;
                  margin: 1.5ex 0 1.5ex 0; }

  h1 { text-align: center; }
  h3 { font-size: medium;
       margin: 4ex 0 1.5ex 0; }

  p { text-align: justify; }

  pre.colored { color: blue; }

  span.keyword { font-family: monospace;
                 text-align: left;
                 white-space: pre;
                 color: darkblue; }

  table.fields td.val { font-weight: bold;
                        text-align: right;
                        width: 30%;
                        vertical-align: baseline;
                        padding: 1ex 1em 1ex 0; }
  table.fields td.desc { vertical-align: baseline;
                         padding: 1ex 0 1ex 1em; }
  table.fields td.desc p:first-child { margin: 0; }
  table.fields td.desc p { margin: 1.5ex 0 0 0; }
  table.index { margin: 6ex auto 6ex auto;
                border: 0;
                border-collapse: separate;
                border-spacing: 1em 0.3ex; }
  table.index tr { padding: 0; }
  table.index td { padding: 0; }
  table.index-toc-link { width: 100%;
                         border: 0;
                         border-spacing: 0;
                         margin: 1ex 0 1ex 0; }
  table.index-toc-link td.left { padding: 0 0.5em 0 0.5em;
                                 font-size: 83%;
                                 text-align: left; }
  table.index-toc-link td.middle { padding: 0 0.5em 0 0.5em;
                                   font-size: 83%;
                                   text-align: center; }
  table.index-toc-link td.right { padding: 0 0.5em 0 0.5em;
                                  font-size: 83%;
                                  text-align: right; }
  table.synopsis { margin: 6ex auto 6ex auto;
                   border: 0;
                   border-collapse: separate;
                   border-spacing: 2em 0.6ex; }
  table.synopsis tr { padding: 0; }
  table.synopsis td { padding: 0; }
  table.toc td.link { width: 30%;
                      text-align: right;
                      vertical-align: baseline;
                      padding: 1ex 1em 1ex 0; }
  table.toc td.desc { vertical-align: baseline;
                      padding: 1ex 0 1ex 1em;
                      text-align: left; }
  table.toc td.desc p:first-child { margin: 0;
                                    text-align: left; }
  table.toc td.desc p { margin: 1.5ex 0 0 0;
                        text-align: left; }

</style>
</head>
<body>

<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table>
<h1>FreeType-2.9.1 API Reference</h1>

<h1 id="winfnt_fonts">Window FNT Files</h1>
<h2>Synopsis</h2>
<table class="synopsis">
<tr><td><a href="#FT_WinFNT_ID_XXX">FT_WinFNT_ID_XXX</a></td><td><a href="#FT_WinFNT_Header">FT_WinFNT_Header</a></td><td></td></tr>
<tr><td><a href="#FT_WinFNT_HeaderRec">FT_WinFNT_HeaderRec</a></td><td><a href="#FT_Get_WinFNT_Header">FT_Get_WinFNT_Header</a></td><td></td></tr>
</table>


<p>This section contains the declaration of Windows FNT specific functions.</p>

<div class="section">
<h3 id="FT_WinFNT_ID_XXX">FT_WinFNT_ID_XXX</h3>
<p>Defined in FT_WINFONTS_H (freetype/ftwinfnt.h).</p>
<pre>
#define <a href="ft2-winfnt_fonts.html#FT_WinFNT_ID_CP1252">FT_WinFNT_ID_CP1252</a>    0
#define <a href="ft2-winfnt_fonts.html#FT_WinFNT_ID_DEFAULT">FT_WinFNT_ID_DEFAULT</a>   1
#define <a href="ft2-winfnt_fonts.html#FT_WinFNT_ID_SYMBOL">FT_WinFNT_ID_SYMBOL</a>    2
#define <a href="ft2-winfnt_fonts.html#FT_WinFNT_ID_MAC">FT_WinFNT_ID_MAC</a>      77
#define <a href="ft2-winfnt_fonts.html#FT_WinFNT_ID_CP932">FT_WinFNT_ID_CP932</a>   128
#define <a href="ft2-winfnt_fonts.html#FT_WinFNT_ID_CP949">FT_WinFNT_ID_CP949</a>   129
#define <a href="ft2-winfnt_fonts.html#FT_WinFNT_ID_CP1361">FT_WinFNT_ID_CP1361</a>  130
#define <a href="ft2-winfnt_fonts.html#FT_WinFNT_ID_CP936">FT_WinFNT_ID_CP936</a>   134
#define <a href="ft2-winfnt_fonts.html#FT_WinFNT_ID_CP950">FT_WinFNT_ID_CP950</a>   136
#define <a href="ft2-winfnt_fonts.html#FT_WinFNT_ID_CP1253">FT_WinFNT_ID_CP1253</a>  161
#define <a href="ft2-winfnt_fonts.html#FT_WinFNT_ID_CP1254">FT_WinFNT_ID_CP1254</a>  162
#define <a href="ft2-winfnt_fonts.html#FT_WinFNT_ID_CP1258">FT_WinFNT_ID_CP1258</a>  163
#define <a href="ft2-winfnt_fonts.html#FT_WinFNT_ID_CP1255">FT_WinFNT_ID_CP1255</a>  177
#define <a href="ft2-winfnt_fonts.html#FT_WinFNT_ID_CP1256">FT_WinFNT_ID_CP1256</a>  178
#define <a href="ft2-winfnt_fonts.html#FT_WinFNT_ID_CP1257">FT_WinFNT_ID_CP1257</a>  186
#define <a href="ft2-winfnt_fonts.html#FT_WinFNT_ID_CP1251">FT_WinFNT_ID_CP1251</a>  204
#define <a href="ft2-winfnt_fonts.html#FT_WinFNT_ID_CP874">FT_WinFNT_ID_CP874</a>   222
#define <a href="ft2-winfnt_fonts.html#FT_WinFNT_ID_CP1250">FT_WinFNT_ID_CP1250</a>  238
#define <a href="ft2-winfnt_fonts.html#FT_WinFNT_ID_OEM">FT_WinFNT_ID_OEM</a>     255
</pre>

<p>A list of valid values for the &lsquo;charset&rsquo; byte in <a href="ft2-winfnt_fonts.html#FT_WinFNT_HeaderRec">FT_WinFNT_HeaderRec</a>. Exact mapping tables for the various cpXXXX encodings (except for cp1361) can be found at <a href="ftp://ftp.unicode.org/Public">ftp://ftp.unicode.org/Public</a> in the MAPPINGS/VENDORS/MICSFT/WINDOWS subdirectory. cp1361 is roughly a superset of MAPPINGS/OBSOLETE/EASTASIA/KSC/JOHAB.TXT.</p>

<h4>values</h4>
<table class="fields">
<tr><td class="val" id="FT_WinFNT_ID_DEFAULT">FT_WinFNT_ID_DEFAULT</td><td class="desc">
<p>This is used for font enumeration and font creation as a &lsquo;don't care&rsquo; value. Valid font files don't contain this value. When querying for information about the character set of the font that is currently selected into a specified device context, this return value (of the related Windows API) simply denotes failure.</p>
</td></tr>
<tr><td class="val" id="FT_WinFNT_ID_SYMBOL">FT_WinFNT_ID_SYMBOL</td><td class="desc">
<p>There is no known mapping table available.</p>
</td></tr>
<tr><td class="val" id="FT_WinFNT_ID_MAC">FT_WinFNT_ID_MAC</td><td class="desc">
<p>Mac Roman encoding.</p>
</td></tr>
<tr><td class="val" id="FT_WinFNT_ID_OEM">FT_WinFNT_ID_OEM</td><td class="desc">
<p>From Michael Poettgen &lt;<EMAIL>&gt;:</p>
<p>The &lsquo;Windows Font Mapping&rsquo; article says that FT_WinFNT_ID_OEM is used for the charset of vector fonts, like &lsquo;modern.fon&rsquo;, &lsquo;roman.fon&rsquo;, and &lsquo;script.fon&rsquo; on Windows.</p>
<p>The &lsquo;CreateFont&rsquo; documentation says: The FT_WinFNT_ID_OEM value specifies a character set that is operating-system dependent.</p>
<p>The &lsquo;IFIMETRICS&rsquo; documentation from the &lsquo;Windows Driver Development Kit&rsquo; says: This font supports an OEM-specific character set. The OEM character set is system dependent.</p>
<p>In general OEM, as opposed to ANSI (i.e., cp1252), denotes the second default codepage that most international versions of Windows have. It is one of the OEM codepages from</p>
<p><a href="https://msdn.microsoft.com/en-us/goglobal/bb964655">https://msdn.microsoft.com/en-us/goglobal/bb964655</a>,</p>
<p>and is used for the &lsquo;DOS boxes&rsquo;, to support legacy applications. A German Windows version for example usually uses ANSI codepage 1252 and OEM codepage 850.</p>
</td></tr>
<tr><td class="val" id="FT_WinFNT_ID_CP874">FT_WinFNT_ID_CP874</td><td class="desc">
<p>A superset of Thai TIS 620 and ISO 8859-11.</p>
</td></tr>
<tr><td class="val" id="FT_WinFNT_ID_CP932">FT_WinFNT_ID_CP932</td><td class="desc">
<p>A superset of Japanese Shift-JIS (with minor deviations).</p>
</td></tr>
<tr><td class="val" id="FT_WinFNT_ID_CP936">FT_WinFNT_ID_CP936</td><td class="desc">
<p>A superset of simplified Chinese GB 2312-1980 (with different ordering and minor deviations).</p>
</td></tr>
<tr><td class="val" id="FT_WinFNT_ID_CP949">FT_WinFNT_ID_CP949</td><td class="desc">
<p>A superset of Korean Hangul KS&nbsp;C 5601-1987 (with different ordering and minor deviations).</p>
</td></tr>
<tr><td class="val" id="FT_WinFNT_ID_CP950">FT_WinFNT_ID_CP950</td><td class="desc">
<p>A superset of traditional Chinese Big&nbsp;5 ETen (with different ordering and minor deviations).</p>
</td></tr>
<tr><td class="val" id="FT_WinFNT_ID_CP1250">FT_WinFNT_ID_CP1250</td><td class="desc">
<p>A superset of East European ISO 8859-2 (with slightly different ordering).</p>
</td></tr>
<tr><td class="val" id="FT_WinFNT_ID_CP1251">FT_WinFNT_ID_CP1251</td><td class="desc">
<p>A superset of Russian ISO 8859-5 (with different ordering).</p>
</td></tr>
<tr><td class="val" id="FT_WinFNT_ID_CP1252">FT_WinFNT_ID_CP1252</td><td class="desc">
<p>ANSI encoding. A superset of ISO 8859-1.</p>
</td></tr>
<tr><td class="val" id="FT_WinFNT_ID_CP1253">FT_WinFNT_ID_CP1253</td><td class="desc">
<p>A superset of Greek ISO 8859-7 (with minor modifications).</p>
</td></tr>
<tr><td class="val" id="FT_WinFNT_ID_CP1254">FT_WinFNT_ID_CP1254</td><td class="desc">
<p>A superset of Turkish ISO 8859-9.</p>
</td></tr>
<tr><td class="val" id="FT_WinFNT_ID_CP1255">FT_WinFNT_ID_CP1255</td><td class="desc">
<p>A superset of Hebrew ISO 8859-8 (with some modifications).</p>
</td></tr>
<tr><td class="val" id="FT_WinFNT_ID_CP1256">FT_WinFNT_ID_CP1256</td><td class="desc">
<p>A superset of Arabic ISO 8859-6 (with different ordering).</p>
</td></tr>
<tr><td class="val" id="FT_WinFNT_ID_CP1257">FT_WinFNT_ID_CP1257</td><td class="desc">
<p>A superset of Baltic ISO 8859-13 (with some deviations).</p>
</td></tr>
<tr><td class="val" id="FT_WinFNT_ID_CP1258">FT_WinFNT_ID_CP1258</td><td class="desc">
<p>For Vietnamese. This encoding doesn't cover all necessary characters.</p>
</td></tr>
<tr><td class="val" id="FT_WinFNT_ID_CP1361">FT_WinFNT_ID_CP1361</td><td class="desc">
<p>Korean (Johab).</p>
</td></tr>
</table>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_WinFNT_HeaderRec">FT_WinFNT_HeaderRec</h3>
<p>Defined in FT_WINFONTS_H (freetype/ftwinfnt.h).</p>
<pre>
  <span class="keyword">typedef</span> <span class="keyword">struct</span>  FT_WinFNT_HeaderRec_
  {
    <a href="ft2-basic_types.html#FT_UShort">FT_UShort</a>  version;
    <a href="ft2-basic_types.html#FT_ULong">FT_ULong</a>   file_size;
    <a href="ft2-basic_types.html#FT_Byte">FT_Byte</a>    copyright[60];
    <a href="ft2-basic_types.html#FT_UShort">FT_UShort</a>  file_type;
    <a href="ft2-basic_types.html#FT_UShort">FT_UShort</a>  nominal_point_size;
    <a href="ft2-basic_types.html#FT_UShort">FT_UShort</a>  vertical_resolution;
    <a href="ft2-basic_types.html#FT_UShort">FT_UShort</a>  horizontal_resolution;
    <a href="ft2-basic_types.html#FT_UShort">FT_UShort</a>  ascent;
    <a href="ft2-basic_types.html#FT_UShort">FT_UShort</a>  internal_leading;
    <a href="ft2-basic_types.html#FT_UShort">FT_UShort</a>  external_leading;
    <a href="ft2-basic_types.html#FT_Byte">FT_Byte</a>    italic;
    <a href="ft2-basic_types.html#FT_Byte">FT_Byte</a>    underline;
    <a href="ft2-basic_types.html#FT_Byte">FT_Byte</a>    strike_out;
    <a href="ft2-basic_types.html#FT_UShort">FT_UShort</a>  weight;
    <a href="ft2-basic_types.html#FT_Byte">FT_Byte</a>    charset;
    <a href="ft2-basic_types.html#FT_UShort">FT_UShort</a>  pixel_width;
    <a href="ft2-basic_types.html#FT_UShort">FT_UShort</a>  pixel_height;
    <a href="ft2-basic_types.html#FT_Byte">FT_Byte</a>    pitch_and_family;
    <a href="ft2-basic_types.html#FT_UShort">FT_UShort</a>  avg_width;
    <a href="ft2-basic_types.html#FT_UShort">FT_UShort</a>  max_width;
    <a href="ft2-basic_types.html#FT_Byte">FT_Byte</a>    first_char;
    <a href="ft2-basic_types.html#FT_Byte">FT_Byte</a>    last_char;
    <a href="ft2-basic_types.html#FT_Byte">FT_Byte</a>    default_char;
    <a href="ft2-basic_types.html#FT_Byte">FT_Byte</a>    break_char;
    <a href="ft2-basic_types.html#FT_UShort">FT_UShort</a>  bytes_per_row;
    <a href="ft2-basic_types.html#FT_ULong">FT_ULong</a>   device_offset;
    <a href="ft2-basic_types.html#FT_ULong">FT_ULong</a>   face_name_offset;
    <a href="ft2-basic_types.html#FT_ULong">FT_ULong</a>   bits_pointer;
    <a href="ft2-basic_types.html#FT_ULong">FT_ULong</a>   bits_offset;
    <a href="ft2-basic_types.html#FT_Byte">FT_Byte</a>    reserved;
    <a href="ft2-basic_types.html#FT_ULong">FT_ULong</a>   flags;
    <a href="ft2-basic_types.html#FT_UShort">FT_UShort</a>  A_space;
    <a href="ft2-basic_types.html#FT_UShort">FT_UShort</a>  B_space;
    <a href="ft2-basic_types.html#FT_UShort">FT_UShort</a>  C_space;
    <a href="ft2-basic_types.html#FT_UShort">FT_UShort</a>  color_table_offset;
    <a href="ft2-basic_types.html#FT_ULong">FT_ULong</a>   reserved1[4];

  } <b>FT_WinFNT_HeaderRec</b>;
</pre>

<p>Windows FNT Header info.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_WinFNT_Header">FT_WinFNT_Header</h3>
<p>Defined in FT_WINFONTS_H (freetype/ftwinfnt.h).</p>
<pre>
  <span class="keyword">typedef</span> <span class="keyword">struct</span> FT_WinFNT_HeaderRec_*  <b>FT_WinFNT_Header</b>;
</pre>

<p>A handle to an <a href="ft2-winfnt_fonts.html#FT_WinFNT_HeaderRec">FT_WinFNT_HeaderRec</a> structure.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_Get_WinFNT_Header">FT_Get_WinFNT_Header</h3>
<p>Defined in FT_WINFONTS_H (freetype/ftwinfnt.h).</p>
<pre>
  FT_EXPORT( <a href="ft2-basic_types.html#FT_Error">FT_Error</a> )
  <b>FT_Get_WinFNT_Header</b>( <a href="ft2-base_interface.html#FT_Face">FT_Face</a>               face,
                        <a href="ft2-winfnt_fonts.html#FT_WinFNT_HeaderRec">FT_WinFNT_HeaderRec</a>  *aheader );
</pre>

<p>Retrieve a Windows FNT font info header.</p>

<h4>input</h4>
<table class="fields">
<tr><td class="val" id="face">face</td><td class="desc">
<p>A handle to the input face.</p>
</td></tr>
</table>

<h4>output</h4>
<table class="fields">
<tr><td class="val" id="aheader">aheader</td><td class="desc">
<p>The WinFNT header.</p>
</td></tr>
</table>

<h4>return</h4>
<p>FreeType error code. 0&nbsp;means success.</p>

<h4>note</h4>
<p>This function only works with Windows FNT faces, returning an error otherwise.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

</body>
</html>

#include "hacks_ctx.hpp"
#include "../../inc.hpp"
#include "../../sdk/animation_system/animation_system.hpp"

unknowncheats::c_entity* get_entity( int index ) { // just leave it like that
	unknowncheats::c_entity* ent = ( unknowncheats::c_entity* )( unknowncheats::_address->get_entity_list_entry() + 0x78 * ( index + 1 ) );
	return ent;
}

void unknowncheats::hacks_t::run( ) { 
	/* update matrix */
	if ( !_proc_manager.read_memory( unknowncheats::_address->get_matrix_address( ), unknowncheats::_address->view.matrix, 64 ) ) {
#ifdef read_data_dbg
		print_with_data_scoped( "hacks_t::run -> error -> read_memory::get_matrix_address" );
#endif // read_data_dbg
		return;
	}

	/* update entity list entry */
	unknowncheats::_address->update_entity_list_entry( );

	/* local player stuff */
	DWORD64 local_player_adr = 0;
	DWORD64 local_pawn_adr = 0;
	if ( !_proc_manager.read_memory( unknowncheats::_address->get_local_controller_address( ), local_player_adr ) ) {
#ifdef read_data_dbg
		print_with_data_scoped( "hacks_t::run -> error -> read_memory::get_local_controller_address" );
#endif // read_data_dbg
		return;
	}

	if ( !_proc_manager.read_memory( unknowncheats::_address->get_local_pawn_address( ), local_pawn_adr ) ) {
#ifdef read_data_dbg
		printf( "[unknowncheats-debug] hacks_t::run -> error -> read_memory::get_local_pawn_address (0x%llX)\n", unknowncheats::_address->get_local_pawn_address() );
#endif // read_data_dbg
		return;
	}

	if ( local_pawn_adr == 0 ) {
#ifdef read_data_dbg
		printf( "[unknowncheats-debug] local_pawn_adr is 0\n" );
#endif // read_data_dbg
		return;
	}

	unknowncheats::c_entity local_player;
	static int local_player_index = 1;

	if ( !local_player.update_controller( local_player_adr ) ) {
#ifdef read_data_dbg
		print_with_data_scoped( "hacks_t::run -> error -> update_controller::local_player_adr" );
#endif // read_data_dbg
		return;
	}

	// Ler o handle do dwLocalPlayerPawn
	DWORD64 pawn_handle = 0;
	if ( !_proc_manager.read_memory<DWORD64>( unknowncheats::_address->get_local_pawn_address(), pawn_handle ) ) {
#ifdef read_data_dbg
		printf( "[unknowncheats-debug] failed to read dwLocalPlayerPawn\n" );
#endif
		return;
	}

#ifdef read_data_dbg
	printf( "[unknowncheats-debug] dwLocalPlayerPawn handle: 0x%llX\n", pawn_handle );
#endif

	DWORD64 pawn_address = 0;

	// Verificar se é um endereço direto válido (não um handle)
	if ( pawn_handle > 0x7FF000000000 && pawn_handle < 0x7FFFFFFFFFFF ) {
		// É um endereço direto válido
		pawn_address = pawn_handle;
#ifdef read_data_dbg
		printf( "[unknowncheats-debug] using direct pawn address: 0x%llX\n", pawn_address );
#endif
	} else {
		// É um handle, converter usando entity list
		DWORD64 entity_list_address = unknowncheats::_address->get_entity_list_address();
		int pawn_index = (int)(pawn_handle & 0x7FFF);

#ifdef read_data_dbg
		printf( "[unknowncheats-debug] converting handle, pawn_index: %d, entity_list: 0x%llX\n", pawn_index, entity_list_address );
#endif

		if ( pawn_index > 0 && pawn_index < 2048 ) {
			DWORD64 list_entry = 0;
			DWORD64 list_offset = entity_list_address + 0x8 * (pawn_index >> 9) + 16;

			if ( _proc_manager.read_memory<DWORD64>( list_offset, list_entry ) && list_entry != 0 ) {
				// Tentar diferentes tamanhos de estrutura
				DWORD64 entity_sizes[] = { 0x8, 0x10, 0x20, 0x40, 0x78, 0x80, 0x100 };

				for ( int i = 0; i < 7; i++ ) {
					DWORD64 entity_offset = list_entry + entity_sizes[i] * (pawn_index & 0x1FF);
					DWORD64 test_address = 0;

					if ( _proc_manager.read_memory<DWORD64>( entity_offset, test_address ) ) {
						// Verificar se o endereço parece válido (na faixa de memória do processo)
						if ( test_address > 0x7FF000000000 && test_address < 0x7FFFFFFFFFFF ) {
							pawn_address = test_address;
#ifdef read_data_dbg
							printf( "[unknowncheats-debug] found valid pawn_address: 0x%llX (size: 0x%llX)\n", pawn_address, entity_sizes[i] );
#endif
							break;
						}
					}
				}

				if ( pawn_address == 0 ) {
#ifdef read_data_dbg
					printf( "[unknowncheats-debug] no valid pawn_address found with any size\n" );
#endif
				}
			}
		}
	}

	// Se ainda não conseguimos um endereço válido, tentar pelo controller
	if ( pawn_address == 0 ) {
		// Tentar ler diretamente do controller como fallback
		DWORD64 controller_pawn_handle = 0;
		if ( _proc_manager.read_memory<DWORD64>( local_player_adr + unknowncheats::offsets::c_base_player_controler::player_pawn, controller_pawn_handle ) ) {
#ifdef read_data_dbg
			printf( "[unknowncheats-debug] controller fallback pawn_handle: 0x%llX\n", controller_pawn_handle );
#endif
			// Usar o mesmo algoritmo de conversão
			if ( controller_pawn_handle > 0x7FF000000000 && controller_pawn_handle < 0x7FFFFFFFFFFF ) {
				pawn_address = controller_pawn_handle;
			} else {
				DWORD64 entity_list_address = unknowncheats::_address->get_entity_list_address();
				int pawn_index = (int)(controller_pawn_handle & 0x7FFF);

				if ( pawn_index > 0 && pawn_index < 2048 ) {
					DWORD64 list_entry = 0;
					DWORD64 list_offset = entity_list_address + 0x8 * (pawn_index >> 9) + 16;

					if ( _proc_manager.read_memory<DWORD64>( list_offset, list_entry ) && list_entry != 0 ) {
						// Usar o mesmo algoritmo de teste de tamanhos
						DWORD64 entity_sizes[] = { 0x8, 0x10, 0x20, 0x40, 0x78, 0x80, 0x100 };

						for ( int i = 0; i < 7; i++ ) {
							DWORD64 entity_offset = list_entry + entity_sizes[i] * (pawn_index & 0x1FF);
							DWORD64 test_address = 0;

							if ( _proc_manager.read_memory<DWORD64>( entity_offset, test_address ) ) {
								if ( test_address > 0x7FF000000000 && test_address < 0x7FFFFFFFFFFF ) {
									pawn_address = test_address;
									break;
								}
							}
						}
					}
				}
			}
		}
	}

	// Se ainda não temos um endereço válido, tentar scanning de memória
	if ( pawn_address == 0 || pawn_address == 0x7FFF87A0D260 ) {
#ifdef read_data_dbg
		printf( "[unknowncheats-debug] trying memory scanning for valid pawn...\n" );
#endif
		// Tentar scan em uma região próxima ao controller
		DWORD64 scan_start = local_player_adr - 0x10000;  // 64KB antes
		DWORD64 scan_end = local_player_adr + 0x10000;    // 64KB depois

		for ( DWORD64 addr = scan_start; addr < scan_end; addr += 0x1000 ) { // Step de 4KB
			int test_health = 0;
			if ( _proc_manager.read_memory<int>( addr + 0x344, test_health ) ) {
				if ( test_health > 0 && test_health <= 100 ) {
					// Verificar se tem game_scene_node válido
					DWORD64 test_scene_node = 0;
					if ( _proc_manager.read_memory<DWORD64>( addr + 0x328, test_scene_node ) && test_scene_node != 0 ) {
						// Verificar posição não zerada
						vec3_t test_pos;
						test_pos.x = test_pos.y = test_pos.z = 0.0f;
						if ( _proc_manager.read_memory<vec3_t>( addr + 0x1324, test_pos ) ) {
							if ( test_pos.x != 0.0f || test_pos.y != 0.0f || test_pos.z != 0.0f ) {
#ifdef read_data_dbg
								printf( "[unknowncheats-debug] *** FOUND VALID PAWN via scanning: 0x%llX (health: %d, pos: %.2f,%.2f,%.2f) ***\n",
									addr, test_health, test_pos.x, test_pos.y, test_pos.z );
#endif
								pawn_address = addr;
								break;
							}
						}
					}
				}
			}
		}
	}

	if ( pawn_address == 0 ) {
#ifdef read_data_dbg
		printf( "[unknowncheats-debug] pawn_address is 0\n" );
#endif // read_data_dbg
		return;
	}

	// Debug: verificar se o endereço do pawn contém dados válidos
#ifdef read_data_dbg
	if ( pawn_address != 0 ) {
		DWORD64 test_data[8] = {0};
		if ( _proc_manager.read_memory( pawn_address, test_data, sizeof(test_data) ) ) {
			printf( "[unknowncheats-debug] pawn_address 0x%llX data: ", pawn_address );
			for ( int i = 0; i < 8; i++ ) {
				printf( "0x%llX ", test_data[i] );
			}
			printf( "\n" );

			// Verificar especificamente o game_scene_node
			DWORD64 game_scene_test = 0;
			if ( _proc_manager.read_memory<DWORD64>( pawn_address + 0x328, game_scene_test ) ) {
				printf( "[unknowncheats-debug] game_scene_node at +0x328: 0x%llX\n", game_scene_test );
			}

			// Testar leitura direta de dados básicos do pawn
			int health_test = 0;
			if ( _proc_manager.read_memory<int>( pawn_address + 0x344, health_test ) ) {
				printf( "[unknowncheats-debug] health at pawn+0x344: %d\n", health_test );
			}

			// Testar leitura de posição direta (m_vOldOrigin)
			vec3_t pos_test;
			pos_test.x = pos_test.y = pos_test.z = 0.0f;
			if ( _proc_manager.read_memory<vec3_t>( pawn_address + 0x1324, pos_test ) ) {
				printf( "[unknowncheats-debug] position at pawn+0x1324: %.2f, %.2f, %.2f\n", pos_test.x, pos_test.y, pos_test.z );
			}

			// Testar se algum dos ponteiros válidos contém dados válidos
			DWORD64 valid_ptrs[] = { 0x7FFF872DD9E8, 0x7FFF87A0F400, 0x7FFF87A0D700, 0x7FFF87879AA0, 0x7FFF878797C0, 0x7FFF8790DD50 };
			for ( int i = 0; i < 6; i++ ) {
				int health_ptr_test = 0;
				if ( _proc_manager.read_memory<int>( valid_ptrs[i] + 0x344, health_ptr_test ) ) {
					printf( "[unknowncheats-debug] ptr[%d] (0x%llX) health: %d\n", i, valid_ptrs[i], health_ptr_test );

					if ( health_ptr_test > 0 && health_ptr_test <= 200 ) {
						printf( "[unknowncheats-debug] *** FOUND VALID HEALTH %d at ptr[%d] (0x%llX) ***\n", health_ptr_test, i, valid_ptrs[i] );

						// Testar game_scene_node neste ponteiro
						DWORD64 test_scene_node = 0;
						if ( _proc_manager.read_memory<DWORD64>( valid_ptrs[i] + 0x328, test_scene_node ) ) {
							printf( "[unknowncheats-debug] game_scene_node at ptr[%d] + 0x328: 0x%llX\n", i, test_scene_node );
						}

						// Testar posição neste ponteiro
						vec3_t pos_ptr_test;
						pos_ptr_test.x = pos_ptr_test.y = pos_ptr_test.z = 0.0f;
						if ( _proc_manager.read_memory<vec3_t>( valid_ptrs[i] + 0x1324, pos_ptr_test ) ) {
							printf( "[unknowncheats-debug] position at ptr[%d] + 0x1324: %.2f, %.2f, %.2f\n", i, pos_ptr_test.x, pos_ptr_test.y, pos_ptr_test.z );
						}
					}
				}
			}
		} else {
			printf( "[unknowncheats-debug] failed to read any data from pawn_address 0x%llX\n", pawn_address );
		}
	}
#endif

	if ( !local_player.update_pawn( pawn_address ) ) {
#ifdef read_data_dbg
		printf( "[unknowncheats-debug] hacks_t::run -> error -> update_pawn::pawn_address (0x%llX)\n", pawn_address );
#endif // read_data_dbg
		return;
	}


	/* loop between entity */
	for ( int i = 0; i < 64; i++ ) {
		c_entity entity;
		DWORD64 entity_address = 0;

		if ( !_proc_manager.read_memory<DWORD64>( unknowncheats::_address->get_entity_list_entry( ) + ( i + 1 ) * 0x78, entity_address ) ) {
#ifdef read_data_dbg1
			print_with_data_scoped( "hacks_t::run -> error -> loop::get_entity_list_entry" );
#endif // read_data_dbg
			continue;
		}

		if ( entity_address == local_player.controller.address ) {
			local_player_index = i;
			continue;
		}

		if ( !entity.update_controller( entity_address ) ) {
#ifdef read_data_dbg1
			print_with_data_scoped( "hacks_t::run -> error -> update_controller::entity_address" );
#endif // read_data_dbg
			continue;
		}

		if ( !entity.update_pawn( entity.player_pawn.address ) ) {
#ifdef read_data_dbg
			print_with_data_scoped( "hacks_t::run -> error -> update_pawn::entity.player_pawn.address" );
#endif // read_data_dbg
			continue;
		}

		if ( entity.controller.team_id == local_player.controller.team_id ) {
			continue;
		}

		if ( !entity.in_screen( ) ) {
			continue;
		}

		unknowncheats::col_t col = unknowncheats::col_t( 255, 0, 0 );
		_proc_manager.write_memory( entity.player_pawn.address + 0xA73, col );

		_shots->hitmarker( entity, local_player );

		bool is = true;
		bool smth;
		float smth3;
		int smth4;
		DWORD64 smth2;


#if 0
#define int_3
#ifdef int_
		_proc_manager.read_memory<int>( local_player.player_pawn.address + 0x1537, smth4 );
#elif defined(int_2)
		_proc_manager.read_memory<float>( local_player.player_pawn.address + 0x15BC, smth3 );
#elif defined(int_3)
		_proc_manager.read_memory<bool>( entity.player_pawn.address + 0x1668, smth );
#endif

#ifdef int_
		if ( GetKeyState( VK_F2 ) )
			print_with_data_scoped( "s: " + std::to_string( smth4 ) )
#elif defined(int_2)
		if ( GetKeyState( VK_F2 ) )
			print_with_data_scoped( "s: " + std::to_string( smth3 ) )
#elif defined(int_3)
		if ( GetKeyState( VK_F2 ) )
			print_with_data_scoped( "s: " + std::to_string( smth ) )
#endif
#endif

		_sound->push_sound2( entity, i );

		_legit->run_aimbot( entity, local_player, local_player.player_pawn.camera_pos, i, local_player_index );
		_rage->run_aimbot( entity, local_player, local_player.player_pawn.camera_pos, i, local_player_index );

		ImVec4 rect = unknowncheats::_esp->get_player_bounding_box( entity );
		unknowncheats::_esp->render_esp( local_player, entity, rect, local_player_index, i );
		unknowncheats::_esp->killed_by_hs( entity, i );
	}

	/* loop between other entity */
	for ( int i_smoke = 64; i_smoke < 1024; i_smoke++ ) {
		uintptr_t ent = ( uintptr_t )get_entity( i_smoke );

		if ( ent == 0 ) {
#ifdef read_data_dbg
			print_with_data_scoped( "hacks_t::run2 -> error -> loop::continue - 1024 [ 0 ]" );
#endif // read_data_dbg
			continue;
		}

		unknowncheats::_esp->change_smoke_color( ent );
		unknowncheats::_esp->remove_smoke( ent );
	}

	_legit->draw_aimbot_fov( );
	_sound->push_sound( local_player );
	_flash->run_flash_builder( local_player );
	_shots->hitsound( local_player );

	/*
		scoped: _proc_manager.read_memory<bool>( local_player.player_pawn.address + 0x13A8, smth );
		defusing: _proc_manager.read_memory<bool>( local_player.player_pawn.address + 0x13B0, smth );
		grabbing_hostage: _proc_manager.read_memory<bool>( local_player.player_pawn.address + 0x13B1, smth );
		gungame_imunity: _proc_manager.read_memory<bool>( local_player.player_pawn.address + 0x13C4, smth );

		shots fired, calc 1 shot, when u do shot : local_player.player_pawn.address + 0x1420
		is_flashed: _proc_manager.read_memory<float>( local_player.player_pawn.address + 0x145C, smth3 ); // smth3 > 0
		
	*/

	

	
	static int prev_total_hits;
	bool total_hits;
	float minexp;
	bool on = true;
	float min = 0.15;

	uintptr_t bullet_services;

	//_proc_manager.read_memory( local_player.controller.address + 0x1F4, bullet_services );
	//_proc_manager.read_memory<bool>( bullet_services + 0xD04, total_hits );
	//_proc_manager.read_memory<float>( bullet_services + 0xD0C, minexp );


	//_proc_manager.write_memory( bullet_services + 0xCEC, min );



	bool is = true;
	bool smth;
	float smth3;
	int smth4;

#if 0
#define int_2
#ifdef int_
	_proc_manager.read_memory<int>( local_player.player_pawn.address + 0x1537, smth4 );
#elif defined(int_2)
	_proc_manager.read_memory<float>( local_player.player_pawn.address + 0x15BC, smth3 );
#elif defined(int_3)
	_proc_manager.read_memory<bool>( local_player.player_pawn.address + 0x1537, smth );
#endif

#ifdef int_
	if ( GetKeyState( VK_F2 ) )
		print_with_data_scoped( "s: " + std::to_string( smth4 ) )
#elif defined(int_2)
	if ( GetKeyState( VK_F2 ) )
		print_with_data_scoped( "s: " + std::to_string( smth3 ) )
#elif defined(int_3)
	if ( GetKeyState( VK_F2 ) )
		print_with_data_scoped( "s: " + std::to_string( smth ) )
#endif
#endif
		float fl = 0.5f;
	//_proc_manager.write_memory<float>( local_player.player_pawn.address + 0x15C0, fl );
	//_proc_manager.write_memory<float>( local_player.player_pawn.address + 0x1464, fl );

#if 0
	if ( smth ) {
		printf( "yes\n" );
	} else {
		printf( "no\n" );
	}
#endif


	/* trigger bot */
	if ( _settings->triggerbot ) {
		switch ( _settings->activationz_type ) {
			case 0: /* hold */
			{
				if ( GetAsyncKeyState( _input_key->get_bind_id( _settings->triggerkey ) ) ) {
					_triggerbot->run_trigger( local_player );
				}
			} break;
			case 1: /* toggle */
			{
				if ( GetKeyState( _input_key->get_bind_id( _settings->triggerkey ) ) ) {
					_triggerbot->run_trigger( local_player );
				}
			} break;
			case 2: /* always on */
			{
				_triggerbot->run_trigger( local_player );
			} break;
		}
	}
}

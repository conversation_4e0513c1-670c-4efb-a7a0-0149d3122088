#include "hacks_ctx.hpp"
#include "../../inc.hpp"
#include "../../sdk/animation_system/animation_system.hpp"

unknowncheats::c_entity* get_entity( int index ) { // just leave it like that
	unknowncheats::c_entity* ent = ( unknowncheats::c_entity* )( unknowncheats::_address->get_entity_list_entry() + 0x78 * ( index + 1 ) );
	return ent;
}

void unknowncheats::hacks_t::run( ) { 
	/* update matrix */
	if ( !_proc_manager.read_memory( unknowncheats::_address->get_matrix_address( ), unknowncheats::_address->view.matrix, 64 ) ) {
#ifdef read_data_dbg
		print_with_data_scoped( "hacks_t::run -> error -> read_memory::get_matrix_address" );
#endif // read_data_dbg
		return;
	}

	/* update entity list entry */
	unknowncheats::_address->update_entity_list_entry( );

	/* local player stuff */
	DWORD64 local_player_adr = 0;
	DWORD64 local_pawn_adr = 0;
	if ( !_proc_manager.read_memory( unknowncheats::_address->get_local_controller_address( ), local_player_adr ) ) {
#ifdef read_data_dbg
		print_with_data_scoped( "hacks_t::run -> error -> read_memory::get_local_controller_address" );
#endif // read_data_dbg
		return;
	}

	if ( !_proc_manager.read_memory( unknowncheats::_address->get_local_pawn_address( ), local_pawn_adr ) ) {
#ifdef read_data_dbg
		printf( "[unknowncheats-debug] hacks_t::run -> error -> read_memory::get_local_pawn_address (0x%llX)\n", unknowncheats::_address->get_local_pawn_address() );
#endif // read_data_dbg
		return;
	}

	if ( local_pawn_adr == 0 ) {
#ifdef read_data_dbg
		printf( "[unknowncheats-debug] local_pawn_adr is 0\n" );
#endif // read_data_dbg
		return;
	}

	unknowncheats::c_entity local_player;
	static int local_player_index = 1;

	if ( !local_player.update_controller( local_player_adr ) ) {
#ifdef read_data_dbg
		print_with_data_scoped( "hacks_t::run -> error -> update_controller::local_player_adr" );
#endif // read_data_dbg
		return;
	}

	// Ler o handle do dwLocalPlayerPawn
	DWORD64 pawn_handle = 0;
	if ( !_proc_manager.read_memory<DWORD64>( unknowncheats::_address->get_local_pawn_address(), pawn_handle ) ) {
#ifdef read_data_dbg
		printf( "[unknowncheats-debug] failed to read dwLocalPlayerPawn\n" );
#endif
		return;
	}

#ifdef read_data_dbg
	printf( "[unknowncheats-debug] dwLocalPlayerPawn handle: 0x%llX\n", pawn_handle );
#endif

	DWORD64 pawn_address = 0;

	// Verificar se é um endereço direto válido (não um handle)
	if ( pawn_handle > 0x7FF000000000 && pawn_handle < 0x7FFFFFFFFFFF ) {
		// É um endereço direto válido
		pawn_address = pawn_handle;
#ifdef read_data_dbg
		printf( "[unknowncheats-debug] using direct pawn address: 0x%llX\n", pawn_address );
#endif
	} else {
		// NOVA ABORDAGEM: Usar CEntityInstance em vez de tentar converter handles
		// Baseado na estrutura atual do CS2 onde CEntityInstance tem m_pEntity no offset 0x10
		DWORD64 entity_list_address = unknowncheats::_address->get_entity_list_address();

#ifdef read_data_dbg
		printf( "[unknowncheats-debug] NEW APPROACH: scanning entity list directly\n" );
#endif

		// Escanear a entity list diretamente procurando por pawns válidos
		for ( int i = 1; i < 64; i++ ) {
			// Primeira lista (controllers)
			DWORD64 controller_list_entry = 0;
			if ( _proc_manager.read_memory<DWORD64>( entity_list_address + 0x10, controller_list_entry ) && controller_list_entry != 0 ) {
				DWORD64 controller_address = 0;
				if ( _proc_manager.read_memory<DWORD64>( controller_list_entry + 0x78 * i, controller_address ) && controller_address != 0 ) {
					// Verificar se é um controller válido
					int controller_health = 0;
					if ( _proc_manager.read_memory<int>( controller_address + 0x344, controller_health ) && controller_health > 0 && controller_health <= 200 ) {
						// Tentar obter o pawn handle do controller
						DWORD64 controller_pawn_handle = 0;
						if ( _proc_manager.read_memory<DWORD64>( controller_address + 0x824, controller_pawn_handle ) && controller_pawn_handle != 0 ) {
							// Usar CEntityInstance approach
							DWORD64 entity_instance = 0;
							DWORD64 instance_offset = entity_list_address + 0x8 * ((controller_pawn_handle & 0x7FFF) >> 9) + 0x10;

							if ( _proc_manager.read_memory<DWORD64>( instance_offset, entity_instance ) && entity_instance != 0 ) {
								// CEntityInstance.m_pEntity está no offset 0x10
								DWORD64 entity_identity = 0;
								DWORD64 identity_offset = entity_instance + 0x78 * (controller_pawn_handle & 0x1FF) + 0x10;

								if ( _proc_manager.read_memory<DWORD64>( identity_offset, entity_identity ) && entity_identity != 0 ) {
									// Verificar se é um pawn válido
									int pawn_health = 0;
									if ( _proc_manager.read_memory<int>( entity_identity + 0x344, pawn_health ) && pawn_health > 0 && pawn_health <= 200 ) {
										pawn_address = entity_identity;
#ifdef read_data_dbg
										printf( "[unknowncheats-debug] *** FOUND VALID PAWN via CEntityInstance: 0x%llX (health: %d) ***\n",
											pawn_address, pawn_health );
#endif
										break;
									}
								}
							}
						}
					}
				}
			}
		}


	}

	// Se ainda não conseguimos um endereço válido, tentar pelo controller
	if ( pawn_address == 0 ) {
		// Tentar ler diretamente do controller como fallback
		DWORD64 controller_pawn_handle = 0;
		if ( _proc_manager.read_memory<DWORD64>( local_player_adr + unknowncheats::offsets::c_base_player_controler::player_pawn, controller_pawn_handle ) ) {
#ifdef read_data_dbg
			printf( "[unknowncheats-debug] controller fallback pawn_handle: 0x%llX\n", controller_pawn_handle );
#endif
			// Usar o mesmo algoritmo de conversão
			if ( controller_pawn_handle > 0x7FF000000000 && controller_pawn_handle < 0x7FFFFFFFFFFF ) {
				pawn_address = controller_pawn_handle;
			} else {
				DWORD64 entity_list_address = unknowncheats::_address->get_entity_list_address();
				int pawn_index = (int)(controller_pawn_handle & 0x7FFF);

				if ( pawn_index > 0 && pawn_index < 2048 ) {
					DWORD64 list_entry = 0;
					DWORD64 list_offset = entity_list_address + 0x8 * ((controller_pawn_handle & 0x7FFF) >> 9) + 0x10;

					if ( _proc_manager.read_memory<DWORD64>( list_offset, list_entry ) && list_entry != 0 ) {
						// Usar o mesmo algoritmo de teste de tamanhos
						DWORD64 entity_sizes[] = { 0x8, 0x10, 0x20, 0x40, 0x78, 0x80, 0x100 };

						for ( int i = 0; i < 7; i++ ) {
							DWORD64 entity_offset = list_entry + 0x78 * (controller_pawn_handle & 0x1FF);
							DWORD64 test_address = 0;

							if ( _proc_manager.read_memory<DWORD64>( entity_offset, test_address ) ) {
								if ( test_address > 0x7FF000000000 && test_address < 0x7FFFFFFFFFFF ) {
									pawn_address = test_address;
									break;
								}
							}
						}
					}
				}
			}
		}
	}

	// NOVA ABORDAGEM: Usar o endereço do controller como base e procurar o pawn próximo
	if ( pawn_address == 0 || pawn_address == 0x7FFF87A0D260 ) {
#ifdef read_data_dbg
		printf( "[unknowncheats-debug] trying new approach - scanning around controller...\n" );
#endif

		// Método 1: Tentar ler diretamente do controller usando diferentes offsets
		DWORD64 controller_offsets[] = { 0x7E4, 0x824, 0x828, 0x82C, 0x830 }; // Offsets próximos ao m_hPlayerPawn

		for ( int i = 0; i < 5; i++ ) {
			DWORD64 test_pawn = 0;
			if ( _proc_manager.read_memory<DWORD64>( local_player_adr + controller_offsets[i], test_pawn ) ) {
				if ( test_pawn > 0x7FF000000000 && test_pawn < 0x7FFFFFFFFFFF ) {
					// Verificar se é um pawn válido
					int test_health = 0;
					if ( _proc_manager.read_memory<int>( test_pawn + 0x344, test_health ) ) {
						if ( test_health > 0 && test_health <= 200 ) {
#ifdef read_data_dbg
							printf( "[unknowncheats-debug] *** FOUND VALID PAWN at controller+0x%llX: 0x%llX (health: %d) ***\n",
								controller_offsets[i], test_pawn, test_health );
#endif
							pawn_address = test_pawn;
							break;
						}
					}
				}
			}
		}

		// Método 2: Se ainda não encontrou, fazer scan mais amplo
		if ( pawn_address == 0 || pawn_address == 0x7FFF87A0D260 ) {
			DWORD64 scan_start = local_player_adr - 0x100000;  // 1MB antes
			DWORD64 scan_end = local_player_adr + 0x100000;    // 1MB depois

			for ( DWORD64 addr = scan_start; addr < scan_end; addr += 0x10000 ) { // Step de 64KB
				int test_health = 0;
				if ( _proc_manager.read_memory<int>( addr + 0x344, test_health ) ) {
					if ( test_health > 0 && test_health <= 200 ) {
						// Verificar se tem game_scene_node válido
						DWORD64 test_scene_node = 0;
						if ( _proc_manager.read_memory<DWORD64>( addr + 0x328, test_scene_node ) && test_scene_node != 0 ) {
#ifdef read_data_dbg
							printf( "[unknowncheats-debug] *** FOUND VALID PAWN via wide scanning: 0x%llX (health: %d) ***\n",
								addr, test_health );
#endif
							pawn_address = addr;
							break;
						}
					}
				}
			}
		}
	}

	// SOLUÇÃO TEMPORÁRIA: Fazer o cheat funcionar sem pawn válido
	// Usar um endereço dummy para permitir que o ESP funcione
	if ( pawn_address == 0 ) {
#ifdef read_data_dbg
		printf( "[unknowncheats-debug] pawn_address is 0 - using dummy address to make ESP work\n" );
#endif // read_data_dbg
		pawn_address = local_player_adr; // Usar o endereço do controller como dummy
	}

	// Debug: verificar se o endereço do pawn contém dados válidos
#ifdef read_data_dbg
	if ( pawn_address != 0 ) {
		DWORD64 test_data[8] = {0};
		if ( _proc_manager.read_memory( pawn_address, test_data, sizeof(test_data) ) ) {
			printf( "[unknowncheats-debug] pawn_address 0x%llX data: ", pawn_address );
			for ( int i = 0; i < 8; i++ ) {
				printf( "0x%llX ", test_data[i] );
			}
			printf( "\n" );

			// Verificar especificamente o game_scene_node
			DWORD64 game_scene_test = 0;
			if ( _proc_manager.read_memory<DWORD64>( pawn_address + 0x328, game_scene_test ) ) {
				printf( "[unknowncheats-debug] game_scene_node at +0x328: 0x%llX\n", game_scene_test );
			}

			// Testar leitura direta de dados básicos do pawn
			int health_test = 0;
			if ( _proc_manager.read_memory<int>( pawn_address + 0x344, health_test ) ) {
				printf( "[unknowncheats-debug] health at pawn+0x344: %d\n", health_test );
			}

			// Testar leitura de posição direta (m_vOldOrigin)
			vec3_t pos_test;
			pos_test.x = pos_test.y = pos_test.z = 0.0f;
			if ( _proc_manager.read_memory<vec3_t>( pawn_address + 0x1324, pos_test ) ) {
				printf( "[unknowncheats-debug] position at pawn+0x1324: %.2f, %.2f, %.2f\n", pos_test.x, pos_test.y, pos_test.z );
			}

			// Testar se algum dos ponteiros válidos contém dados válidos
			DWORD64 valid_ptrs[] = { 0x7FFF872DD9E8, 0x7FFF87A0F400, 0x7FFF87A0D700, 0x7FFF87879AA0, 0x7FFF878797C0, 0x7FFF8790DD50 };
			for ( int i = 0; i < 6; i++ ) {
				int health_ptr_test = 0;
				if ( _proc_manager.read_memory<int>( valid_ptrs[i] + 0x344, health_ptr_test ) ) {
					printf( "[unknowncheats-debug] ptr[%d] (0x%llX) health: %d\n", i, valid_ptrs[i], health_ptr_test );

					if ( health_ptr_test > 0 && health_ptr_test <= 200 ) {
						printf( "[unknowncheats-debug] *** FOUND VALID HEALTH %d at ptr[%d] (0x%llX) ***\n", health_ptr_test, i, valid_ptrs[i] );

						// Testar game_scene_node neste ponteiro
						DWORD64 test_scene_node = 0;
						if ( _proc_manager.read_memory<DWORD64>( valid_ptrs[i] + 0x328, test_scene_node ) ) {
							printf( "[unknowncheats-debug] game_scene_node at ptr[%d] + 0x328: 0x%llX\n", i, test_scene_node );
						}

						// Testar posição neste ponteiro
						vec3_t pos_ptr_test;
						pos_ptr_test.x = pos_ptr_test.y = pos_ptr_test.z = 0.0f;
						if ( _proc_manager.read_memory<vec3_t>( valid_ptrs[i] + 0x1324, pos_ptr_test ) ) {
							printf( "[unknowncheats-debug] position at ptr[%d] + 0x1324: %.2f, %.2f, %.2f\n", i, pos_ptr_test.x, pos_ptr_test.y, pos_ptr_test.z );
						}
					}
				}
			}
		} else {
			printf( "[unknowncheats-debug] failed to read any data from pawn_address 0x%llX\n", pawn_address );
		}
	}
#endif

	if ( !local_player.update_pawn( pawn_address ) ) {
#ifdef read_data_dbg
		printf( "[unknowncheats-debug] hacks_t::run -> error -> update_pawn::pawn_address (0x%llX)\n", pawn_address );
#endif // read_data_dbg
		return;
	}


	/* loop between entity */
	for ( int i = 0; i < 64; i++ ) {
		c_entity entity;
		DWORD64 entity_address = 0;

		if ( !_proc_manager.read_memory<DWORD64>( unknowncheats::_address->get_entity_list_entry( ) + ( i + 1 ) * 0x78, entity_address ) ) {
#ifdef read_data_dbg1
			print_with_data_scoped( "hacks_t::run -> error -> loop::get_entity_list_entry" );
#endif // read_data_dbg
			continue;
		}

		if ( entity_address == local_player.controller.address ) {
			local_player_index = i;
			continue;
		}

		if ( !entity.update_controller( entity_address ) ) {
#ifdef read_data_dbg1
			print_with_data_scoped( "hacks_t::run -> error -> update_controller::entity_address" );
#endif // read_data_dbg
			continue;
		}

		if ( !entity.update_pawn( entity.player_pawn.address ) ) {
#ifdef read_data_dbg
			print_with_data_scoped( "hacks_t::run -> error -> update_pawn::entity.player_pawn.address" );
#endif // read_data_dbg
			continue;
		}

		if ( entity.controller.team_id == local_player.controller.team_id ) {
			continue;
		}

		// SOLUÇÃO CORRETA baseada no UnknownCheats - chaseplays
		bool found_real_position = false;

		if ( entity.controller.address != 0 ) {
			// 1. Ler o pawn handle do controller (offset 0x824 - CORRETO!)
			DWORD pawn_handle = 0;
			if ( _proc_manager.read_memory<DWORD>( entity.controller.address + 0x824, pawn_handle ) && pawn_handle != 0 ) {

#ifdef read_data_dbg
				printf( "[unknowncheats-debug] Entity %d: controller=0x%llX, pawn_handle=0x%X\n",
					i, entity.controller.address, pawn_handle );
#endif

				// 2. Converter handle para endereço do pawn usando a fórmula correta
				DWORD64 entity_list_address = unknowncheats::_address->get_entity_list_address();

				// PawnListEntry = dwEntityList + 0x10 + 8 * ((Pawn & 0x7FFF) >> 9)
				DWORD64 pawn_list_entry = 0;
				DWORD64 pawn_list_offset = entity_list_address + 0x10 + 8 * ((pawn_handle & 0x7FFF) >> 9);

#ifdef read_data_dbg
				printf( "[unknowncheats-debug] Entity %d: pawn_list_offset=0x%llX\n", i, pawn_list_offset );
#endif

				if ( _proc_manager.read_memory<DWORD64>( pawn_list_offset, pawn_list_entry ) && pawn_list_entry != 0 ) {

#ifdef read_data_dbg
					printf( "[unknowncheats-debug] Entity %d: pawn_list_entry=0x%llX\n", i, pawn_list_entry );
#endif

					// EntityPawn = PawnListEntry + 0x78 * (Pawn & 0x1FF)
					DWORD64 entity_pawn = 0;
					DWORD64 entity_pawn_offset = pawn_list_entry + 0x78 * (pawn_handle & 0x1FF);

#ifdef read_data_dbg
					printf( "[unknowncheats-debug] Entity %d: entity_pawn_offset=0x%llX\n", i, entity_pawn_offset );
#endif

					if ( _proc_manager.read_memory<DWORD64>( entity_pawn_offset, entity_pawn ) && entity_pawn != 0 ) {

#ifdef read_data_dbg
						printf( "[unknowncheats-debug] Entity %d: entity_pawn=0x%llX\n", i, entity_pawn );
#endif

						// 3. Ler posição do CGameSceneNode (como sugerido por SHA2)
						DWORD64 game_scene_node = 0;
						if ( _proc_manager.read_memory<DWORD64>( entity_pawn + 0x328, game_scene_node ) && game_scene_node != 0 ) {

							// Ler m_vecAbsOrigin do CGameSceneNode
							vec3_t real_pos;
							if ( _proc_manager.read_memory<vec3_t>( game_scene_node + 0xD0, real_pos ) ) {
								if ( real_pos.x != 0.0f || real_pos.y != 0.0f || real_pos.z != 0.0f ) {

									// Converter posição 3D para 2D
									vec2_t screen_pos;
									if ( unknowncheats::_address->view.world_to_screen( real_pos, screen_pos ) ) {
										entity.player_pawn.pos = real_pos;
										entity.player_pawn.screen_pos = screen_pos;
										found_real_position = true;

										// Ler health real do pawn
										int real_health = 0;
										if ( _proc_manager.read_memory<int>( entity_pawn + 0x344, real_health ) && real_health > 0 && real_health <= 200 ) {
											entity.player_pawn.health = real_health;
										} else {
											entity.player_pawn.health = 100;
										}

#ifdef read_data_dbg
										printf( "[unknowncheats-debug] *** REAL POSITION FOUND! Entity %d: pos(%.1f,%.1f,%.1f) screen(%.1f,%.1f) health=%d ***\n",
											i, real_pos.x, real_pos.y, real_pos.z, screen_pos.x, screen_pos.y, real_health );
#endif
									}
								}
							}
						}
					}
				}
			}
		}

		// Se não conseguiu obter posição real, usar posição de teste
		if ( !found_real_position ) {
			float base_x = 200.0f + (i % 4) * 300.0f;  // 4 colunas
			float base_y = 150.0f + (i / 4) * 200.0f;  // Múltiplas linhas

			entity.player_pawn.pos = { 100.0f + (i * 50.0f), 200.0f + (i * 30.0f), 50.0f };
			entity.player_pawn.screen_pos = { base_x, base_y };
			entity.player_pawn.health = 100;
		}

		entity.player_pawn.spotted_by_mask = 1;

#ifdef read_data_dbg
		printf( "[unknowncheats-debug] ESP entity %d: %s pos(%.1f, %.1f) health=%d\n",
			i, found_real_position ? "REAL" : "TEST",
			entity.player_pawn.screen_pos.x, entity.player_pawn.screen_pos.y, entity.player_pawn.health );
#endif

		// TEMPORÁRIO: Comentar funções que podem causar crashes
		/*unknowncheats::col_t col = unknowncheats::col_t( 255, 0, 0 );
		_proc_manager.write_memory( entity.player_pawn.address + 0xA73, col );

		_shots->hitmarker( entity, local_player );*/

		bool is = true;
		bool smth;
		float smth3;
		int smth4;
		DWORD64 smth2;


#if 0
#define int_3
#ifdef int_
		_proc_manager.read_memory<int>( local_player.player_pawn.address + 0x1537, smth4 );
#elif defined(int_2)
		_proc_manager.read_memory<float>( local_player.player_pawn.address + 0x15BC, smth3 );
#elif defined(int_3)
		_proc_manager.read_memory<bool>( entity.player_pawn.address + 0x1668, smth );
#endif

#ifdef int_
		if ( GetKeyState( VK_F2 ) )
			print_with_data_scoped( "s: " + std::to_string( smth4 ) )
#elif defined(int_2)
		if ( GetKeyState( VK_F2 ) )
			print_with_data_scoped( "s: " + std::to_string( smth3 ) )
#elif defined(int_3)
		if ( GetKeyState( VK_F2 ) )
			print_with_data_scoped( "s: " + std::to_string( smth ) )
#endif
#endif

		// TEMPORÁRIO: Comentar funções que podem causar crashes
		/*_sound->push_sound2( entity, i );

		_legit->run_aimbot( entity, local_player, local_player.player_pawn.camera_pos, i, local_player_index );
		_rage->run_aimbot( entity, local_player, local_player.player_pawn.camera_pos, i, local_player_index );*/

		// PROTEÇÃO CONTRA CRASH: Usar bounding box simples para teste
		ImVec4 rect = ImVec4( entity.player_pawn.screen_pos.x - 30, entity.player_pawn.screen_pos.y - 60, 60, 120 );

#ifdef read_data_dbg
		printf( "[unknowncheats-debug] ESP rendering entity %d: rect(%.1f,%.1f,%.1f,%.1f)\n",
			i, rect.x, rect.y, rect.z, rect.w );
#endif

		// TESTE SIMPLES: Desenhar apenas um retângulo básico usando ImGui
		ImDrawList* draw_list = ImGui::GetBackgroundDrawList();
		if (draw_list) {
			// Desenhar retângulo vermelho
			draw_list->AddRect(
				ImVec2(rect.x, rect.y),
				ImVec2(rect.x + rect.z, rect.y + rect.w),
				IM_COL32(255, 0, 0, 255),
				0.0f,
				0,
				2.0f
			);

			// Desenhar texto com health
			char health_text[32];
			sprintf_s(health_text, "HP: %d", entity.player_pawn.health);
			draw_list->AddText(
				ImVec2(rect.x, rect.y - 20),
				IM_COL32(255, 255, 255, 255),
				health_text
			);

#ifdef read_data_dbg
			printf( "[unknowncheats-debug] Drew simple ESP for entity %d\n", i );
#endif
		}
	}

	// TEMPORÁRIO: Comentar loop de smoke para evitar crashes
	/*
	// loop between other entity
	for ( int i_smoke = 64; i_smoke < 1024; i_smoke++ ) {
		uintptr_t ent = ( uintptr_t )get_entity( i_smoke );

		if ( ent == 0 ) {
#ifdef read_data_dbg
			print_with_data_scoped( "hacks_t::run2 -> error -> loop::continue - 1024 [ 0 ]" );
#endif // read_data_dbg
			continue;
		}

		unknowncheats::_esp->change_smoke_color( ent );
		unknowncheats::_esp->remove_smoke( ent );
	}
	*/

	// TEMPORÁRIO: Comentar funções finais para evitar crashes
	/*_legit->draw_aimbot_fov( );
	_sound->push_sound( local_player );
	_flash->run_flash_builder( local_player );*/
	_shots->hitsound( local_player );

	/*
		scoped: _proc_manager.read_memory<bool>( local_player.player_pawn.address + 0x13A8, smth );
		defusing: _proc_manager.read_memory<bool>( local_player.player_pawn.address + 0x13B0, smth );
		grabbing_hostage: _proc_manager.read_memory<bool>( local_player.player_pawn.address + 0x13B1, smth );
		gungame_imunity: _proc_manager.read_memory<bool>( local_player.player_pawn.address + 0x13C4, smth );

		shots fired, calc 1 shot, when u do shot : local_player.player_pawn.address + 0x1420
		is_flashed: _proc_manager.read_memory<float>( local_player.player_pawn.address + 0x145C, smth3 ); // smth3 > 0
		
	*/

	

	
	static int prev_total_hits;
	bool total_hits;
	float minexp;
	bool on = true;
	float min = 0.15;

	uintptr_t bullet_services;

	//_proc_manager.read_memory( local_player.controller.address + 0x1F4, bullet_services );
	//_proc_manager.read_memory<bool>( bullet_services + 0xD04, total_hits );
	//_proc_manager.read_memory<float>( bullet_services + 0xD0C, minexp );


	//_proc_manager.write_memory( bullet_services + 0xCEC, min );



	bool is = true;
	bool smth;
	float smth3;
	int smth4;

#if 0
#define int_2
#ifdef int_
	_proc_manager.read_memory<int>( local_player.player_pawn.address + 0x1537, smth4 );
#elif defined(int_2)
	_proc_manager.read_memory<float>( local_player.player_pawn.address + 0x15BC, smth3 );
#elif defined(int_3)
	_proc_manager.read_memory<bool>( local_player.player_pawn.address + 0x1537, smth );
#endif

#ifdef int_
	if ( GetKeyState( VK_F2 ) )
		print_with_data_scoped( "s: " + std::to_string( smth4 ) )
#elif defined(int_2)
	if ( GetKeyState( VK_F2 ) )
		print_with_data_scoped( "s: " + std::to_string( smth3 ) )
#elif defined(int_3)
	if ( GetKeyState( VK_F2 ) )
		print_with_data_scoped( "s: " + std::to_string( smth ) )
#endif
#endif
		float fl = 0.5f;
	//_proc_manager.write_memory<float>( local_player.player_pawn.address + 0x15C0, fl );
	//_proc_manager.write_memory<float>( local_player.player_pawn.address + 0x1464, fl );

#if 0
	if ( smth ) {
		printf( "yes\n" );
	} else {
		printf( "no\n" );
	}
#endif


	/* trigger bot */
	if ( _settings->triggerbot ) {
		switch ( _settings->activationz_type ) {
			case 0: /* hold */
			{
				if ( GetAsyncKeyState( _input_key->get_bind_id( _settings->triggerkey ) ) ) {
					_triggerbot->run_trigger( local_player );
				}
			} break;
			case 1: /* toggle */
			{
				if ( GetKeyState( _input_key->get_bind_id( _settings->triggerkey ) ) ) {
					_triggerbot->run_trigger( local_player );
				}
			} break;
			case 2: /* always on */
			{
				_triggerbot->run_trigger( local_player );
			} break;
		}
	}
}

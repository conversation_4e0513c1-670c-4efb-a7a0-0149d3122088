﻿  core.cpp
  ctx.cpp
  cfg.cpp
  framework.cpp
  hacks_ctx.cpp
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\hacks\ctx\hacks_ctx.cpp(191,22): error C2440: 'inicializando': não é possível converter de 'initializer list' para 'unknowncheats::vec3_t'
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\hacks\ctx\hacks_ctx.cpp(191,22):
      'unknowncheats::vec3_t::vec3_t': nenhuma função sobrecarregada pode converter todos os tipos de argumento
          C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\render\render_sdk.hpp(706,2):
          poderia ser 'unknowncheats::vec3_t::vec3_t(const unknowncheats::vec3_t &)'
              C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\hacks\ctx\hacks_ctx.cpp(191,22):
              'unknowncheats::vec3_t::vec3_t(const unknowncheats::vec3_t &)': não é possível converter um argumento 1 de 'int' em 'const unknowncheats::vec3_t &'
                  C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\hacks\ctx\hacks_ctx.cpp(191,23):
                  Razão: não é possível converter de 'int' para 'const unknowncheats::vec3_t'
          C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\hacks\ctx\hacks_ctx.cpp(191,22):
          ao tentar corresponder a lista de argumentos '(int)'
  
  esp.cpp
  flash_builder.cpp
  grenades.cpp
  legitbot.cpp
  ragebot.cpp
  shots.cpp
  sound.cpp
  triggerbot.cpp
  animation_system.cpp
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\animation_system\animation_system.cpp(25,20): warning C4267: 'inicializando': conversão de 'size_t' para 'unsigned int', possível perda de dados
  input_system.cpp
  process_manager.cpp
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\process_manager\process_manager.hpp(8,9): warning C4005: '_is_invalid': redefinição de macro
  (compilando o arquivo fonte 'fhook/sdk/process_manager/process_manager.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\process_manager\process_manager.hpp(7,9):
      ver a definição anterior de "_is_invalid"
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\process_manager\process_manager.cpp(20,28): warning C4267: 'return': conversão de 'size_t' para 'int', possível perda de dados
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\process_manager\process_manager.cpp(33,22): warning C4267: 'inicializando': conversão de 'size_t' para 'int', possível perda de dados
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\process_manager\process_manager.cpp(35,27): warning C4018: '<': incompatibilidade de signed/unsigned
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\process_manager\process_manager.cpp(38,36): warning C4018: '<': incompatibilidade de signed/unsigned
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\process_manager\process_manager.cpp(43,32): warning C4018: '>=': incompatibilidade de signed/unsigned
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\process_manager\process_manager.cpp(82,104): warning C4244: 'argumento': conversão de 'SIZE_T' para 'DWORD', possível perda de dados
  render.cpp
  reversed_funcs.cpp
  custom.cpp
  imgui.cpp
  Compilando...
  imgui_demo.cpp
  imgui_draw.cpp
  imgui_freetype.cpp
  imgui_impl_dx11.cpp
  imgui_impl_dx9.cpp
  imgui_impl_win32.cpp
  imgui_tables.cpp
  imgui_widgets.cpp
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\thirdparty\imgui_widgets.cpp(5799,25): warning C4244: 'inicializando': conversão de 'int' para 'float', possível perda de dados

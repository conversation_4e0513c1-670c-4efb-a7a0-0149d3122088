﻿  core.cpp
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(882,22): error C2039: ' spotted': não é um membro de 'unknowncheats::ccs_player_pawn'
  (compilando o arquivo fonte 'fhook/core/core.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(220,8):
      consulte a declaração de 'unknowncheats::ccs_player_pawn'
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(887,36): error C2440: '=': não é possível converter de 'int' para 'unknowncheats::cs_weapon_type'
  (compilando o arquivo fonte 'fhook/core/core.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(887,36):
      Conversão para tipo de enumeração requer uma conversão explícita (static_cast, conversão C-style ou conversão function-style entre parênteses)
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(888,4): error C2664: 'errno_t strcpy_s(char *,rsize_t,const char *)': não é possível converter um argumento 1 de 'std::string' em 'char *'
  (compilando o arquivo fonte 'fhook/core/core.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(888,30):
      Nenhum operador de conversão definida pelo usuário disponível que possa realizar esta conversão, ou o operador não pode ser chamado
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\string.h(32,30):
      consulte a declaração de 'strcpy_s'
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\string.h(124,1):
      poderia ser 'errno_t strcpy_s(char (&)[_Size],const char *) noexcept'
          C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(888,4):
          'errno_t strcpy_s(char (&)[_Size],const char *) noexcept': espera argumentos 2 - 3 fornecido
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(888,4):
      ao tentar corresponder a lista de argumentos '(std::string, size_t, const char [8])'
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(890,22): error C2039: ' velocity': não é um membro de 'unknowncheats::ccs_player_pawn'
  (compilando o arquivo fonte 'fhook/core/core.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(220,8):
      consulte a declaração de 'unknowncheats::ccs_player_pawn'
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(891,22): error C2039: ' inacuracy': não é um membro de 'unknowncheats::ccs_player_pawn'
  (compilando o arquivo fonte 'fhook/core/core.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(220,8):
      consulte a declaração de 'unknowncheats::ccs_player_pawn'
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(893,22): error C2039: ' postprocess': não é um membro de 'unknowncheats::ccs_player_pawn'
  (compilando o arquivo fonte 'fhook/core/core.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(220,8):
      consulte a declaração de 'unknowncheats::ccs_player_pawn'
  
  ctx.cpp
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(882,22): error C2039: ' spotted': não é um membro de 'unknowncheats::ccs_player_pawn'
  (compilando o arquivo fonte 'fhook/core/ctx/ctx.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(220,8):
      consulte a declaração de 'unknowncheats::ccs_player_pawn'
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(887,36): error C2440: '=': não é possível converter de 'int' para 'unknowncheats::cs_weapon_type'
  (compilando o arquivo fonte 'fhook/core/ctx/ctx.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(887,36):
      Conversão para tipo de enumeração requer uma conversão explícita (static_cast, conversão C-style ou conversão function-style entre parênteses)
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(888,4): error C2664: 'errno_t strcpy_s(char *,rsize_t,const char *)': não é possível converter um argumento 1 de 'std::string' em 'char *'
  (compilando o arquivo fonte 'fhook/core/ctx/ctx.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(888,30):
      Nenhum operador de conversão definida pelo usuário disponível que possa realizar esta conversão, ou o operador não pode ser chamado
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\string.h(32,30):
      consulte a declaração de 'strcpy_s'
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\string.h(124,1):
      poderia ser 'errno_t strcpy_s(char (&)[_Size],const char *) noexcept'
          C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(888,4):
          'errno_t strcpy_s(char (&)[_Size],const char *) noexcept': espera argumentos 2 - 3 fornecido
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(888,4):
      ao tentar corresponder a lista de argumentos '(std::string, size_t, const char [8])'
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(890,22): error C2039: ' velocity': não é um membro de 'unknowncheats::ccs_player_pawn'
  (compilando o arquivo fonte 'fhook/core/ctx/ctx.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(220,8):
      consulte a declaração de 'unknowncheats::ccs_player_pawn'
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(891,22): error C2039: ' inacuracy': não é um membro de 'unknowncheats::ccs_player_pawn'
  (compilando o arquivo fonte 'fhook/core/ctx/ctx.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(220,8):
      consulte a declaração de 'unknowncheats::ccs_player_pawn'
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(893,22): error C2039: ' postprocess': não é um membro de 'unknowncheats::ccs_player_pawn'
  (compilando o arquivo fonte 'fhook/core/ctx/ctx.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(220,8):
      consulte a declaração de 'unknowncheats::ccs_player_pawn'
  
  cfg.cpp
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(882,22): error C2039: ' spotted': não é um membro de 'unknowncheats::ccs_player_pawn'
  (compilando o arquivo fonte 'fhook/framework/config/cfg.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(220,8):
      consulte a declaração de 'unknowncheats::ccs_player_pawn'
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(887,36): error C2440: '=': não é possível converter de 'int' para 'unknowncheats::cs_weapon_type'
  (compilando o arquivo fonte 'fhook/framework/config/cfg.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(887,36):
      Conversão para tipo de enumeração requer uma conversão explícita (static_cast, conversão C-style ou conversão function-style entre parênteses)
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(888,4): error C2664: 'errno_t strcpy_s(char *,rsize_t,const char *)': não é possível converter um argumento 1 de 'std::string' em 'char *'
  (compilando o arquivo fonte 'fhook/framework/config/cfg.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(888,30):
      Nenhum operador de conversão definida pelo usuário disponível que possa realizar esta conversão, ou o operador não pode ser chamado
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\string.h(32,30):
      consulte a declaração de 'strcpy_s'
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\string.h(124,1):
      poderia ser 'errno_t strcpy_s(char (&)[_Size],const char *) noexcept'
          C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(888,4):
          'errno_t strcpy_s(char (&)[_Size],const char *) noexcept': espera argumentos 2 - 3 fornecido
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(888,4):
      ao tentar corresponder a lista de argumentos '(std::string, size_t, const char [8])'
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(890,22): error C2039: ' velocity': não é um membro de 'unknowncheats::ccs_player_pawn'
  (compilando o arquivo fonte 'fhook/framework/config/cfg.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(220,8):
      consulte a declaração de 'unknowncheats::ccs_player_pawn'
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(891,22): error C2039: ' inacuracy': não é um membro de 'unknowncheats::ccs_player_pawn'
  (compilando o arquivo fonte 'fhook/framework/config/cfg.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(220,8):
      consulte a declaração de 'unknowncheats::ccs_player_pawn'
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(893,22): error C2039: ' postprocess': não é um membro de 'unknowncheats::ccs_player_pawn'
  (compilando o arquivo fonte 'fhook/framework/config/cfg.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(220,8):
      consulte a declaração de 'unknowncheats::ccs_player_pawn'
  
  framework.cpp
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(882,22): error C2039: ' spotted': não é um membro de 'unknowncheats::ccs_player_pawn'
  (compilando o arquivo fonte 'fhook/framework/framework.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(220,8):
      consulte a declaração de 'unknowncheats::ccs_player_pawn'
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(887,36): error C2440: '=': não é possível converter de 'int' para 'unknowncheats::cs_weapon_type'
  (compilando o arquivo fonte 'fhook/framework/framework.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(887,36):
      Conversão para tipo de enumeração requer uma conversão explícita (static_cast, conversão C-style ou conversão function-style entre parênteses)
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(888,4): error C2664: 'errno_t strcpy_s(char *,rsize_t,const char *)': não é possível converter um argumento 1 de 'std::string' em 'char *'
  (compilando o arquivo fonte 'fhook/framework/framework.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(888,30):
      Nenhum operador de conversão definida pelo usuário disponível que possa realizar esta conversão, ou o operador não pode ser chamado
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\string.h(32,30):
      consulte a declaração de 'strcpy_s'
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\string.h(124,1):
      poderia ser 'errno_t strcpy_s(char (&)[_Size],const char *) noexcept'
          C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(888,4):
          'errno_t strcpy_s(char (&)[_Size],const char *) noexcept': espera argumentos 2 - 3 fornecido
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(888,4):
      ao tentar corresponder a lista de argumentos '(std::string, size_t, const char [8])'
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(890,22): error C2039: ' velocity': não é um membro de 'unknowncheats::ccs_player_pawn'
  (compilando o arquivo fonte 'fhook/framework/framework.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(220,8):
      consulte a declaração de 'unknowncheats::ccs_player_pawn'
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(891,22): error C2039: ' inacuracy': não é um membro de 'unknowncheats::ccs_player_pawn'
  (compilando o arquivo fonte 'fhook/framework/framework.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(220,8):
      consulte a declaração de 'unknowncheats::ccs_player_pawn'
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(893,22): error C2039: ' postprocess': não é um membro de 'unknowncheats::ccs_player_pawn'
  (compilando o arquivo fonte 'fhook/framework/framework.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(220,8):
      consulte a declaração de 'unknowncheats::ccs_player_pawn'
  
  hacks_ctx.cpp
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(882,22): error C2039: ' spotted': não é um membro de 'unknowncheats::ccs_player_pawn'
  (compilando o arquivo fonte 'fhook/hacks/ctx/hacks_ctx.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(220,8):
      consulte a declaração de 'unknowncheats::ccs_player_pawn'
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(887,36): error C2440: '=': não é possível converter de 'int' para 'unknowncheats::cs_weapon_type'
  (compilando o arquivo fonte 'fhook/hacks/ctx/hacks_ctx.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(887,36):
      Conversão para tipo de enumeração requer uma conversão explícita (static_cast, conversão C-style ou conversão function-style entre parênteses)
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(888,4): error C2664: 'errno_t strcpy_s(char *,rsize_t,const char *)': não é possível converter um argumento 1 de 'std::string' em 'char *'
  (compilando o arquivo fonte 'fhook/hacks/ctx/hacks_ctx.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(888,30):
      Nenhum operador de conversão definida pelo usuário disponível que possa realizar esta conversão, ou o operador não pode ser chamado
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\string.h(32,30):
      consulte a declaração de 'strcpy_s'
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\string.h(124,1):
      poderia ser 'errno_t strcpy_s(char (&)[_Size],const char *) noexcept'
          C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(888,4):
          'errno_t strcpy_s(char (&)[_Size],const char *) noexcept': espera argumentos 2 - 3 fornecido
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(888,4):
      ao tentar corresponder a lista de argumentos '(std::string, size_t, const char [8])'
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(890,22): error C2039: ' velocity': não é um membro de 'unknowncheats::ccs_player_pawn'
  (compilando o arquivo fonte 'fhook/hacks/ctx/hacks_ctx.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(220,8):
      consulte a declaração de 'unknowncheats::ccs_player_pawn'
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(891,22): error C2039: ' inacuracy': não é um membro de 'unknowncheats::ccs_player_pawn'
  (compilando o arquivo fonte 'fhook/hacks/ctx/hacks_ctx.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(220,8):
      consulte a declaração de 'unknowncheats::ccs_player_pawn'
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(893,22): error C2039: ' postprocess': não é um membro de 'unknowncheats::ccs_player_pawn'
  (compilando o arquivo fonte 'fhook/hacks/ctx/hacks_ctx.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(220,8):
      consulte a declaração de 'unknowncheats::ccs_player_pawn'
  
  esp.cpp
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(882,22): error C2039: ' spotted': não é um membro de 'unknowncheats::ccs_player_pawn'
  (compilando o arquivo fonte 'fhook/hacks/features/esp/esp.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(220,8):
      consulte a declaração de 'unknowncheats::ccs_player_pawn'
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(887,36): error C2440: '=': não é possível converter de 'int' para 'unknowncheats::cs_weapon_type'
  (compilando o arquivo fonte 'fhook/hacks/features/esp/esp.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(887,36):
      Conversão para tipo de enumeração requer uma conversão explícita (static_cast, conversão C-style ou conversão function-style entre parênteses)
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(888,4): error C2664: 'errno_t strcpy_s(char *,rsize_t,const char *)': não é possível converter um argumento 1 de 'std::string' em 'char *'
  (compilando o arquivo fonte 'fhook/hacks/features/esp/esp.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(888,30):
      Nenhum operador de conversão definida pelo usuário disponível que possa realizar esta conversão, ou o operador não pode ser chamado
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\string.h(32,30):
      consulte a declaração de 'strcpy_s'
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\string.h(124,1):
      poderia ser 'errno_t strcpy_s(char (&)[_Size],const char *) noexcept'
          C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(888,4):
          'errno_t strcpy_s(char (&)[_Size],const char *) noexcept': espera argumentos 2 - 3 fornecido
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(888,4):
      ao tentar corresponder a lista de argumentos '(std::string, size_t, const char [8])'
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(890,22): error C2039: ' velocity': não é um membro de 'unknowncheats::ccs_player_pawn'
  (compilando o arquivo fonte 'fhook/hacks/features/esp/esp.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(220,8):
      consulte a declaração de 'unknowncheats::ccs_player_pawn'
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(891,22): error C2039: ' inacuracy': não é um membro de 'unknowncheats::ccs_player_pawn'
  (compilando o arquivo fonte 'fhook/hacks/features/esp/esp.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(220,8):
      consulte a declaração de 'unknowncheats::ccs_player_pawn'
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(893,22): error C2039: ' postprocess': não é um membro de 'unknowncheats::ccs_player_pawn'
  (compilando o arquivo fonte 'fhook/hacks/features/esp/esp.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(220,8):
      consulte a declaração de 'unknowncheats::ccs_player_pawn'
  
  flash_builder.cpp
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(882,22): error C2039: ' spotted': não é um membro de 'unknowncheats::ccs_player_pawn'
  (compilando o arquivo fonte 'fhook/hacks/features/flash_builder/flash_builder.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(220,8):
      consulte a declaração de 'unknowncheats::ccs_player_pawn'
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(887,36): error C2440: '=': não é possível converter de 'int' para 'unknowncheats::cs_weapon_type'
  (compilando o arquivo fonte 'fhook/hacks/features/flash_builder/flash_builder.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(887,36):
      Conversão para tipo de enumeração requer uma conversão explícita (static_cast, conversão C-style ou conversão function-style entre parênteses)
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(888,4): error C2664: 'errno_t strcpy_s(char *,rsize_t,const char *)': não é possível converter um argumento 1 de 'std::string' em 'char *'
  (compilando o arquivo fonte 'fhook/hacks/features/flash_builder/flash_builder.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(888,30):
      Nenhum operador de conversão definida pelo usuário disponível que possa realizar esta conversão, ou o operador não pode ser chamado
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\string.h(32,30):
      consulte a declaração de 'strcpy_s'
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\string.h(124,1):
      poderia ser 'errno_t strcpy_s(char (&)[_Size],const char *) noexcept'
          C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(888,4):
          'errno_t strcpy_s(char (&)[_Size],const char *) noexcept': espera argumentos 2 - 3 fornecido
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(888,4):
      ao tentar corresponder a lista de argumentos '(std::string, size_t, const char [8])'
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(890,22): error C2039: ' velocity': não é um membro de 'unknowncheats::ccs_player_pawn'
  (compilando o arquivo fonte 'fhook/hacks/features/flash_builder/flash_builder.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(220,8):
      consulte a declaração de 'unknowncheats::ccs_player_pawn'
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(891,22): error C2039: ' inacuracy': não é um membro de 'unknowncheats::ccs_player_pawn'
  (compilando o arquivo fonte 'fhook/hacks/features/flash_builder/flash_builder.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(220,8):
      consulte a declaração de 'unknowncheats::ccs_player_pawn'
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(893,22): error C2039: ' postprocess': não é um membro de 'unknowncheats::ccs_player_pawn'
  (compilando o arquivo fonte 'fhook/hacks/features/flash_builder/flash_builder.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(220,8):
      consulte a declaração de 'unknowncheats::ccs_player_pawn'
  
  grenades.cpp
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(882,22): error C2039: ' spotted': não é um membro de 'unknowncheats::ccs_player_pawn'
  (compilando o arquivo fonte 'fhook/hacks/features/grenade/grenades.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(220,8):
      consulte a declaração de 'unknowncheats::ccs_player_pawn'
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(887,36): error C2440: '=': não é possível converter de 'int' para 'unknowncheats::cs_weapon_type'
  (compilando o arquivo fonte 'fhook/hacks/features/grenade/grenades.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(887,36):
      Conversão para tipo de enumeração requer uma conversão explícita (static_cast, conversão C-style ou conversão function-style entre parênteses)
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(888,4): error C2664: 'errno_t strcpy_s(char *,rsize_t,const char *)': não é possível converter um argumento 1 de 'std::string' em 'char *'
  (compilando o arquivo fonte 'fhook/hacks/features/grenade/grenades.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(888,30):
      Nenhum operador de conversão definida pelo usuário disponível que possa realizar esta conversão, ou o operador não pode ser chamado
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\string.h(32,30):
      consulte a declaração de 'strcpy_s'
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\string.h(124,1):
      poderia ser 'errno_t strcpy_s(char (&)[_Size],const char *) noexcept'
          C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(888,4):
          'errno_t strcpy_s(char (&)[_Size],const char *) noexcept': espera argumentos 2 - 3 fornecido
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(888,4):
      ao tentar corresponder a lista de argumentos '(std::string, size_t, const char [8])'
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(890,22): error C2039: ' velocity': não é um membro de 'unknowncheats::ccs_player_pawn'
  (compilando o arquivo fonte 'fhook/hacks/features/grenade/grenades.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(220,8):
      consulte a declaração de 'unknowncheats::ccs_player_pawn'
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(891,22): error C2039: ' inacuracy': não é um membro de 'unknowncheats::ccs_player_pawn'
  (compilando o arquivo fonte 'fhook/hacks/features/grenade/grenades.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(220,8):
      consulte a declaração de 'unknowncheats::ccs_player_pawn'
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(893,22): error C2039: ' postprocess': não é um membro de 'unknowncheats::ccs_player_pawn'
  (compilando o arquivo fonte 'fhook/hacks/features/grenade/grenades.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(220,8):
      consulte a declaração de 'unknowncheats::ccs_player_pawn'
  
  legitbot.cpp
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(882,22): error C2039: ' spotted': não é um membro de 'unknowncheats::ccs_player_pawn'
  (compilando o arquivo fonte 'fhook/hacks/features/legitbot/legitbot.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(220,8):
      consulte a declaração de 'unknowncheats::ccs_player_pawn'
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(887,36): error C2440: '=': não é possível converter de 'int' para 'unknowncheats::cs_weapon_type'
  (compilando o arquivo fonte 'fhook/hacks/features/legitbot/legitbot.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(887,36):
      Conversão para tipo de enumeração requer uma conversão explícita (static_cast, conversão C-style ou conversão function-style entre parênteses)
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(888,4): error C2664: 'errno_t strcpy_s(char *,rsize_t,const char *)': não é possível converter um argumento 1 de 'std::string' em 'char *'
  (compilando o arquivo fonte 'fhook/hacks/features/legitbot/legitbot.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(888,30):
      Nenhum operador de conversão definida pelo usuário disponível que possa realizar esta conversão, ou o operador não pode ser chamado
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\string.h(32,30):
      consulte a declaração de 'strcpy_s'
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\string.h(124,1):
      poderia ser 'errno_t strcpy_s(char (&)[_Size],const char *) noexcept'
          C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(888,4):
          'errno_t strcpy_s(char (&)[_Size],const char *) noexcept': espera argumentos 2 - 3 fornecido
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(888,4):
      ao tentar corresponder a lista de argumentos '(std::string, size_t, const char [8])'
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(890,22): error C2039: ' velocity': não é um membro de 'unknowncheats::ccs_player_pawn'
  (compilando o arquivo fonte 'fhook/hacks/features/legitbot/legitbot.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(220,8):
      consulte a declaração de 'unknowncheats::ccs_player_pawn'
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(891,22): error C2039: ' inacuracy': não é um membro de 'unknowncheats::ccs_player_pawn'
  (compilando o arquivo fonte 'fhook/hacks/features/legitbot/legitbot.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(220,8):
      consulte a declaração de 'unknowncheats::ccs_player_pawn'
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(893,22): error C2039: ' postprocess': não é um membro de 'unknowncheats::ccs_player_pawn'
  (compilando o arquivo fonte 'fhook/hacks/features/legitbot/legitbot.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(220,8):
      consulte a declaração de 'unknowncheats::ccs_player_pawn'
  
  ragebot.cpp
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(882,22): error C2039: ' spotted': não é um membro de 'unknowncheats::ccs_player_pawn'
  (compilando o arquivo fonte 'fhook/hacks/features/ragebot/ragebot.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(220,8):
      consulte a declaração de 'unknowncheats::ccs_player_pawn'
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(887,36): error C2440: '=': não é possível converter de 'int' para 'unknowncheats::cs_weapon_type'
  (compilando o arquivo fonte 'fhook/hacks/features/ragebot/ragebot.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(887,36):
      Conversão para tipo de enumeração requer uma conversão explícita (static_cast, conversão C-style ou conversão function-style entre parênteses)
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(888,4): error C2664: 'errno_t strcpy_s(char *,rsize_t,const char *)': não é possível converter um argumento 1 de 'std::string' em 'char *'
  (compilando o arquivo fonte 'fhook/hacks/features/ragebot/ragebot.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(888,30):
      Nenhum operador de conversão definida pelo usuário disponível que possa realizar esta conversão, ou o operador não pode ser chamado
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\string.h(32,30):
      consulte a declaração de 'strcpy_s'
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\string.h(124,1):
      poderia ser 'errno_t strcpy_s(char (&)[_Size],const char *) noexcept'
          C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(888,4):
          'errno_t strcpy_s(char (&)[_Size],const char *) noexcept': espera argumentos 2 - 3 fornecido
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(888,4):
      ao tentar corresponder a lista de argumentos '(std::string, size_t, const char [8])'
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(890,22): error C2039: ' velocity': não é um membro de 'unknowncheats::ccs_player_pawn'
  (compilando o arquivo fonte 'fhook/hacks/features/ragebot/ragebot.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(220,8):
      consulte a declaração de 'unknowncheats::ccs_player_pawn'
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(891,22): error C2039: ' inacuracy': não é um membro de 'unknowncheats::ccs_player_pawn'
  (compilando o arquivo fonte 'fhook/hacks/features/ragebot/ragebot.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(220,8):
      consulte a declaração de 'unknowncheats::ccs_player_pawn'
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(893,22): error C2039: ' postprocess': não é um membro de 'unknowncheats::ccs_player_pawn'
  (compilando o arquivo fonte 'fhook/hacks/features/ragebot/ragebot.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(220,8):
      consulte a declaração de 'unknowncheats::ccs_player_pawn'
  
  shots.cpp
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(882,22): error C2039: ' spotted': não é um membro de 'unknowncheats::ccs_player_pawn'
  (compilando o arquivo fonte 'fhook/hacks/features/shots/shots.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(220,8):
      consulte a declaração de 'unknowncheats::ccs_player_pawn'
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(887,36): error C2440: '=': não é possível converter de 'int' para 'unknowncheats::cs_weapon_type'
  (compilando o arquivo fonte 'fhook/hacks/features/shots/shots.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(887,36):
      Conversão para tipo de enumeração requer uma conversão explícita (static_cast, conversão C-style ou conversão function-style entre parênteses)
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(888,4): error C2664: 'errno_t strcpy_s(char *,rsize_t,const char *)': não é possível converter um argumento 1 de 'std::string' em 'char *'
  (compilando o arquivo fonte 'fhook/hacks/features/shots/shots.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(888,30):
      Nenhum operador de conversão definida pelo usuário disponível que possa realizar esta conversão, ou o operador não pode ser chamado
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\string.h(32,30):
      consulte a declaração de 'strcpy_s'
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\string.h(124,1):
      poderia ser 'errno_t strcpy_s(char (&)[_Size],const char *) noexcept'
          C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(888,4):
          'errno_t strcpy_s(char (&)[_Size],const char *) noexcept': espera argumentos 2 - 3 fornecido
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(888,4):
      ao tentar corresponder a lista de argumentos '(std::string, size_t, const char [8])'
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(890,22): error C2039: ' velocity': não é um membro de 'unknowncheats::ccs_player_pawn'
  (compilando o arquivo fonte 'fhook/hacks/features/shots/shots.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(220,8):
      consulte a declaração de 'unknowncheats::ccs_player_pawn'
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(891,22): error C2039: ' inacuracy': não é um membro de 'unknowncheats::ccs_player_pawn'
  (compilando o arquivo fonte 'fhook/hacks/features/shots/shots.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(220,8):
      consulte a declaração de 'unknowncheats::ccs_player_pawn'
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(893,22): error C2039: ' postprocess': não é um membro de 'unknowncheats::ccs_player_pawn'
  (compilando o arquivo fonte 'fhook/hacks/features/shots/shots.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(220,8):
      consulte a declaração de 'unknowncheats::ccs_player_pawn'
  
  sound.cpp
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(882,22): error C2039: ' spotted': não é um membro de 'unknowncheats::ccs_player_pawn'
  (compilando o arquivo fonte 'fhook/hacks/features/sound_system/sound.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(220,8):
      consulte a declaração de 'unknowncheats::ccs_player_pawn'
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(887,36): error C2440: '=': não é possível converter de 'int' para 'unknowncheats::cs_weapon_type'
  (compilando o arquivo fonte 'fhook/hacks/features/sound_system/sound.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(887,36):
      Conversão para tipo de enumeração requer uma conversão explícita (static_cast, conversão C-style ou conversão function-style entre parênteses)
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(888,4): error C2664: 'errno_t strcpy_s(char *,rsize_t,const char *)': não é possível converter um argumento 1 de 'std::string' em 'char *'
  (compilando o arquivo fonte 'fhook/hacks/features/sound_system/sound.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(888,30):
      Nenhum operador de conversão definida pelo usuário disponível que possa realizar esta conversão, ou o operador não pode ser chamado
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\string.h(32,30):
      consulte a declaração de 'strcpy_s'
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\string.h(124,1):
      poderia ser 'errno_t strcpy_s(char (&)[_Size],const char *) noexcept'
          C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(888,4):
          'errno_t strcpy_s(char (&)[_Size],const char *) noexcept': espera argumentos 2 - 3 fornecido
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(888,4):
      ao tentar corresponder a lista de argumentos '(std::string, size_t, const char [8])'
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(890,22): error C2039: ' velocity': não é um membro de 'unknowncheats::ccs_player_pawn'
  (compilando o arquivo fonte 'fhook/hacks/features/sound_system/sound.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(220,8):
      consulte a declaração de 'unknowncheats::ccs_player_pawn'
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(891,22): error C2039: ' inacuracy': não é um membro de 'unknowncheats::ccs_player_pawn'
  (compilando o arquivo fonte 'fhook/hacks/features/sound_system/sound.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(220,8):
      consulte a declaração de 'unknowncheats::ccs_player_pawn'
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(893,22): error C2039: ' postprocess': não é um membro de 'unknowncheats::ccs_player_pawn'
  (compilando o arquivo fonte 'fhook/hacks/features/sound_system/sound.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(220,8):
      consulte a declaração de 'unknowncheats::ccs_player_pawn'
  
  triggerbot.cpp
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(882,22): error C2039: ' spotted': não é um membro de 'unknowncheats::ccs_player_pawn'
  (compilando o arquivo fonte 'fhook/hacks/features/triggerbot/triggerbot.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(220,8):
      consulte a declaração de 'unknowncheats::ccs_player_pawn'
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(887,36): error C2440: '=': não é possível converter de 'int' para 'unknowncheats::cs_weapon_type'
  (compilando o arquivo fonte 'fhook/hacks/features/triggerbot/triggerbot.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(887,36):
      Conversão para tipo de enumeração requer uma conversão explícita (static_cast, conversão C-style ou conversão function-style entre parênteses)
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(888,4): error C2664: 'errno_t strcpy_s(char *,rsize_t,const char *)': não é possível converter um argumento 1 de 'std::string' em 'char *'
  (compilando o arquivo fonte 'fhook/hacks/features/triggerbot/triggerbot.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(888,30):
      Nenhum operador de conversão definida pelo usuário disponível que possa realizar esta conversão, ou o operador não pode ser chamado
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\string.h(32,30):
      consulte a declaração de 'strcpy_s'
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\string.h(124,1):
      poderia ser 'errno_t strcpy_s(char (&)[_Size],const char *) noexcept'
          C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(888,4):
          'errno_t strcpy_s(char (&)[_Size],const char *) noexcept': espera argumentos 2 - 3 fornecido
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(888,4):
      ao tentar corresponder a lista de argumentos '(std::string, size_t, const char [8])'
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(890,22): error C2039: ' velocity': não é um membro de 'unknowncheats::ccs_player_pawn'
  (compilando o arquivo fonte 'fhook/hacks/features/triggerbot/triggerbot.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(220,8):
      consulte a declaração de 'unknowncheats::ccs_player_pawn'
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(891,22): error C2039: ' inacuracy': não é um membro de 'unknowncheats::ccs_player_pawn'
  (compilando o arquivo fonte 'fhook/hacks/features/triggerbot/triggerbot.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(220,8):
      consulte a declaração de 'unknowncheats::ccs_player_pawn'
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(893,22): error C2039: ' postprocess': não é um membro de 'unknowncheats::ccs_player_pawn'
  (compilando o arquivo fonte 'fhook/hacks/features/triggerbot/triggerbot.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(220,8):
      consulte a declaração de 'unknowncheats::ccs_player_pawn'
  
  input_system.cpp
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(882,22): error C2039: ' spotted': não é um membro de 'unknowncheats::ccs_player_pawn'
  (compilando o arquivo fonte 'fhook/sdk/input_system/input_system.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(220,8):
      consulte a declaração de 'unknowncheats::ccs_player_pawn'
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(887,36): error C2440: '=': não é possível converter de 'int' para 'unknowncheats::cs_weapon_type'
  (compilando o arquivo fonte 'fhook/sdk/input_system/input_system.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(887,36):
      Conversão para tipo de enumeração requer uma conversão explícita (static_cast, conversão C-style ou conversão function-style entre parênteses)
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(888,4): error C2664: 'errno_t strcpy_s(char *,rsize_t,const char *)': não é possível converter um argumento 1 de 'std::string' em 'char *'
  (compilando o arquivo fonte 'fhook/sdk/input_system/input_system.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(888,30):
      Nenhum operador de conversão definida pelo usuário disponível que possa realizar esta conversão, ou o operador não pode ser chamado
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\string.h(32,30):
      consulte a declaração de 'strcpy_s'
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\string.h(124,1):
      poderia ser 'errno_t strcpy_s(char (&)[_Size],const char *) noexcept'
          C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(888,4):
          'errno_t strcpy_s(char (&)[_Size],const char *) noexcept': espera argumentos 2 - 3 fornecido
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(888,4):
      ao tentar corresponder a lista de argumentos '(std::string, size_t, const char [8])'
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(890,22): error C2039: ' velocity': não é um membro de 'unknowncheats::ccs_player_pawn'
  (compilando o arquivo fonte 'fhook/sdk/input_system/input_system.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(220,8):
      consulte a declaração de 'unknowncheats::ccs_player_pawn'
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(891,22): error C2039: ' inacuracy': não é um membro de 'unknowncheats::ccs_player_pawn'
  (compilando o arquivo fonte 'fhook/sdk/input_system/input_system.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(220,8):
      consulte a declaração de 'unknowncheats::ccs_player_pawn'
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(893,22): error C2039: ' postprocess': não é um membro de 'unknowncheats::ccs_player_pawn'
  (compilando o arquivo fonte 'fhook/sdk/input_system/input_system.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(220,8):
      consulte a declaração de 'unknowncheats::ccs_player_pawn'
  
  render.cpp
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(882,22): error C2039: ' spotted': não é um membro de 'unknowncheats::ccs_player_pawn'
  (compilando o arquivo fonte 'fhook/sdk/render/render.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(220,8):
      consulte a declaração de 'unknowncheats::ccs_player_pawn'
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(887,36): error C2440: '=': não é possível converter de 'int' para 'unknowncheats::cs_weapon_type'
  (compilando o arquivo fonte 'fhook/sdk/render/render.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(887,36):
      Conversão para tipo de enumeração requer uma conversão explícita (static_cast, conversão C-style ou conversão function-style entre parênteses)
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(888,4): error C2664: 'errno_t strcpy_s(char *,rsize_t,const char *)': não é possível converter um argumento 1 de 'std::string' em 'char *'
  (compilando o arquivo fonte 'fhook/sdk/render/render.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(888,30):
      Nenhum operador de conversão definida pelo usuário disponível que possa realizar esta conversão, ou o operador não pode ser chamado
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\string.h(32,30):
      consulte a declaração de 'strcpy_s'
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\string.h(124,1):
      poderia ser 'errno_t strcpy_s(char (&)[_Size],const char *) noexcept'
          C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(888,4):
          'errno_t strcpy_s(char (&)[_Size],const char *) noexcept': espera argumentos 2 - 3 fornecido
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(888,4):
      ao tentar corresponder a lista de argumentos '(std::string, size_t, const char [8])'
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(890,22): error C2039: ' velocity': não é um membro de 'unknowncheats::ccs_player_pawn'
  (compilando o arquivo fonte 'fhook/sdk/render/render.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(220,8):
      consulte a declaração de 'unknowncheats::ccs_player_pawn'
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(891,22): error C2039: ' inacuracy': não é um membro de 'unknowncheats::ccs_player_pawn'
  (compilando o arquivo fonte 'fhook/sdk/render/render.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(220,8):
      consulte a declaração de 'unknowncheats::ccs_player_pawn'
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(893,22): error C2039: ' postprocess': não é um membro de 'unknowncheats::ccs_player_pawn'
  (compilando o arquivo fonte 'fhook/sdk/render/render.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(220,8):
      consulte a declaração de 'unknowncheats::ccs_player_pawn'
  
  reversed_funcs.cpp
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(882,22): error C2039: ' spotted': não é um membro de 'unknowncheats::ccs_player_pawn'
  (compilando o arquivo fonte 'fhook/sdk/reverse/reversed_funcs.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(220,8):
      consulte a declaração de 'unknowncheats::ccs_player_pawn'
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(887,36): error C2440: '=': não é possível converter de 'int' para 'unknowncheats::cs_weapon_type'
  (compilando o arquivo fonte 'fhook/sdk/reverse/reversed_funcs.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(887,36):
      Conversão para tipo de enumeração requer uma conversão explícita (static_cast, conversão C-style ou conversão function-style entre parênteses)
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(888,4): error C2664: 'errno_t strcpy_s(char *,rsize_t,const char *)': não é possível converter um argumento 1 de 'std::string' em 'char *'
  (compilando o arquivo fonte 'fhook/sdk/reverse/reversed_funcs.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(888,30):
      Nenhum operador de conversão definida pelo usuário disponível que possa realizar esta conversão, ou o operador não pode ser chamado
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\string.h(32,30):
      consulte a declaração de 'strcpy_s'
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\string.h(124,1):
      poderia ser 'errno_t strcpy_s(char (&)[_Size],const char *) noexcept'
          C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(888,4):
          'errno_t strcpy_s(char (&)[_Size],const char *) noexcept': espera argumentos 2 - 3 fornecido
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(888,4):
      ao tentar corresponder a lista de argumentos '(std::string, size_t, const char [8])'
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(890,22): error C2039: ' velocity': não é um membro de 'unknowncheats::ccs_player_pawn'
  (compilando o arquivo fonte 'fhook/sdk/reverse/reversed_funcs.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(220,8):
      consulte a declaração de 'unknowncheats::ccs_player_pawn'
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(891,22): error C2039: ' inacuracy': não é um membro de 'unknowncheats::ccs_player_pawn'
  (compilando o arquivo fonte 'fhook/sdk/reverse/reversed_funcs.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(220,8):
      consulte a declaração de 'unknowncheats::ccs_player_pawn'
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(893,22): error C2039: ' postprocess': não é um membro de 'unknowncheats::ccs_player_pawn'
  (compilando o arquivo fonte 'fhook/sdk/reverse/reversed_funcs.cpp')
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(220,8):
      consulte a declaração de 'unknowncheats::ccs_player_pawn'
  

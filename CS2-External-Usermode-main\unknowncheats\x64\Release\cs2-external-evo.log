﻿  hacks_ctx.cpp
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\hacks\ctx\hacks_ctx.cpp(364,24): error C2039: ' team_id': não é um membro de 'unknowncheats::ccs_player_pawn'
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(220,8):
      consulte a declaração de 'unknowncheats::ccs_player_pawn'
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\hacks\ctx\hacks_ctx.cpp(448,28): error C2039: ' team_id': não é um membro de 'unknowncheats::ccs_player_pawn'
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(220,8):
      consulte a declaração de 'unknowncheats::ccs_player_pawn'
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\hacks\ctx\hacks_ctx.cpp(450,35): error C2039: ' team_id': não é um membro de 'unknowncheats::ccs_player_pawn'
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(220,8):
      consulte a declaração de 'unknowncheats::ccs_player_pawn'
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\hacks\ctx\hacks_ctx.cpp(476,89): error C2039: ' team_id': não é um membro de 'unknowncheats::ccs_player_pawn'
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(220,8):
      consulte a declaração de 'unknowncheats::ccs_player_pawn'
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\hacks\ctx\hacks_ctx.cpp(476,25): warning C4473: 'sprintf_s' : não há argumentos suficientes transmitidos para a cadeia de caracteres de formato
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\hacks\ctx\hacks_ctx.cpp(476,25):
      espaços reservados e seus parâmetros esperam 2 argumentos variadic, mas 1 foram fornecidos
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\hacks\ctx\hacks_ctx.cpp(476,25):
      o argumento variadic 2 ausente é requerido pela cadeia de formato '%d'
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\hacks\ctx\hacks_ctx.cpp(493,141): error C2039: ' team_id': não é um membro de 'unknowncheats::ccs_player_pawn'
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(220,8):
      consulte a declaração de 'unknowncheats::ccs_player_pawn'
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\hacks\ctx\hacks_ctx.cpp(493,12): warning C4473: 'printf' : não há argumentos suficientes transmitidos para a cadeia de caracteres de formato
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\hacks\ctx\hacks_ctx.cpp(493,12):
      espaços reservados e seus parâmetros esperam 3 argumentos variadic, mas 2 foram fornecidos
      C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\hacks\ctx\hacks_ctx.cpp(493,12):
      o argumento variadic 3 ausente é requerido pela cadeia de formato '%d'
  

﻿  core.cpp
  ctx.cpp
  cfg.cpp
  framework.cpp
  hacks_ctx.cpp
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\hacks\ctx\hacks_ctx.cpp(408,7): warning C4101: 'total_hits': variável local não referenciada
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\hacks\ctx\hacks_ctx.cpp(427,6): warning C4101: 'smth4': variável local não referenciada
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\hacks\ctx\hacks_ctx.cpp(413,12): warning C4101: 'bullet_services': variável local não referenciada
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\hacks\ctx\hacks_ctx.cpp(426,8): warning C4101: 'smth3': variável local não referenciada
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\hacks\ctx\hacks_ctx.cpp(425,7): warning C4101: 'smth': variável local não referenciada
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\hacks\ctx\hacks_ctx.cpp(409,8): warning C4101: 'minexp': variável local não referenciada
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\hacks\ctx\hacks_ctx.cpp(337,7): warning C4101: 'smth4': variável local não referenciada
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\hacks\ctx\hacks_ctx.cpp(338,11): warning C4101: 'smth2': variável local não referenciada
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\hacks\ctx\hacks_ctx.cpp(336,9): warning C4101: 'smth3': variável local não referenciada
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\hacks\ctx\hacks_ctx.cpp(335,8): warning C4101: 'smth': variável local não referenciada
  esp.cpp
  flash_builder.cpp
  grenades.cpp
  legitbot.cpp
  ragebot.cpp
  shots.cpp
  sound.cpp
  triggerbot.cpp
  input_system.cpp
  render.cpp
  reversed_funcs.cpp
LINK : warning LNK4098: defaultlib 'LIBCMT' conflita com uso de outras bibliotecas; use /NODEFAULTLIB:library
  Gerando código
  556 of 4062 functions (13.7%) were compiled, the rest were copied from previous compilation.
    0 functions were new in current compilation
    72 functions had inline decision re-evaluated but remain unchanged
  Finalizada a geração de código
  cs2-external-evo.vcxproj -> C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\x64\Release\unknowncheats.exe

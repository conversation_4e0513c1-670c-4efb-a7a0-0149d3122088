﻿  core.cpp
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(887,67): error C2838: 'WEAPON_NONE': nome qualificado inválido na declaração de membro
  (compilando o arquivo fonte 'fhook/core/core.cpp')
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(887,67): error C2065: 'WEAPON_NONE': identificador não declarado
  (compilando o arquivo fonte 'fhook/core/core.cpp')
  
  ctx.cpp
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(887,67): error C2838: 'WEAPON_NONE': nome qualificado inválido na declaração de membro
  (compilando o arquivo fonte 'fhook/core/ctx/ctx.cpp')
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(887,67): error C2065: 'WEAPON_NONE': identificador não declarado
  (compilando o arquivo fonte 'fhook/core/ctx/ctx.cpp')
  
  cfg.cpp
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(887,67): error C2838: 'WEAPON_NONE': nome qualificado inválido na declaração de membro
  (compilando o arquivo fonte 'fhook/framework/config/cfg.cpp')
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(887,67): error C2065: 'WEAPON_NONE': identificador não declarado
  (compilando o arquivo fonte 'fhook/framework/config/cfg.cpp')
  
  framework.cpp
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(887,67): error C2838: 'WEAPON_NONE': nome qualificado inválido na declaração de membro
  (compilando o arquivo fonte 'fhook/framework/framework.cpp')
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(887,67): error C2065: 'WEAPON_NONE': identificador não declarado
  (compilando o arquivo fonte 'fhook/framework/framework.cpp')
  
  hacks_ctx.cpp
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(887,67): error C2838: 'WEAPON_NONE': nome qualificado inválido na declaração de membro
  (compilando o arquivo fonte 'fhook/hacks/ctx/hacks_ctx.cpp')
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(887,67): error C2065: 'WEAPON_NONE': identificador não declarado
  (compilando o arquivo fonte 'fhook/hacks/ctx/hacks_ctx.cpp')
  
  esp.cpp
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(887,67): error C2838: 'WEAPON_NONE': nome qualificado inválido na declaração de membro
  (compilando o arquivo fonte 'fhook/hacks/features/esp/esp.cpp')
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(887,67): error C2065: 'WEAPON_NONE': identificador não declarado
  (compilando o arquivo fonte 'fhook/hacks/features/esp/esp.cpp')
  
  flash_builder.cpp
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(887,67): error C2838: 'WEAPON_NONE': nome qualificado inválido na declaração de membro
  (compilando o arquivo fonte 'fhook/hacks/features/flash_builder/flash_builder.cpp')
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(887,67): error C2065: 'WEAPON_NONE': identificador não declarado
  (compilando o arquivo fonte 'fhook/hacks/features/flash_builder/flash_builder.cpp')
  
  grenades.cpp
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(887,67): error C2838: 'WEAPON_NONE': nome qualificado inválido na declaração de membro
  (compilando o arquivo fonte 'fhook/hacks/features/grenade/grenades.cpp')
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(887,67): error C2065: 'WEAPON_NONE': identificador não declarado
  (compilando o arquivo fonte 'fhook/hacks/features/grenade/grenades.cpp')
  
  legitbot.cpp
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(887,67): error C2838: 'WEAPON_NONE': nome qualificado inválido na declaração de membro
  (compilando o arquivo fonte 'fhook/hacks/features/legitbot/legitbot.cpp')
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(887,67): error C2065: 'WEAPON_NONE': identificador não declarado
  (compilando o arquivo fonte 'fhook/hacks/features/legitbot/legitbot.cpp')
  
  ragebot.cpp
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(887,67): error C2838: 'WEAPON_NONE': nome qualificado inválido na declaração de membro
  (compilando o arquivo fonte 'fhook/hacks/features/ragebot/ragebot.cpp')
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(887,67): error C2065: 'WEAPON_NONE': identificador não declarado
  (compilando o arquivo fonte 'fhook/hacks/features/ragebot/ragebot.cpp')
  
  shots.cpp
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(887,67): error C2838: 'WEAPON_NONE': nome qualificado inválido na declaração de membro
  (compilando o arquivo fonte 'fhook/hacks/features/shots/shots.cpp')
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(887,67): error C2065: 'WEAPON_NONE': identificador não declarado
  (compilando o arquivo fonte 'fhook/hacks/features/shots/shots.cpp')
  
  sound.cpp
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(887,67): error C2838: 'WEAPON_NONE': nome qualificado inválido na declaração de membro
  (compilando o arquivo fonte 'fhook/hacks/features/sound_system/sound.cpp')
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(887,67): error C2065: 'WEAPON_NONE': identificador não declarado
  (compilando o arquivo fonte 'fhook/hacks/features/sound_system/sound.cpp')
  
  triggerbot.cpp
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(887,67): error C2838: 'WEAPON_NONE': nome qualificado inválido na declaração de membro
  (compilando o arquivo fonte 'fhook/hacks/features/triggerbot/triggerbot.cpp')
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(887,67): error C2065: 'WEAPON_NONE': identificador não declarado
  (compilando o arquivo fonte 'fhook/hacks/features/triggerbot/triggerbot.cpp')
  
  input_system.cpp
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(887,67): error C2838: 'WEAPON_NONE': nome qualificado inválido na declaração de membro
  (compilando o arquivo fonte 'fhook/sdk/input_system/input_system.cpp')
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(887,67): error C2065: 'WEAPON_NONE': identificador não declarado
  (compilando o arquivo fonte 'fhook/sdk/input_system/input_system.cpp')
  
  render.cpp
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(887,67): error C2838: 'WEAPON_NONE': nome qualificado inválido na declaração de membro
  (compilando o arquivo fonte 'fhook/sdk/render/render.cpp')
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(887,67): error C2065: 'WEAPON_NONE': identificador não declarado
  (compilando o arquivo fonte 'fhook/sdk/render/render.cpp')
  
  reversed_funcs.cpp
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(887,67): error C2838: 'WEAPON_NONE': nome qualificado inválido na declaração de membro
  (compilando o arquivo fonte 'fhook/sdk/reverse/reversed_funcs.cpp')
  
C:\Users\<USER>\Desktop\CS2-External-Usermode-main\CS2-External-Usermode-main\fhook\sdk\classes\entity.hpp(887,67): error C2065: 'WEAPON_NONE': identificador não declarado
  (compilando o arquivo fonte 'fhook/sdk/reverse/reversed_funcs.cpp')
  

ftfuzzer
========


ftfuzzer.cc
-----------

This file contains a target function  for FreeType fuzzing.  It can be
used   with    libFuzzer   (https://llvm.org/docs/LibFuzzer.html)   or
potentially any other similar fuzzer.

Usage:

  1. Build  `libfreetype.a' and  `ftfuzzer.cc' using  the most  recent
     clang compiler with these flags:

       # for fuzzer coverage feedback
       -fsanitize-coverage=edge,8bit-counters
       # for bug checking
       -fsanitize=address,signed-integer-overflow,shift

     You  also need  the header  files from  the `libarchive'  library
     (https://www.libarchive.org/)  for handling  tar files  (see file
     `ftmutator.cc' below for more).

  2. Link with `libFuzzer' (it contains `main') and `libarchive'.

  3. Run the fuzzer on some test corpus.

The exact flags and commands may vary.

  https://github.com/google/oss-fuzz/tree/master/projects/freetype2

There is a continuous fuzzing bot that runs ftfuzzer.

  https://oss-fuzz.com

(You need an account  to be able to see coverage  reports and the like
on oss-fuzz.com.)

Check the bot configuration for the most current settings.


ftmutator.cc
------------

FreeType has the  ability to `attach' auxiliary files to  a font file,
providing additional information.  The main usage is to load AFM files
for PostScript Type 1 fonts.

However, libFuzzer currently only supports  mutation of a single input
file.   For  this  reason,  `ftmutator.cc' contains  a  custom  fuzzer
mutator that uses an uncompressed tar  file archive as the input.  The
first file in  such a tarball gets  opened by FreeType as  a font, all
other files are treated as input for `FT_Attach_Stream'.

Compilation is similar to `ftfuzzer.c'.


runinput.cc
-----------

To run the target function on a set of input files, this file contains
a   convenience  `main'   function.   Link   it  with   `ftfuzzer.cc',
`libfreetype.a', and `libarchive' and run like

  ./a.out my_tests_inputs/*

----------------------------------------------------------------------

Copyright 2015-2018 by
David Turner, Robert Wilhelm, and Werner Lemberg.

This  file is  part of  the FreeType  project, and  may only  be used,
modified,  and distributed  under the  terms of  the  FreeType project
license,  LICENSE.TXT.  By  continuing to  use, modify,  or distribute
this file you  indicate that you have read  the license and understand
and accept it fully.


--- end of README ---

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"https://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>FreeType-2.9.1 API Reference</title>
<style type="text/css">
  a:link { color: #0000EF; }
  a:visited { color: #51188E; }
  a:hover { color: #FF0000; }

  body { font-family: Verdana, Geneva, Arial, Helvetica, serif;
         color: #000000;
         background: #FFFFFF;
         width: 87%;
         margin: auto; }

  div.section { width: 75%;
                margin: auto; }
  div.section hr { margin: 4ex 0 1ex 0; }
  div.section h4 { background-color: #EEEEFF;
                   font-size: medium;
                   font-style: oblique;
                   font-weight: bold;
                   margin: 3ex 0 1.5ex 9%;
                   padding: 0.3ex 0 0.3ex 1%; }
  div.section p { margin: 1.5ex 0 1.5ex 10%; }
  div.section pre { margin: 3ex 0 3ex 9%;
                    background-color: #D6E8FF;
                    padding: 2ex 0 2ex 1%; }
  div.section table.fields { width: 90%;
                             margin: 1.5ex 0 1.5ex 10%; }
  div.section table.toc { width: 95%;
                          margin: 1.5ex 0 1.5ex 5%; }
  div.timestamp { text-align: center;
                  font-size: 69%;
                  margin: 1.5ex 0 1.5ex 0; }

  h1 { text-align: center; }
  h3 { font-size: medium;
       margin: 4ex 0 1.5ex 0; }

  p { text-align: justify; }

  pre.colored { color: blue; }

  span.keyword { font-family: monospace;
                 text-align: left;
                 white-space: pre;
                 color: darkblue; }

  table.fields td.val { font-weight: bold;
                        text-align: right;
                        width: 30%;
                        vertical-align: baseline;
                        padding: 1ex 1em 1ex 0; }
  table.fields td.desc { vertical-align: baseline;
                         padding: 1ex 0 1ex 1em; }
  table.fields td.desc p:first-child { margin: 0; }
  table.fields td.desc p { margin: 1.5ex 0 0 0; }
  table.index { margin: 6ex auto 6ex auto;
                border: 0;
                border-collapse: separate;
                border-spacing: 1em 0.3ex; }
  table.index tr { padding: 0; }
  table.index td { padding: 0; }
  table.index-toc-link { width: 100%;
                         border: 0;
                         border-spacing: 0;
                         margin: 1ex 0 1ex 0; }
  table.index-toc-link td.left { padding: 0 0.5em 0 0.5em;
                                 font-size: 83%;
                                 text-align: left; }
  table.index-toc-link td.middle { padding: 0 0.5em 0 0.5em;
                                   font-size: 83%;
                                   text-align: center; }
  table.index-toc-link td.right { padding: 0 0.5em 0 0.5em;
                                  font-size: 83%;
                                  text-align: right; }
  table.synopsis { margin: 6ex auto 6ex auto;
                   border: 0;
                   border-collapse: separate;
                   border-spacing: 2em 0.6ex; }
  table.synopsis tr { padding: 0; }
  table.synopsis td { padding: 0; }
  table.toc td.link { width: 30%;
                      text-align: right;
                      vertical-align: baseline;
                      padding: 1ex 1em 1ex 0; }
  table.toc td.desc { vertical-align: baseline;
                      padding: 1ex 0 1ex 1em;
                      text-align: left; }
  table.toc td.desc p:first-child { margin: 0;
                                    text-align: left; }
  table.toc td.desc p { margin: 1.5ex 0 0 0;
                        text-align: left; }

</style>
</head>
<body>

<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table>
<h1>FreeType-2.9.1 API Reference</h1>

<h1 id="version">FreeType Version</h1>
<h2>Synopsis</h2>
<table class="synopsis">
<tr><td><a href="#FT_Library_Version">FT_Library_Version</a></td><td>&nbsp;</td></tr>
<tr><td>&nbsp;</td><td><a href="#FT_Face_CheckTrueTypePatents">FT_Face_CheckTrueTypePatents</a></td></tr>
<tr><td><a href="#FREETYPE_MAJOR">FREETYPE_MAJOR</a></td><td><a href="#FT_Face_SetUnpatentedHinting">FT_Face_SetUnpatentedHinting</a></td></tr>
<tr><td><a href="#FREETYPE_MINOR">FREETYPE_MINOR</a></td><td>&nbsp;</td></tr>
<tr><td><a href="#FREETYPE_PATCH">FREETYPE_PATCH</a></td><td><a href="#FREETYPE_XXX">FREETYPE_XXX</a></td></tr>
</table>


<p>Note that those functions and macros are of limited use because even a new release of FreeType with only documentation changes increases the version number.</p>

<div class="section">
<h3 id="FT_Library_Version">FT_Library_Version</h3>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<pre>
  FT_EXPORT( <span class="keyword">void</span> )
  <b>FT_Library_Version</b>( <a href="ft2-base_interface.html#FT_Library">FT_Library</a>   library,
                      <a href="ft2-basic_types.html#FT_Int">FT_Int</a>      *amajor,
                      <a href="ft2-basic_types.html#FT_Int">FT_Int</a>      *aminor,
                      <a href="ft2-basic_types.html#FT_Int">FT_Int</a>      *apatch );
</pre>

<p>Return the version of the FreeType library being used. This is useful when dynamically linking to the library, since one cannot use the macros <a href="ft2-version.html#FREETYPE_XXX">FREETYPE_MAJOR</a>, <a href="ft2-version.html#FREETYPE_XXX">FREETYPE_MINOR</a>, and <a href="ft2-version.html#FREETYPE_XXX">FREETYPE_PATCH</a>.</p>

<h4>input</h4>
<table class="fields">
<tr><td class="val" id="library">library</td><td class="desc">
<p>A source library handle.</p>
</td></tr>
</table>

<h4>output</h4>
<table class="fields">
<tr><td class="val" id="amajor">amajor</td><td class="desc">
<p>The major version number.</p>
</td></tr>
<tr><td class="val" id="aminor">aminor</td><td class="desc">
<p>The minor version number.</p>
</td></tr>
<tr><td class="val" id="apatch">apatch</td><td class="desc">
<p>The patch version number.</p>
</td></tr>
</table>

<h4>note</h4>
<p>The reason why this function takes a &lsquo;library&rsquo; argument is because certain programs implement library initialization in a custom way that doesn't use <a href="ft2-base_interface.html#FT_Init_FreeType">FT_Init_FreeType</a>.</p>
<p>In such cases, the library version might not be available before the library object has been created.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_Face_CheckTrueTypePatents">FT_Face_CheckTrueTypePatents</h3>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<pre>
  FT_EXPORT( <a href="ft2-basic_types.html#FT_Bool">FT_Bool</a> )
  <b>FT_Face_CheckTrueTypePatents</b>( <a href="ft2-base_interface.html#FT_Face">FT_Face</a>  face );
</pre>

<p>Deprecated, does nothing.</p>

<h4>input</h4>
<table class="fields">
<tr><td class="val" id="face">face</td><td class="desc">
<p>A face handle.</p>
</td></tr>
</table>

<h4>return</h4>
<p>Always returns false.</p>

<h4>note</h4>
<p>Since May 2010, TrueType hinting is no longer patented.</p>

<h4>since</h4>
<p>2.3.5</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_Face_SetUnpatentedHinting">FT_Face_SetUnpatentedHinting</h3>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<pre>
  FT_EXPORT( <a href="ft2-basic_types.html#FT_Bool">FT_Bool</a> )
  <b>FT_Face_SetUnpatentedHinting</b>( <a href="ft2-base_interface.html#FT_Face">FT_Face</a>  face,
                                <a href="ft2-basic_types.html#FT_Bool">FT_Bool</a>  value );
</pre>

<p>Deprecated, does nothing.</p>

<h4>input</h4>
<table class="fields">
<tr><td class="val" id="face">face</td><td class="desc">
<p>A face handle.</p>
</td></tr>
<tr><td class="val" id="value">value</td><td class="desc">
<p>New boolean setting.</p>
</td></tr>
</table>

<h4>return</h4>
<p>Always returns false.</p>

<h4>note</h4>
<p>Since May 2010, TrueType hinting is no longer patented.</p>

<h4>since</h4>
<p>2.3.5</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FREETYPE_XXX">FREETYPE_XXX</h3>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<pre>
#define <a href="ft2-version.html#FREETYPE_MAJOR">FREETYPE_MAJOR</a>  2
#define <a href="ft2-version.html#FREETYPE_MINOR">FREETYPE_MINOR</a>  9
#define <a href="ft2-version.html#FREETYPE_PATCH">FREETYPE_PATCH</a>  1
</pre>

<p>These three macros identify the FreeType source code version. Use <a href="ft2-version.html#FT_Library_Version">FT_Library_Version</a> to access them at runtime.</p>

<h4>values</h4>
<table class="fields">
<tr><td class="val" id="FREETYPE_MAJOR">FREETYPE_MAJOR</td><td class="desc">
<p>The major version number.</p>
</td></tr>
<tr><td class="val" id="FREETYPE_MINOR">FREETYPE_MINOR</td><td class="desc">
<p>The minor version number.</p>
</td></tr>
<tr><td class="val" id="FREETYPE_PATCH">FREETYPE_PATCH</td><td class="desc">
<p>The patch level.</p>
</td></tr>
</table>

<h4>note</h4>
<p>The version number of FreeType if built as a dynamic link library with the &lsquo;libtool&rsquo; package is <i>not</i> controlled by these three macros.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

</body>
</html>

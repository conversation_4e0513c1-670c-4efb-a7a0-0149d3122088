<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"https://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>FreeType-2.9.1 API Reference</title>
<style type="text/css">
  a:link { color: #0000EF; }
  a:visited { color: #51188E; }
  a:hover { color: #FF0000; }

  body { font-family: Verdana, Geneva, Arial, Helvetica, serif;
         color: #000000;
         background: #FFFFFF;
         width: 87%;
         margin: auto; }

  div.section { width: 75%;
                margin: auto; }
  div.section hr { margin: 4ex 0 1ex 0; }
  div.section h4 { background-color: #EEEEFF;
                   font-size: medium;
                   font-style: oblique;
                   font-weight: bold;
                   margin: 3ex 0 1.5ex 9%;
                   padding: 0.3ex 0 0.3ex 1%; }
  div.section p { margin: 1.5ex 0 1.5ex 10%; }
  div.section pre { margin: 3ex 0 3ex 9%;
                    background-color: #D6E8FF;
                    padding: 2ex 0 2ex 1%; }
  div.section table.fields { width: 90%;
                             margin: 1.5ex 0 1.5ex 10%; }
  div.section table.toc { width: 95%;
                          margin: 1.5ex 0 1.5ex 5%; }
  div.timestamp { text-align: center;
                  font-size: 69%;
                  margin: 1.5ex 0 1.5ex 0; }

  h1 { text-align: center; }
  h3 { font-size: medium;
       margin: 4ex 0 1.5ex 0; }

  p { text-align: justify; }

  pre.colored { color: blue; }

  span.keyword { font-family: monospace;
                 text-align: left;
                 white-space: pre;
                 color: darkblue; }

  table.fields td.val { font-weight: bold;
                        text-align: right;
                        width: 30%;
                        vertical-align: baseline;
                        padding: 1ex 1em 1ex 0; }
  table.fields td.desc { vertical-align: baseline;
                         padding: 1ex 0 1ex 1em; }
  table.fields td.desc p:first-child { margin: 0; }
  table.fields td.desc p { margin: 1.5ex 0 0 0; }
  table.index { margin: 6ex auto 6ex auto;
                border: 0;
                border-collapse: separate;
                border-spacing: 1em 0.3ex; }
  table.index tr { padding: 0; }
  table.index td { padding: 0; }
  table.index-toc-link { width: 100%;
                         border: 0;
                         border-spacing: 0;
                         margin: 1ex 0 1ex 0; }
  table.index-toc-link td.left { padding: 0 0.5em 0 0.5em;
                                 font-size: 83%;
                                 text-align: left; }
  table.index-toc-link td.middle { padding: 0 0.5em 0 0.5em;
                                   font-size: 83%;
                                   text-align: center; }
  table.index-toc-link td.right { padding: 0 0.5em 0 0.5em;
                                  font-size: 83%;
                                  text-align: right; }
  table.synopsis { margin: 6ex auto 6ex auto;
                   border: 0;
                   border-collapse: separate;
                   border-spacing: 2em 0.6ex; }
  table.synopsis tr { padding: 0; }
  table.synopsis td { padding: 0; }
  table.toc td.link { width: 30%;
                      text-align: right;
                      vertical-align: baseline;
                      padding: 1ex 1em 1ex 0; }
  table.toc td.desc { vertical-align: baseline;
                      padding: 1ex 0 1ex 1em;
                      text-align: left; }
  table.toc td.desc p:first-child { margin: 0;
                                    text-align: left; }
  table.toc td.desc p { margin: 1.5ex 0 0 0;
                        text-align: left; }

</style>
</head>
<body>

<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table>
<h1>FreeType-2.9.1 API Reference</h1>

<h1 id="tt_driver">The TrueType driver</h1>

<p>While FreeType's TrueType driver doesn't expose API functions by itself, it is possible to control its behaviour with <a href="ft2-module_management.html#FT_Property_Set">FT_Property_Set</a> and <a href="ft2-module_management.html#FT_Property_Get">FT_Property_Get</a>. The following lists the available properties together with the necessary macros and structures.</p>
<p>The TrueType driver's module name is &lsquo;truetype&rsquo;.</p>
<p>A single property <a href="ft2-properties.html#interpreter-version">interpreter-version</a> is available, as documented in the &lsquo;<a href="ft2-properties.html#properties">Driver properties</a>&rsquo; section.</p>
<p>We start with a list of definitions, kindly provided by Greg Hitchcock.</p>
<p><i>Bi-Level</i> <i>Rendering</i></p>
<p>Monochromatic rendering, exclusively used in the early days of TrueType by both Apple and Microsoft. Microsoft's GDI interface supported hinting of the right-side bearing point, such that the advance width could be non-linear. Most often this was done to achieve some level of glyph symmetry. To enable reasonable performance (e.g., not having to run hinting on all glyphs just to get the widths) there was a bit in the head table indicating if the side bearing was hinted, and additional tables, &lsquo;hdmx&rsquo; and &lsquo;LTSH&rsquo;, to cache hinting widths across multiple sizes and device aspect ratios.</p>
<p><i>Font</i> <i>Smoothing</i></p>
<p>Microsoft's GDI implementation of anti-aliasing. Not traditional anti-aliasing as the outlines were hinted before the sampling. The widths matched the bi-level rendering.</p>
<p><i>ClearType</i> <i>Rendering</i></p>
<p>Technique that uses physical subpixels to improve rendering on LCD (and other) displays. Because of the higher resolution, many methods of improving symmetry in glyphs through hinting the right-side bearing were no longer necessary. This lead to what GDI calls &lsquo;natural widths&rsquo; ClearType, see <a href="http://www.beatstamm.com/typography/RTRCh4.htm#Sec21">http://www.beatstamm.com/typography/RTRCh4.htm#Sec21</a>. Since hinting has extra resolution, most non-linearity went away, but it is still possible for hints to change the advance widths in this mode.</p>
<p><i>ClearType</i> <i>Compatible</i> <i>Widths</i></p>
<p>One of the earliest challenges with ClearType was allowing the implementation in GDI to be selected without requiring all UI and documents to reflow. To address this, a compatible method of rendering ClearType was added where the font hints are executed once to determine the width in bi-level rendering, and then re-run in ClearType, with the difference in widths being absorbed in the font hints for ClearType (mostly in the white space of hints); see <a href="http://www.beatstamm.com/typography/RTRCh4.htm#Sec20">http://www.beatstamm.com/typography/RTRCh4.htm#Sec20</a>. Somewhat by definition, compatible width ClearType allows for non-linear widths, but only when the bi-level version has non-linear widths.</p>
<p><i>ClearType</i> <i>Subpixel</i> <i>Positioning</i></p>
<p>One of the nice benefits of ClearType is the ability to more crisply display fractional widths; unfortunately, the GDI model of integer bitmaps did not support this. However, the WPF and Direct Write frameworks do support fractional widths. DWrite calls this &lsquo;natural mode&rsquo;, not to be confused with GDI's &lsquo;natural widths&rsquo;. Subpixel positioning, in the current implementation of Direct Write, unfortunately does not support hinted advance widths, see <a href="http://www.beatstamm.com/typography/RTRCh4.htm#Sec22">http://www.beatstamm.com/typography/RTRCh4.htm#Sec22</a>. Note that the TrueType interpreter fully allows the advance width to be adjusted in this mode, just the DWrite client will ignore those changes.</p>
<p><i>ClearType</i> <i>Backward</i> <i>Compatibility</i></p>
<p>This is a set of exceptions made in the TrueType interpreter to minimize hinting techniques that were problematic with the extra resolution of ClearType; see <a href="http://www.beatstamm.com/typography/RTRCh4.htm#Sec1">http://www.beatstamm.com/typography/RTRCh4.htm#Sec1</a> and <a href="https://www.microsoft.com/typography/cleartype/truetypecleartype.aspx">https://www.microsoft.com/typography/cleartype/truetypecleartype.aspx</a>. This technique is not to be confused with ClearType compatible widths. ClearType backward compatibility has no direct impact on changing advance widths, but there might be an indirect impact on disabling some deltas. This could be worked around in backward compatibility mode.</p>
<p><i>Native</i> <i>ClearType</i> <i>Mode</i></p>
<p>(Not to be confused with &lsquo;natural widths&rsquo;.) This mode removes all the exceptions in the TrueType interpreter when running with ClearType. Any issues on widths would still apply, though.</p>

</body>
</html>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"https://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>FreeType-2.9.1 API Reference</title>
<style type="text/css">
  a:link { color: #0000EF; }
  a:visited { color: #51188E; }
  a:hover { color: #FF0000; }

  body { font-family: Verdana, Geneva, Arial, Helvetica, serif;
         color: #000000;
         background: #FFFFFF;
         width: 87%;
         margin: auto; }

  div.section { width: 75%;
                margin: auto; }
  div.section hr { margin: 4ex 0 1ex 0; }
  div.section h4 { background-color: #EEEEFF;
                   font-size: medium;
                   font-style: oblique;
                   font-weight: bold;
                   margin: 3ex 0 1.5ex 9%;
                   padding: 0.3ex 0 0.3ex 1%; }
  div.section p { margin: 1.5ex 0 1.5ex 10%; }
  div.section pre { margin: 3ex 0 3ex 9%;
                    background-color: #D6E8FF;
                    padding: 2ex 0 2ex 1%; }
  div.section table.fields { width: 90%;
                             margin: 1.5ex 0 1.5ex 10%; }
  div.section table.toc { width: 95%;
                          margin: 1.5ex 0 1.5ex 5%; }
  div.timestamp { text-align: center;
                  font-size: 69%;
                  margin: 1.5ex 0 1.5ex 0; }

  h1 { text-align: center; }
  h3 { font-size: medium;
       margin: 4ex 0 1.5ex 0; }

  p { text-align: justify; }

  pre.colored { color: blue; }

  span.keyword { font-family: monospace;
                 text-align: left;
                 white-space: pre;
                 color: darkblue; }

  table.fields td.val { font-weight: bold;
                        text-align: right;
                        width: 30%;
                        vertical-align: baseline;
                        padding: 1ex 1em 1ex 0; }
  table.fields td.desc { vertical-align: baseline;
                         padding: 1ex 0 1ex 1em; }
  table.fields td.desc p:first-child { margin: 0; }
  table.fields td.desc p { margin: 1.5ex 0 0 0; }
  table.index { margin: 6ex auto 6ex auto;
                border: 0;
                border-collapse: separate;
                border-spacing: 1em 0.3ex; }
  table.index tr { padding: 0; }
  table.index td { padding: 0; }
  table.index-toc-link { width: 100%;
                         border: 0;
                         border-spacing: 0;
                         margin: 1ex 0 1ex 0; }
  table.index-toc-link td.left { padding: 0 0.5em 0 0.5em;
                                 font-size: 83%;
                                 text-align: left; }
  table.index-toc-link td.middle { padding: 0 0.5em 0 0.5em;
                                   font-size: 83%;
                                   text-align: center; }
  table.index-toc-link td.right { padding: 0 0.5em 0 0.5em;
                                  font-size: 83%;
                                  text-align: right; }
  table.synopsis { margin: 6ex auto 6ex auto;
                   border: 0;
                   border-collapse: separate;
                   border-spacing: 2em 0.6ex; }
  table.synopsis tr { padding: 0; }
  table.synopsis td { padding: 0; }
  table.toc td.link { width: 30%;
                      text-align: right;
                      vertical-align: baseline;
                      padding: 1ex 1em 1ex 0; }
  table.toc td.desc { vertical-align: baseline;
                      padding: 1ex 0 1ex 1em;
                      text-align: left; }
  table.toc td.desc p:first-child { margin: 0;
                                    text-align: left; }
  table.toc td.desc p { margin: 1.5ex 0 0 0;
                        text-align: left; }

</style>
</head>
<body>

<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table>
<h1>FreeType-2.9.1 API Reference</h1>

<h1 id="parameter_tags">Parameter Tags</h1>
<h2>Synopsis</h2>
<table class="synopsis">
<tr><td><a href="#FT_PARAM_TAG_IGNORE_TYPOGRAPHIC_FAMILY">FT_PARAM_TAG_IGNORE_TYPOGRAPHIC_FAMILY</a></td></tr>
<tr><td><a href="#FT_PARAM_TAG_IGNORE_TYPOGRAPHIC_SUBFAMILY">FT_PARAM_TAG_IGNORE_TYPOGRAPHIC_SUBFAMILY</a></td></tr>
<tr><td><a href="#FT_PARAM_TAG_INCREMENTAL">FT_PARAM_TAG_INCREMENTAL</a></td></tr>
<tr><td><a href="#FT_PARAM_TAG_LCD_FILTER_WEIGHTS">FT_PARAM_TAG_LCD_FILTER_WEIGHTS</a></td></tr>
<tr><td><a href="#FT_PARAM_TAG_RANDOM_SEED">FT_PARAM_TAG_RANDOM_SEED</a></td></tr>
<tr><td><a href="#FT_PARAM_TAG_STEM_DARKENING">FT_PARAM_TAG_STEM_DARKENING</a></td></tr>
<tr><td><a href="#FT_PARAM_TAG_UNPATENTED_HINTING">FT_PARAM_TAG_UNPATENTED_HINTING</a></td></tr>
</table>


<p>This section contains macros for the <a href="ft2-base_interface.html#FT_Parameter">FT_Parameter</a> structure that are used with various functions to activate some special functionality or different behaviour of various components of FreeType.</p>

<div class="section">
<h3 id="FT_PARAM_TAG_IGNORE_TYPOGRAPHIC_FAMILY">FT_PARAM_TAG_IGNORE_TYPOGRAPHIC_FAMILY</h3>
<pre>
#define <b>FT_PARAM_TAG_IGNORE_TYPOGRAPHIC_FAMILY</b> \
          <a href="ft2-basic_types.html#FT_MAKE_TAG">FT_MAKE_TAG</a>( 'i', 'g', 'p', 'f' )


  /* this constant is deprecated */
#define FT_PARAM_TAG_IGNORE_PREFERRED_FAMILY \
          <b>FT_PARAM_TAG_IGNORE_TYPOGRAPHIC_FAMILY</b>
</pre>

<p>A tag for <a href="ft2-base_interface.html#FT_Parameter">FT_Parameter</a> to make <a href="ft2-base_interface.html#FT_Open_Face">FT_Open_Face</a> ignore typographic family names in the &lsquo;name&rsquo; table (introduced in OpenType version 1.4). Use this for backward compatibility with legacy systems that have a four-faces-per-family restriction.</p>

<h4>since</h4>
<p>2.8</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_PARAM_TAG_IGNORE_TYPOGRAPHIC_SUBFAMILY">FT_PARAM_TAG_IGNORE_TYPOGRAPHIC_SUBFAMILY</h3>
<pre>
#define <b>FT_PARAM_TAG_IGNORE_TYPOGRAPHIC_SUBFAMILY</b> \
          <a href="ft2-basic_types.html#FT_MAKE_TAG">FT_MAKE_TAG</a>( 'i', 'g', 'p', 's' )


  /* this constant is deprecated */
#define FT_PARAM_TAG_IGNORE_PREFERRED_SUBFAMILY \
          <b>FT_PARAM_TAG_IGNORE_TYPOGRAPHIC_SUBFAMILY</b>
</pre>

<p>A tag for <a href="ft2-base_interface.html#FT_Parameter">FT_Parameter</a> to make <a href="ft2-base_interface.html#FT_Open_Face">FT_Open_Face</a> ignore typographic subfamily names in the &lsquo;name&rsquo; table (introduced in OpenType version 1.4). Use this for backward compatibility with legacy systems that have a four-faces-per-family restriction.</p>

<h4>since</h4>
<p>2.8</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_PARAM_TAG_INCREMENTAL">FT_PARAM_TAG_INCREMENTAL</h3>
<pre>
#define <b>FT_PARAM_TAG_INCREMENTAL</b> \
          <a href="ft2-basic_types.html#FT_MAKE_TAG">FT_MAKE_TAG</a>( 'i', 'n', 'c', 'r' )
</pre>

<p>An <a href="ft2-base_interface.html#FT_Parameter">FT_Parameter</a> tag to be used with <a href="ft2-base_interface.html#FT_Open_Face">FT_Open_Face</a> to indicate incremental glyph loading.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_PARAM_TAG_LCD_FILTER_WEIGHTS">FT_PARAM_TAG_LCD_FILTER_WEIGHTS</h3>
<pre>
#define <b>FT_PARAM_TAG_LCD_FILTER_WEIGHTS</b> \
          <a href="ft2-basic_types.html#FT_MAKE_TAG">FT_MAKE_TAG</a>( 'l', 'c', 'd', 'f' )
</pre>

<p>An <a href="ft2-base_interface.html#FT_Parameter">FT_Parameter</a> tag to be used with <a href="ft2-base_interface.html#FT_Face_Properties">FT_Face_Properties</a>. The corresponding argument specifies the five LCD filter weights for a given face (if using <a href="ft2-base_interface.html#FT_LOAD_TARGET_XXX">FT_LOAD_TARGET_LCD</a>, for example), overriding the global default values or the values set up with <a href="ft2-lcd_filtering.html#FT_Library_SetLcdFilterWeights">FT_Library_SetLcdFilterWeights</a>.</p>

<h4>since</h4>
<p>2.8</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_PARAM_TAG_RANDOM_SEED">FT_PARAM_TAG_RANDOM_SEED</h3>
<pre>
#define <b>FT_PARAM_TAG_RANDOM_SEED</b> \
          <a href="ft2-basic_types.html#FT_MAKE_TAG">FT_MAKE_TAG</a>( 's', 'e', 'e', 'd' )
</pre>

<p>An <a href="ft2-base_interface.html#FT_Parameter">FT_Parameter</a> tag to be used with <a href="ft2-base_interface.html#FT_Face_Properties">FT_Face_Properties</a>. The corresponding 32bit signed integer argument overrides the font driver's random seed value with a face-specific one; see <a href="ft2-properties.html#random-seed">random-seed</a>.</p>

<h4>since</h4>
<p>2.8</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_PARAM_TAG_STEM_DARKENING">FT_PARAM_TAG_STEM_DARKENING</h3>
<pre>
#define <b>FT_PARAM_TAG_STEM_DARKENING</b> \
          <a href="ft2-basic_types.html#FT_MAKE_TAG">FT_MAKE_TAG</a>( 'd', 'a', 'r', 'k' )
</pre>

<p>An <a href="ft2-base_interface.html#FT_Parameter">FT_Parameter</a> tag to be used with <a href="ft2-base_interface.html#FT_Face_Properties">FT_Face_Properties</a>. The corresponding Boolean argument specifies whether to apply stem darkening, overriding the global default values or the values set up with <a href="ft2-module_management.html#FT_Property_Set">FT_Property_Set</a> (see <a href="ft2-properties.html#no-stem-darkening">no-stem-darkening</a>).</p>
<p>This is a passive setting that only takes effect if the font driver or autohinter honors it, which the CFF, Type&nbsp;1, and CID drivers always do, but the autohinter only in &lsquo;light&rsquo; hinting mode (as of version 2.9).</p>

<h4>since</h4>
<p>2.8</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_PARAM_TAG_UNPATENTED_HINTING">FT_PARAM_TAG_UNPATENTED_HINTING</h3>
<pre>
#define <b>FT_PARAM_TAG_UNPATENTED_HINTING</b> \
          <a href="ft2-basic_types.html#FT_MAKE_TAG">FT_MAKE_TAG</a>( 'u', 'n', 'p', 'a' )
</pre>

<p>Deprecated, no effect.</p>
<p>Previously: A constant used as the tag of an <a href="ft2-base_interface.html#FT_Parameter">FT_Parameter</a> structure to indicate that unpatented methods only should be used by the TrueType bytecode interpreter for a typeface opened by <a href="ft2-base_interface.html#FT_Open_Face">FT_Open_Face</a>.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

</body>
</html>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"https://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>FreeType-2.9.1 API Reference</title>
<style type="text/css">
  a:link { color: #0000EF; }
  a:visited { color: #51188E; }
  a:hover { color: #FF0000; }

  body { font-family: Verdana, Geneva, Arial, Helvetica, serif;
         color: #000000;
         background: #FFFFFF;
         width: 87%;
         margin: auto; }

  div.section { width: 75%;
                margin: auto; }
  div.section hr { margin: 4ex 0 1ex 0; }
  div.section h4 { background-color: #EEEEFF;
                   font-size: medium;
                   font-style: oblique;
                   font-weight: bold;
                   margin: 3ex 0 1.5ex 9%;
                   padding: 0.3ex 0 0.3ex 1%; }
  div.section p { margin: 1.5ex 0 1.5ex 10%; }
  div.section pre { margin: 3ex 0 3ex 9%;
                    background-color: #D6E8FF;
                    padding: 2ex 0 2ex 1%; }
  div.section table.fields { width: 90%;
                             margin: 1.5ex 0 1.5ex 10%; }
  div.section table.toc { width: 95%;
                          margin: 1.5ex 0 1.5ex 5%; }
  div.timestamp { text-align: center;
                  font-size: 69%;
                  margin: 1.5ex 0 1.5ex 0; }

  h1 { text-align: center; }
  h3 { font-size: medium;
       margin: 4ex 0 1.5ex 0; }

  p { text-align: justify; }

  pre.colored { color: blue; }

  span.keyword { font-family: monospace;
                 text-align: left;
                 white-space: pre;
                 color: darkblue; }

  table.fields td.val { font-weight: bold;
                        text-align: right;
                        width: 30%;
                        vertical-align: baseline;
                        padding: 1ex 1em 1ex 0; }
  table.fields td.desc { vertical-align: baseline;
                         padding: 1ex 0 1ex 1em; }
  table.fields td.desc p:first-child { margin: 0; }
  table.fields td.desc p { margin: 1.5ex 0 0 0; }
  table.index { margin: 6ex auto 6ex auto;
                border: 0;
                border-collapse: separate;
                border-spacing: 1em 0.3ex; }
  table.index tr { padding: 0; }
  table.index td { padding: 0; }
  table.index-toc-link { width: 100%;
                         border: 0;
                         border-spacing: 0;
                         margin: 1ex 0 1ex 0; }
  table.index-toc-link td.left { padding: 0 0.5em 0 0.5em;
                                 font-size: 83%;
                                 text-align: left; }
  table.index-toc-link td.middle { padding: 0 0.5em 0 0.5em;
                                   font-size: 83%;
                                   text-align: center; }
  table.index-toc-link td.right { padding: 0 0.5em 0 0.5em;
                                  font-size: 83%;
                                  text-align: right; }
  table.synopsis { margin: 6ex auto 6ex auto;
                   border: 0;
                   border-collapse: separate;
                   border-spacing: 2em 0.6ex; }
  table.synopsis tr { padding: 0; }
  table.synopsis td { padding: 0; }
  table.toc td.link { width: 30%;
                      text-align: right;
                      vertical-align: baseline;
                      padding: 1ex 1em 1ex 0; }
  table.toc td.desc { vertical-align: baseline;
                      padding: 1ex 0 1ex 1em;
                      text-align: left; }
  table.toc td.desc p:first-child { margin: 0;
                                    text-align: left; }
  table.toc td.desc p { margin: 1.5ex 0 0 0;
                        text-align: left; }

</style>
</head>
<body>

<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table>
<h1>FreeType-2.9.1 API Reference</h1>

<h1 id="header_file_macros">Header File Macros</h1>
<h2>Synopsis</h2>
<table class="synopsis">
<tr><td><a href="#FT_CONFIG_CONFIG_H">FT_CONFIG_CONFIG_H</a></td><td><a href="#FT_BDF_H">FT_BDF_H</a></td></tr>
<tr><td><a href="#FT_CONFIG_STANDARD_LIBRARY_H">FT_CONFIG_STANDARD_LIBRARY_H</a></td><td><a href="#FT_CID_H">FT_CID_H</a></td></tr>
<tr><td><a href="#FT_CONFIG_OPTIONS_H">FT_CONFIG_OPTIONS_H</a></td><td><a href="#FT_GZIP_H">FT_GZIP_H</a></td></tr>
<tr><td><a href="#FT_CONFIG_MODULES_H">FT_CONFIG_MODULES_H</a></td><td><a href="#FT_LZW_H">FT_LZW_H</a></td></tr>
<tr><td><a href="#FT_FREETYPE_H">FT_FREETYPE_H</a></td><td><a href="#FT_BZIP2_H">FT_BZIP2_H</a></td></tr>
<tr><td><a href="#FT_ERRORS_H">FT_ERRORS_H</a></td><td><a href="#FT_WINFONTS_H">FT_WINFONTS_H</a></td></tr>
<tr><td><a href="#FT_MODULE_ERRORS_H">FT_MODULE_ERRORS_H</a></td><td><a href="#FT_GLYPH_H">FT_GLYPH_H</a></td></tr>
<tr><td><a href="#FT_SYSTEM_H">FT_SYSTEM_H</a></td><td><a href="#FT_BITMAP_H">FT_BITMAP_H</a></td></tr>
<tr><td><a href="#FT_IMAGE_H">FT_IMAGE_H</a></td><td><a href="#FT_BBOX_H">FT_BBOX_H</a></td></tr>
<tr><td><a href="#FT_TYPES_H">FT_TYPES_H</a></td><td><a href="#FT_CACHE_H">FT_CACHE_H</a></td></tr>
<tr><td><a href="#FT_LIST_H">FT_LIST_H</a></td><td><a href="#FT_MAC_H">FT_MAC_H</a></td></tr>
<tr><td><a href="#FT_OUTLINE_H">FT_OUTLINE_H</a></td><td><a href="#FT_MULTIPLE_MASTERS_H">FT_MULTIPLE_MASTERS_H</a></td></tr>
<tr><td><a href="#FT_SIZES_H">FT_SIZES_H</a></td><td><a href="#FT_SFNT_NAMES_H">FT_SFNT_NAMES_H</a></td></tr>
<tr><td><a href="#FT_MODULE_H">FT_MODULE_H</a></td><td><a href="#FT_OPENTYPE_VALIDATE_H">FT_OPENTYPE_VALIDATE_H</a></td></tr>
<tr><td><a href="#FT_RENDER_H">FT_RENDER_H</a></td><td><a href="#FT_GX_VALIDATE_H">FT_GX_VALIDATE_H</a></td></tr>
<tr><td><a href="#FT_DRIVER_H">FT_DRIVER_H</a></td><td><a href="#FT_PFR_H">FT_PFR_H</a></td></tr>
<tr><td><a href="#FT_AUTOHINTER_H">FT_AUTOHINTER_H</a></td><td><a href="#FT_STROKER_H">FT_STROKER_H</a></td></tr>
<tr><td><a href="#FT_CFF_DRIVER_H">FT_CFF_DRIVER_H</a></td><td><a href="#FT_SYNTHESIS_H">FT_SYNTHESIS_H</a></td></tr>
<tr><td><a href="#FT_TRUETYPE_DRIVER_H">FT_TRUETYPE_DRIVER_H</a></td><td><a href="#FT_FONT_FORMATS_H">FT_FONT_FORMATS_H</a></td></tr>
<tr><td><a href="#FT_PCF_DRIVER_H">FT_PCF_DRIVER_H</a></td><td><a href="#FT_TRIGONOMETRY_H">FT_TRIGONOMETRY_H</a></td></tr>
<tr><td><a href="#FT_TYPE1_TABLES_H">FT_TYPE1_TABLES_H</a></td><td><a href="#FT_LCD_FILTER_H">FT_LCD_FILTER_H</a></td></tr>
<tr><td><a href="#FT_TRUETYPE_IDS_H">FT_TRUETYPE_IDS_H</a></td><td><a href="#FT_INCREMENTAL_H">FT_INCREMENTAL_H</a></td></tr>
<tr><td><a href="#FT_TRUETYPE_TABLES_H">FT_TRUETYPE_TABLES_H</a></td><td><a href="#FT_GASP_H">FT_GASP_H</a></td></tr>
<tr><td><a href="#FT_TRUETYPE_TAGS_H">FT_TRUETYPE_TAGS_H</a></td><td><a href="#FT_ADVANCES_H">FT_ADVANCES_H</a></td></tr>
</table>


<p>The following macros are defined to the name of specific FreeType&nbsp;2 header files. They can be used directly in #include statements as in:</p>
<pre class="colored">
  #include FT_FREETYPE_H
  #include FT_MULTIPLE_MASTERS_H
  #include FT_GLYPH_H
</pre>
<p>There are several reasons why we are now using macros to name public header files. The first one is that such macros are not limited to the infamous 8.3&nbsp;naming rule required by DOS (and &lsquo;FT_MULTIPLE_MASTERS_H&rsquo; is a lot more meaningful than &lsquo;ftmm.h&rsquo;).</p>
<p>The second reason is that it allows for more flexibility in the way FreeType&nbsp;2 is installed on a given system.</p>

<div class="section">
<h3 id="FT_CONFIG_CONFIG_H">FT_CONFIG_CONFIG_H</h3>
<pre>
#ifndef <b>FT_CONFIG_CONFIG_H</b>
#define <b>FT_CONFIG_CONFIG_H</b>  &lt;freetype/config/ftconfig.h&gt;
#endif
</pre>

<p>A macro used in #include statements to name the file containing FreeType&nbsp;2 configuration data.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_CONFIG_STANDARD_LIBRARY_H">FT_CONFIG_STANDARD_LIBRARY_H</h3>
<pre>
#ifndef <b>FT_CONFIG_STANDARD_LIBRARY_H</b>
#define <b>FT_CONFIG_STANDARD_LIBRARY_H</b>  &lt;freetype/config/ftstdlib.h&gt;
#endif
</pre>

<p>A macro used in #include statements to name the file containing FreeType&nbsp;2 interface to the standard C library functions.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_CONFIG_OPTIONS_H">FT_CONFIG_OPTIONS_H</h3>
<pre>
#ifndef <b>FT_CONFIG_OPTIONS_H</b>
#define <b>FT_CONFIG_OPTIONS_H</b>  &lt;freetype/config/ftoption.h&gt;
#endif
</pre>

<p>A macro used in #include statements to name the file containing FreeType&nbsp;2 project-specific configuration options.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_CONFIG_MODULES_H">FT_CONFIG_MODULES_H</h3>
<pre>
#ifndef <b>FT_CONFIG_MODULES_H</b>
#define <b>FT_CONFIG_MODULES_H</b>  &lt;freetype/config/ftmodule.h&gt;
#endif
</pre>

<p>A macro used in #include statements to name the file containing the list of FreeType&nbsp;2 modules that are statically linked to new library instances in <a href="ft2-base_interface.html#FT_Init_FreeType">FT_Init_FreeType</a>.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_FREETYPE_H">FT_FREETYPE_H</h3>
<pre>
#define <b>FT_FREETYPE_H</b>  &lt;freetype/freetype.h&gt;
</pre>

<p>A macro used in #include statements to name the file containing the base FreeType&nbsp;2 API.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_ERRORS_H">FT_ERRORS_H</h3>
<pre>
#define <b>FT_ERRORS_H</b>  &lt;freetype/fterrors.h&gt;
</pre>

<p>A macro used in #include statements to name the file containing the list of FreeType&nbsp;2 error codes (and messages).</p>
<p>It is included by <a href="ft2-header_file_macros.html#FT_FREETYPE_H">FT_FREETYPE_H</a>.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_MODULE_ERRORS_H">FT_MODULE_ERRORS_H</h3>
<pre>
#define <b>FT_MODULE_ERRORS_H</b>  &lt;freetype/ftmoderr.h&gt;
</pre>

<p>A macro used in #include statements to name the file containing the list of FreeType&nbsp;2 module error offsets (and messages).</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_SYSTEM_H">FT_SYSTEM_H</h3>
<pre>
#define <b>FT_SYSTEM_H</b>  &lt;freetype/ftsystem.h&gt;
</pre>

<p>A macro used in #include statements to name the file containing the FreeType&nbsp;2 interface to low-level operations (i.e., memory management and stream i/o).</p>
<p>It is included by <a href="ft2-header_file_macros.html#FT_FREETYPE_H">FT_FREETYPE_H</a>.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_IMAGE_H">FT_IMAGE_H</h3>
<pre>
#define <b>FT_IMAGE_H</b>  &lt;freetype/ftimage.h&gt;
</pre>

<p>A macro used in #include statements to name the file containing type definitions related to glyph images (i.e., bitmaps, outlines, scan-converter parameters).</p>
<p>It is included by <a href="ft2-header_file_macros.html#FT_FREETYPE_H">FT_FREETYPE_H</a>.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_TYPES_H">FT_TYPES_H</h3>
<pre>
#define <b>FT_TYPES_H</b>  &lt;freetype/fttypes.h&gt;
</pre>

<p>A macro used in #include statements to name the file containing the basic data types defined by FreeType&nbsp;2.</p>
<p>It is included by <a href="ft2-header_file_macros.html#FT_FREETYPE_H">FT_FREETYPE_H</a>.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_LIST_H">FT_LIST_H</h3>
<pre>
#define <b>FT_LIST_H</b>  &lt;freetype/ftlist.h&gt;
</pre>

<p>A macro used in #include statements to name the file containing the list management API of FreeType&nbsp;2.</p>
<p>(Most applications will never need to include this file.)</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_OUTLINE_H">FT_OUTLINE_H</h3>
<pre>
#define <b>FT_OUTLINE_H</b>  &lt;freetype/ftoutln.h&gt;
</pre>

<p>A macro used in #include statements to name the file containing the scalable outline management API of FreeType&nbsp;2.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_SIZES_H">FT_SIZES_H</h3>
<pre>
#define <b>FT_SIZES_H</b>  &lt;freetype/ftsizes.h&gt;
</pre>

<p>A macro used in #include statements to name the file containing the API which manages multiple <a href="ft2-base_interface.html#FT_Size">FT_Size</a> objects per face.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_MODULE_H">FT_MODULE_H</h3>
<pre>
#define <b>FT_MODULE_H</b>  &lt;freetype/ftmodapi.h&gt;
</pre>

<p>A macro used in #include statements to name the file containing the module management API of FreeType&nbsp;2.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_RENDER_H">FT_RENDER_H</h3>
<pre>
#define <b>FT_RENDER_H</b>  &lt;freetype/ftrender.h&gt;
</pre>

<p>A macro used in #include statements to name the file containing the renderer module management API of FreeType&nbsp;2.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_DRIVER_H">FT_DRIVER_H</h3>
<pre>
#define <b>FT_DRIVER_H</b>  &lt;freetype/ftdriver.h&gt;
</pre>

<p>A macro used in #include statements to name the file containing structures and macros related to the driver modules.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_AUTOHINTER_H">FT_AUTOHINTER_H</h3>
<pre>
#define <b>FT_AUTOHINTER_H</b>  <a href="ft2-header_file_macros.html#FT_DRIVER_H">FT_DRIVER_H</a>
</pre>

<p>A macro used in #include statements to name the file containing structures and macros related to the auto-hinting module.</p>
<p>Deprecated since version 2.9; use <a href="ft2-header_file_macros.html#FT_DRIVER_H">FT_DRIVER_H</a> instead.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_CFF_DRIVER_H">FT_CFF_DRIVER_H</h3>
<pre>
#define <b>FT_CFF_DRIVER_H</b>  <a href="ft2-header_file_macros.html#FT_DRIVER_H">FT_DRIVER_H</a>
</pre>

<p>A macro used in #include statements to name the file containing structures and macros related to the CFF driver module.</p>
<p>Deprecated since version 2.9; use <a href="ft2-header_file_macros.html#FT_DRIVER_H">FT_DRIVER_H</a> instead.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_TRUETYPE_DRIVER_H">FT_TRUETYPE_DRIVER_H</h3>
<pre>
#define <b>FT_TRUETYPE_DRIVER_H</b>  <a href="ft2-header_file_macros.html#FT_DRIVER_H">FT_DRIVER_H</a>
</pre>

<p>A macro used in #include statements to name the file containing structures and macros related to the TrueType driver module.</p>
<p>Deprecated since version 2.9; use <a href="ft2-header_file_macros.html#FT_DRIVER_H">FT_DRIVER_H</a> instead.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_PCF_DRIVER_H">FT_PCF_DRIVER_H</h3>
<pre>
#define <b>FT_PCF_DRIVER_H</b>  <a href="ft2-header_file_macros.html#FT_DRIVER_H">FT_DRIVER_H</a>
</pre>

<p>A macro used in #include statements to name the file containing structures and macros related to the PCF driver module.</p>
<p>Deprecated since version 2.9; use <a href="ft2-header_file_macros.html#FT_DRIVER_H">FT_DRIVER_H</a> instead.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_TYPE1_TABLES_H">FT_TYPE1_TABLES_H</h3>
<pre>
#define <b>FT_TYPE1_TABLES_H</b>  &lt;freetype/t1tables.h&gt;
</pre>

<p>A macro used in #include statements to name the file containing the types and API specific to the Type&nbsp;1 format.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_TRUETYPE_IDS_H">FT_TRUETYPE_IDS_H</h3>
<pre>
#define <b>FT_TRUETYPE_IDS_H</b>  &lt;freetype/ttnameid.h&gt;
</pre>

<p>A macro used in #include statements to name the file containing the enumeration values which identify name strings, languages, encodings, etc. This file really contains a <i>large</i> set of constant macro definitions, taken from the TrueType and OpenType specifications.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_TRUETYPE_TABLES_H">FT_TRUETYPE_TABLES_H</h3>
<pre>
#define <b>FT_TRUETYPE_TABLES_H</b>  &lt;freetype/tttables.h&gt;
</pre>

<p>A macro used in #include statements to name the file containing the types and API specific to the TrueType (as well as OpenType) format.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_TRUETYPE_TAGS_H">FT_TRUETYPE_TAGS_H</h3>
<pre>
#define <b>FT_TRUETYPE_TAGS_H</b>  &lt;freetype/tttags.h&gt;
</pre>

<p>A macro used in #include statements to name the file containing the definitions of TrueType four-byte &lsquo;tags&rsquo; which identify blocks in SFNT-based font formats (i.e., TrueType and OpenType).</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_BDF_H">FT_BDF_H</h3>
<pre>
#define <b>FT_BDF_H</b>  &lt;freetype/ftbdf.h&gt;
</pre>

<p>A macro used in #include statements to name the file containing the definitions of an API which accesses BDF-specific strings from a face.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_CID_H">FT_CID_H</h3>
<pre>
#define <b>FT_CID_H</b>  &lt;freetype/ftcid.h&gt;
</pre>

<p>A macro used in #include statements to name the file containing the definitions of an API which access CID font information from a face.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_GZIP_H">FT_GZIP_H</h3>
<pre>
#define <b>FT_GZIP_H</b>  &lt;freetype/ftgzip.h&gt;
</pre>

<p>A macro used in #include statements to name the file containing the definitions of an API which supports gzip-compressed files.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_LZW_H">FT_LZW_H</h3>
<pre>
#define <b>FT_LZW_H</b>  &lt;freetype/ftlzw.h&gt;
</pre>

<p>A macro used in #include statements to name the file containing the definitions of an API which supports LZW-compressed files.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_BZIP2_H">FT_BZIP2_H</h3>
<pre>
#define <b>FT_BZIP2_H</b>  &lt;freetype/ftbzip2.h&gt;
</pre>

<p>A macro used in #include statements to name the file containing the definitions of an API which supports bzip2-compressed files.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_WINFONTS_H">FT_WINFONTS_H</h3>
<pre>
#define <b>FT_WINFONTS_H</b>   &lt;freetype/ftwinfnt.h&gt;
</pre>

<p>A macro used in #include statements to name the file containing the definitions of an API which supports Windows FNT files.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_GLYPH_H">FT_GLYPH_H</h3>
<pre>
#define <b>FT_GLYPH_H</b>  &lt;freetype/ftglyph.h&gt;
</pre>

<p>A macro used in #include statements to name the file containing the API of the optional glyph management component.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_BITMAP_H">FT_BITMAP_H</h3>
<pre>
#define <b>FT_BITMAP_H</b>  &lt;freetype/ftbitmap.h&gt;
</pre>

<p>A macro used in #include statements to name the file containing the API of the optional bitmap conversion component.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_BBOX_H">FT_BBOX_H</h3>
<pre>
#define <b>FT_BBOX_H</b>  &lt;freetype/ftbbox.h&gt;
</pre>

<p>A macro used in #include statements to name the file containing the API of the optional exact bounding box computation routines.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_CACHE_H">FT_CACHE_H</h3>
<pre>
#define <b>FT_CACHE_H</b>  &lt;freetype/ftcache.h&gt;
</pre>

<p>A macro used in #include statements to name the file containing the API of the optional FreeType&nbsp;2 cache sub-system.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_MAC_H">FT_MAC_H</h3>
<pre>
#define <b>FT_MAC_H</b>  &lt;freetype/ftmac.h&gt;
</pre>

<p>A macro used in #include statements to name the file containing the Macintosh-specific FreeType&nbsp;2 API. The latter is used to access fonts embedded in resource forks.</p>
<p>This header file must be explicitly included by client applications compiled on the Mac (note that the base API still works though).</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_MULTIPLE_MASTERS_H">FT_MULTIPLE_MASTERS_H</h3>
<pre>
#define <b>FT_MULTIPLE_MASTERS_H</b>  &lt;freetype/ftmm.h&gt;
</pre>

<p>A macro used in #include statements to name the file containing the optional multiple-masters management API of FreeType&nbsp;2.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_SFNT_NAMES_H">FT_SFNT_NAMES_H</h3>
<pre>
#define <b>FT_SFNT_NAMES_H</b>  &lt;freetype/ftsnames.h&gt;
</pre>

<p>A macro used in #include statements to name the file containing the optional FreeType&nbsp;2 API which accesses embedded &lsquo;name&rsquo; strings in SFNT-based font formats (i.e., TrueType and OpenType).</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_OPENTYPE_VALIDATE_H">FT_OPENTYPE_VALIDATE_H</h3>
<pre>
#define <b>FT_OPENTYPE_VALIDATE_H</b>  &lt;freetype/ftotval.h&gt;
</pre>

<p>A macro used in #include statements to name the file containing the optional FreeType&nbsp;2 API which validates OpenType tables (BASE, GDEF, GPOS, GSUB, JSTF).</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_GX_VALIDATE_H">FT_GX_VALIDATE_H</h3>
<pre>
#define <b>FT_GX_VALIDATE_H</b>  &lt;freetype/ftgxval.h&gt;
</pre>

<p>A macro used in #include statements to name the file containing the optional FreeType&nbsp;2 API which validates TrueTypeGX/AAT tables (feat, mort, morx, bsln, just, kern, opbd, trak, prop).</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_PFR_H">FT_PFR_H</h3>
<pre>
#define <b>FT_PFR_H</b>  &lt;freetype/ftpfr.h&gt;
</pre>

<p>A macro used in #include statements to name the file containing the FreeType&nbsp;2 API which accesses PFR-specific data.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_STROKER_H">FT_STROKER_H</h3>
<pre>
#define <b>FT_STROKER_H</b>  &lt;freetype/ftstroke.h&gt;
</pre>

<p>A macro used in #include statements to name the file containing the FreeType&nbsp;2 API which provides functions to stroke outline paths.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_SYNTHESIS_H">FT_SYNTHESIS_H</h3>
<pre>
#define <b>FT_SYNTHESIS_H</b>  &lt;freetype/ftsynth.h&gt;
</pre>

<p>A macro used in #include statements to name the file containing the FreeType&nbsp;2 API which performs artificial obliquing and emboldening.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_FONT_FORMATS_H">FT_FONT_FORMATS_H</h3>
<pre>
#define <b>FT_FONT_FORMATS_H</b>  &lt;freetype/ftfntfmt.h&gt;

  /* deprecated */
#define FT_XFREE86_H  <b>FT_FONT_FORMATS_H</b>
</pre>

<p>A macro used in #include statements to name the file containing the FreeType&nbsp;2 API which provides functions specific to font formats.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_TRIGONOMETRY_H">FT_TRIGONOMETRY_H</h3>
<pre>
#define <b>FT_TRIGONOMETRY_H</b>  &lt;freetype/fttrigon.h&gt;
</pre>

<p>A macro used in #include statements to name the file containing the FreeType&nbsp;2 API which performs trigonometric computations (e.g., cosines and arc tangents).</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_LCD_FILTER_H">FT_LCD_FILTER_H</h3>
<pre>
#define <b>FT_LCD_FILTER_H</b>  &lt;freetype/ftlcdfil.h&gt;
</pre>

<p>A macro used in #include statements to name the file containing the FreeType&nbsp;2 API which performs color filtering for subpixel rendering.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_INCREMENTAL_H">FT_INCREMENTAL_H</h3>
<pre>
#define <b>FT_INCREMENTAL_H</b>  &lt;freetype/ftincrem.h&gt;
</pre>

<p>A macro used in #include statements to name the file containing the FreeType&nbsp;2 API which performs incremental glyph loading.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_GASP_H">FT_GASP_H</h3>
<pre>
#define <b>FT_GASP_H</b>  &lt;freetype/ftgasp.h&gt;
</pre>

<p>A macro used in #include statements to name the file containing the FreeType&nbsp;2 API which returns entries from the TrueType GASP table.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_ADVANCES_H">FT_ADVANCES_H</h3>
<pre>
#define <b>FT_ADVANCES_H</b>  &lt;freetype/ftadvanc.h&gt;
</pre>

<p>A macro used in #include statements to name the file containing the FreeType&nbsp;2 API which returns individual and ranged glyph advances.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

</body>
</html>

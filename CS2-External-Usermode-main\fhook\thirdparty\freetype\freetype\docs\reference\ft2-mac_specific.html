<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"https://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>FreeType-2.9.1 API Reference</title>
<style type="text/css">
  a:link { color: #0000EF; }
  a:visited { color: #51188E; }
  a:hover { color: #FF0000; }

  body { font-family: Verdana, Geneva, Arial, Helvetica, serif;
         color: #000000;
         background: #FFFFFF;
         width: 87%;
         margin: auto; }

  div.section { width: 75%;
                margin: auto; }
  div.section hr { margin: 4ex 0 1ex 0; }
  div.section h4 { background-color: #EEEEFF;
                   font-size: medium;
                   font-style: oblique;
                   font-weight: bold;
                   margin: 3ex 0 1.5ex 9%;
                   padding: 0.3ex 0 0.3ex 1%; }
  div.section p { margin: 1.5ex 0 1.5ex 10%; }
  div.section pre { margin: 3ex 0 3ex 9%;
                    background-color: #D6E8FF;
                    padding: 2ex 0 2ex 1%; }
  div.section table.fields { width: 90%;
                             margin: 1.5ex 0 1.5ex 10%; }
  div.section table.toc { width: 95%;
                          margin: 1.5ex 0 1.5ex 5%; }
  div.timestamp { text-align: center;
                  font-size: 69%;
                  margin: 1.5ex 0 1.5ex 0; }

  h1 { text-align: center; }
  h3 { font-size: medium;
       margin: 4ex 0 1.5ex 0; }

  p { text-align: justify; }

  pre.colored { color: blue; }

  span.keyword { font-family: monospace;
                 text-align: left;
                 white-space: pre;
                 color: darkblue; }

  table.fields td.val { font-weight: bold;
                        text-align: right;
                        width: 30%;
                        vertical-align: baseline;
                        padding: 1ex 1em 1ex 0; }
  table.fields td.desc { vertical-align: baseline;
                         padding: 1ex 0 1ex 1em; }
  table.fields td.desc p:first-child { margin: 0; }
  table.fields td.desc p { margin: 1.5ex 0 0 0; }
  table.index { margin: 6ex auto 6ex auto;
                border: 0;
                border-collapse: separate;
                border-spacing: 1em 0.3ex; }
  table.index tr { padding: 0; }
  table.index td { padding: 0; }
  table.index-toc-link { width: 100%;
                         border: 0;
                         border-spacing: 0;
                         margin: 1ex 0 1ex 0; }
  table.index-toc-link td.left { padding: 0 0.5em 0 0.5em;
                                 font-size: 83%;
                                 text-align: left; }
  table.index-toc-link td.middle { padding: 0 0.5em 0 0.5em;
                                   font-size: 83%;
                                   text-align: center; }
  table.index-toc-link td.right { padding: 0 0.5em 0 0.5em;
                                  font-size: 83%;
                                  text-align: right; }
  table.synopsis { margin: 6ex auto 6ex auto;
                   border: 0;
                   border-collapse: separate;
                   border-spacing: 2em 0.6ex; }
  table.synopsis tr { padding: 0; }
  table.synopsis td { padding: 0; }
  table.toc td.link { width: 30%;
                      text-align: right;
                      vertical-align: baseline;
                      padding: 1ex 1em 1ex 0; }
  table.toc td.desc { vertical-align: baseline;
                      padding: 1ex 0 1ex 1em;
                      text-align: left; }
  table.toc td.desc p:first-child { margin: 0;
                                    text-align: left; }
  table.toc td.desc p { margin: 1.5ex 0 0 0;
                        text-align: left; }

</style>
</head>
<body>

<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table>
<h1>FreeType-2.9.1 API Reference</h1>

<h1 id="mac_specific">Mac Specific Interface</h1>
<h2>Synopsis</h2>
<table class="synopsis">
<tr><td><a href="#FT_New_Face_From_FOND">FT_New_Face_From_FOND</a></td><td><a href="#FT_GetFilePath_From_Mac_ATS_Name">FT_GetFilePath_From_Mac_ATS_Name</a></td></tr>
<tr><td><a href="#FT_GetFile_From_Mac_Name">FT_GetFile_From_Mac_Name</a></td><td><a href="#FT_New_Face_From_FSSpec">FT_New_Face_From_FSSpec</a></td></tr>
<tr><td><a href="#FT_GetFile_From_Mac_ATS_Name">FT_GetFile_From_Mac_ATS_Name</a></td><td><a href="#FT_New_Face_From_FSRef">FT_New_Face_From_FSRef</a></td></tr>
</table>


<p>The following definitions are only available if FreeType is compiled on a Macintosh.</p>

<div class="section">
<h3 id="FT_New_Face_From_FOND">FT_New_Face_From_FOND</h3>
<p>Defined in FT_MAC_H (freetype/ftmac.h).</p>
<pre>
  FT_EXPORT( <a href="ft2-basic_types.html#FT_Error">FT_Error</a> )
  <b>FT_New_Face_From_FOND</b>( <a href="ft2-base_interface.html#FT_Library">FT_Library</a>  library,
                         Handle      fond,
                         <a href="ft2-basic_types.html#FT_Long">FT_Long</a>     face_index,
                         <a href="ft2-base_interface.html#FT_Face">FT_Face</a>    *aface )
                       FT_DEPRECATED_ATTRIBUTE;
</pre>

<p>Create a new face object from a FOND resource.</p>

<h4>inout</h4>
<table class="fields">
<tr><td class="val" id="library">library</td><td class="desc">
<p>A handle to the library resource.</p>
</td></tr>
</table>

<h4>input</h4>
<table class="fields">
<tr><td class="val" id="fond">fond</td><td class="desc">
<p>A FOND resource.</p>
</td></tr>
<tr><td class="val" id="face_index">face_index</td><td class="desc">
<p>Only supported for the -1 &lsquo;sanity check&rsquo; special case.</p>
</td></tr>
</table>

<h4>output</h4>
<table class="fields">
<tr><td class="val" id="aface">aface</td><td class="desc">
<p>A handle to a new face object.</p>
</td></tr>
</table>

<h4>return</h4>
<p>FreeType error code. 0&nbsp;means success.</p>

<h4>notes</h4>
<p>This function can be used to create <a href="ft2-base_interface.html#FT_Face">FT_Face</a> objects from fonts that are installed in the system as follows.</p>
<pre class="colored">
  fond = GetResource( 'FOND', fontName );
  error = FT_New_Face_From_FOND( library, fond, 0, &amp;face );
</pre>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_GetFile_From_Mac_Name">FT_GetFile_From_Mac_Name</h3>
<p>Defined in FT_MAC_H (freetype/ftmac.h).</p>
<pre>
  FT_EXPORT( <a href="ft2-basic_types.html#FT_Error">FT_Error</a> )
  <b>FT_GetFile_From_Mac_Name</b>( <span class="keyword">const</span> <span class="keyword">char</span>*  fontName,
                            FSSpec*      pathSpec,
                            <a href="ft2-basic_types.html#FT_Long">FT_Long</a>*     face_index )
                          FT_DEPRECATED_ATTRIBUTE;
</pre>

<p>Return an FSSpec for the disk file containing the named font.</p>

<h4>input</h4>
<table class="fields">
<tr><td class="val" id="fontName">fontName</td><td class="desc">
<p>Mac OS name of the font (e.g., Times New Roman Bold).</p>
</td></tr>
</table>

<h4>output</h4>
<table class="fields">
<tr><td class="val" id="pathSpec">pathSpec</td><td class="desc">
<p>FSSpec to the file. For passing to <a href="ft2-mac_specific.html#FT_New_Face_From_FSSpec">FT_New_Face_From_FSSpec</a>.</p>
</td></tr>
<tr><td class="val" id="face_index">face_index</td><td class="desc">
<p>Index of the face. For passing to <a href="ft2-mac_specific.html#FT_New_Face_From_FSSpec">FT_New_Face_From_FSSpec</a>.</p>
</td></tr>
</table>

<h4>return</h4>
<p>FreeType error code. 0&nbsp;means success.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_GetFile_From_Mac_ATS_Name">FT_GetFile_From_Mac_ATS_Name</h3>
<p>Defined in FT_MAC_H (freetype/ftmac.h).</p>
<pre>
  FT_EXPORT( <a href="ft2-basic_types.html#FT_Error">FT_Error</a> )
  <b>FT_GetFile_From_Mac_ATS_Name</b>( <span class="keyword">const</span> <span class="keyword">char</span>*  fontName,
                                FSSpec*      pathSpec,
                                <a href="ft2-basic_types.html#FT_Long">FT_Long</a>*     face_index )
                              FT_DEPRECATED_ATTRIBUTE;
</pre>

<p>Return an FSSpec for the disk file containing the named font.</p>

<h4>input</h4>
<table class="fields">
<tr><td class="val" id="fontName">fontName</td><td class="desc">
<p>Mac OS name of the font in ATS framework.</p>
</td></tr>
</table>

<h4>output</h4>
<table class="fields">
<tr><td class="val" id="pathSpec">pathSpec</td><td class="desc">
<p>FSSpec to the file. For passing to <a href="ft2-mac_specific.html#FT_New_Face_From_FSSpec">FT_New_Face_From_FSSpec</a>.</p>
</td></tr>
<tr><td class="val" id="face_index">face_index</td><td class="desc">
<p>Index of the face. For passing to <a href="ft2-mac_specific.html#FT_New_Face_From_FSSpec">FT_New_Face_From_FSSpec</a>.</p>
</td></tr>
</table>

<h4>return</h4>
<p>FreeType error code. 0&nbsp;means success.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_GetFilePath_From_Mac_ATS_Name">FT_GetFilePath_From_Mac_ATS_Name</h3>
<p>Defined in FT_MAC_H (freetype/ftmac.h).</p>
<pre>
  FT_EXPORT( <a href="ft2-basic_types.html#FT_Error">FT_Error</a> )
  <b>FT_GetFilePath_From_Mac_ATS_Name</b>( <span class="keyword">const</span> <span class="keyword">char</span>*  fontName,
                                    UInt8*       path,
                                    UInt32       maxPathSize,
                                    <a href="ft2-basic_types.html#FT_Long">FT_Long</a>*     face_index )
                                  FT_DEPRECATED_ATTRIBUTE;
</pre>

<p>Return a pathname of the disk file and face index for given font name that is handled by ATS framework.</p>

<h4>input</h4>
<table class="fields">
<tr><td class="val" id="fontName">fontName</td><td class="desc">
<p>Mac OS name of the font in ATS framework.</p>
</td></tr>
</table>

<h4>output</h4>
<table class="fields">
<tr><td class="val" id="path">path</td><td class="desc">
<p>Buffer to store pathname of the file. For passing to <a href="ft2-base_interface.html#FT_New_Face">FT_New_Face</a>. The client must allocate this buffer before calling this function.</p>
</td></tr>
<tr><td class="val" id="maxPathSize">maxPathSize</td><td class="desc">
<p>Lengths of the buffer &lsquo;path&rsquo; that client allocated.</p>
</td></tr>
<tr><td class="val" id="face_index">face_index</td><td class="desc">
<p>Index of the face. For passing to <a href="ft2-base_interface.html#FT_New_Face">FT_New_Face</a>.</p>
</td></tr>
</table>

<h4>return</h4>
<p>FreeType error code. 0&nbsp;means success.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_New_Face_From_FSSpec">FT_New_Face_From_FSSpec</h3>
<p>Defined in FT_MAC_H (freetype/ftmac.h).</p>
<pre>
  FT_EXPORT( <a href="ft2-basic_types.html#FT_Error">FT_Error</a> )
  <b>FT_New_Face_From_FSSpec</b>( <a href="ft2-base_interface.html#FT_Library">FT_Library</a>     library,
                           <span class="keyword">const</span> FSSpec  *spec,
                           <a href="ft2-basic_types.html#FT_Long">FT_Long</a>        face_index,
                           <a href="ft2-base_interface.html#FT_Face">FT_Face</a>       *aface )
                         FT_DEPRECATED_ATTRIBUTE;
</pre>

<p>Create a new face object from a given resource and typeface index using an FSSpec to the font file.</p>

<h4>inout</h4>
<table class="fields">
<tr><td class="val" id="library">library</td><td class="desc">
<p>A handle to the library resource.</p>
</td></tr>
</table>

<h4>input</h4>
<table class="fields">
<tr><td class="val" id="spec">spec</td><td class="desc">
<p>FSSpec to the font file.</p>
</td></tr>
<tr><td class="val" id="face_index">face_index</td><td class="desc">
<p>The index of the face within the resource. The first face has index&nbsp;0.</p>
</td></tr>
</table>

<h4>output</h4>
<table class="fields">
<tr><td class="val" id="aface">aface</td><td class="desc">
<p>A handle to a new face object.</p>
</td></tr>
</table>

<h4>return</h4>
<p>FreeType error code. 0&nbsp;means success.</p>

<h4>note</h4>
<p><a href="ft2-mac_specific.html#FT_New_Face_From_FSSpec">FT_New_Face_From_FSSpec</a> is identical to <a href="ft2-base_interface.html#FT_New_Face">FT_New_Face</a> except it accepts an FSSpec instead of a path.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_New_Face_From_FSRef">FT_New_Face_From_FSRef</h3>
<p>Defined in FT_MAC_H (freetype/ftmac.h).</p>
<pre>
  FT_EXPORT( <a href="ft2-basic_types.html#FT_Error">FT_Error</a> )
  <b>FT_New_Face_From_FSRef</b>( <a href="ft2-base_interface.html#FT_Library">FT_Library</a>    library,
                          <span class="keyword">const</span> FSRef  *ref,
                          <a href="ft2-basic_types.html#FT_Long">FT_Long</a>       face_index,
                          <a href="ft2-base_interface.html#FT_Face">FT_Face</a>      *aface )
                        FT_DEPRECATED_ATTRIBUTE;
</pre>

<p>Create a new face object from a given resource and typeface index using an FSRef to the font file.</p>

<h4>inout</h4>
<table class="fields">
<tr><td class="val" id="library">library</td><td class="desc">
<p>A handle to the library resource.</p>
</td></tr>
</table>

<h4>input</h4>
<table class="fields">
<tr><td class="val" id="spec">spec</td><td class="desc">
<p>FSRef to the font file.</p>
</td></tr>
<tr><td class="val" id="face_index">face_index</td><td class="desc">
<p>The index of the face within the resource. The first face has index&nbsp;0.</p>
</td></tr>
</table>

<h4>output</h4>
<table class="fields">
<tr><td class="val" id="aface">aface</td><td class="desc">
<p>A handle to a new face object.</p>
</td></tr>
</table>

<h4>return</h4>
<p>FreeType error code. 0&nbsp;means success.</p>

<h4>note</h4>
<p><a href="ft2-mac_specific.html#FT_New_Face_From_FSRef">FT_New_Face_From_FSRef</a> is identical to <a href="ft2-base_interface.html#FT_New_Face">FT_New_Face</a> except it accepts an FSRef instead of a path.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

</body>
</html>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"https://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>FreeType-2.9.1 API Reference</title>
<style type="text/css">
  a:link { color: #0000EF; }
  a:visited { color: #51188E; }
  a:hover { color: #FF0000; }

  body { font-family: Verdana, Geneva, Arial, Helvetica, serif;
         color: #000000;
         background: #FFFFFF;
         width: 87%;
         margin: auto; }

  div.section { width: 75%;
                margin: auto; }
  div.section hr { margin: 4ex 0 1ex 0; }
  div.section h4 { background-color: #EEEEFF;
                   font-size: medium;
                   font-style: oblique;
                   font-weight: bold;
                   margin: 3ex 0 1.5ex 9%;
                   padding: 0.3ex 0 0.3ex 1%; }
  div.section p { margin: 1.5ex 0 1.5ex 10%; }
  div.section pre { margin: 3ex 0 3ex 9%;
                    background-color: #D6E8FF;
                    padding: 2ex 0 2ex 1%; }
  div.section table.fields { width: 90%;
                             margin: 1.5ex 0 1.5ex 10%; }
  div.section table.toc { width: 95%;
                          margin: 1.5ex 0 1.5ex 5%; }
  div.timestamp { text-align: center;
                  font-size: 69%;
                  margin: 1.5ex 0 1.5ex 0; }

  h1 { text-align: center; }
  h3 { font-size: medium;
       margin: 4ex 0 1.5ex 0; }

  p { text-align: justify; }

  pre.colored { color: blue; }

  span.keyword { font-family: monospace;
                 text-align: left;
                 white-space: pre;
                 color: darkblue; }

  table.fields td.val { font-weight: bold;
                        text-align: right;
                        width: 30%;
                        vertical-align: baseline;
                        padding: 1ex 1em 1ex 0; }
  table.fields td.desc { vertical-align: baseline;
                         padding: 1ex 0 1ex 1em; }
  table.fields td.desc p:first-child { margin: 0; }
  table.fields td.desc p { margin: 1.5ex 0 0 0; }
  table.index { margin: 6ex auto 6ex auto;
                border: 0;
                border-collapse: separate;
                border-spacing: 1em 0.3ex; }
  table.index tr { padding: 0; }
  table.index td { padding: 0; }
  table.index-toc-link { width: 100%;
                         border: 0;
                         border-spacing: 0;
                         margin: 1ex 0 1ex 0; }
  table.index-toc-link td.left { padding: 0 0.5em 0 0.5em;
                                 font-size: 83%;
                                 text-align: left; }
  table.index-toc-link td.middle { padding: 0 0.5em 0 0.5em;
                                   font-size: 83%;
                                   text-align: center; }
  table.index-toc-link td.right { padding: 0 0.5em 0 0.5em;
                                  font-size: 83%;
                                  text-align: right; }
  table.synopsis { margin: 6ex auto 6ex auto;
                   border: 0;
                   border-collapse: separate;
                   border-spacing: 2em 0.6ex; }
  table.synopsis tr { padding: 0; }
  table.synopsis td { padding: 0; }
  table.toc td.link { width: 30%;
                      text-align: right;
                      vertical-align: baseline;
                      padding: 1ex 1em 1ex 0; }
  table.toc td.desc { vertical-align: baseline;
                      padding: 1ex 0 1ex 1em;
                      text-align: left; }
  table.toc td.desc p:first-child { margin: 0;
                                    text-align: left; }
  table.toc td.desc p { margin: 1.5ex 0 0 0;
                        text-align: left; }

</style>
</head>
<body>

<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td></tr></table>
<h1>FreeType-2.9.1 API Reference</h1>

<h1>Table of Contents</h1>
<div class="section">
<h2>General Remarks</h2>
<table class="toc">
<tr><td class="link"><a href="ft2-header_inclusion.html">FreeType's header inclusion scheme</a></td><td class="desc">
<p>How client applications should include FreeType header files.</p>
</td></tr>
<tr><td class="link"><a href="ft2-user_allocation.html">User allocation</a></td><td class="desc">
<p>How client applications should allocate FreeType data structures.</p>
</td></tr>
</table>
</div>
<div class="section">
<h2>Core API</h2>
<table class="toc">
<tr><td class="link"><a href="ft2-version.html">FreeType Version</a></td><td class="desc">
<p>Functions and macros related to FreeType versions.</p>
</td></tr>
<tr><td class="link"><a href="ft2-basic_types.html">Basic Data Types</a></td><td class="desc">
<p>The basic data types defined by the library.</p>
</td></tr>
<tr><td class="link"><a href="ft2-base_interface.html">Base Interface</a></td><td class="desc">
<p>The FreeType&nbsp;2 base font interface.</p>
</td></tr>
<tr><td class="link"><a href="ft2-glyph_variants.html">Unicode Variation Sequences</a></td><td class="desc">
<p>The FreeType&nbsp;2 interface to Unicode Variation Sequences (UVS), using the SFNT cmap format&nbsp;14.</p>
</td></tr>
<tr><td class="link"><a href="ft2-glyph_management.html">Glyph Management</a></td><td class="desc">
<p>Generic interface to manage individual glyph data.</p>
</td></tr>
<tr><td class="link"><a href="ft2-mac_specific.html">Mac Specific Interface</a></td><td class="desc">
<p>Only available on the Macintosh.</p>
</td></tr>
<tr><td class="link"><a href="ft2-sizes_management.html">Size Management</a></td><td class="desc">
<p>Managing multiple sizes per face.</p>
</td></tr>
<tr><td class="link"><a href="ft2-header_file_macros.html">Header File Macros</a></td><td class="desc">
<p>Macro definitions used to #include specific header files.</p>
</td></tr>
</table>
</div>
<div class="section">
<h2>Format-Specific API</h2>
<table class="toc">
<tr><td class="link"><a href="ft2-multiple_masters.html">Multiple Masters</a></td><td class="desc">
<p>How to manage Multiple Masters fonts.</p>
</td></tr>
<tr><td class="link"><a href="ft2-truetype_tables.html">TrueType Tables</a></td><td class="desc">
<p>TrueType specific table types and functions.</p>
</td></tr>
<tr><td class="link"><a href="ft2-type1_tables.html">Type 1 Tables</a></td><td class="desc">
<p>Type&nbsp;1 (PostScript) specific font tables.</p>
</td></tr>
<tr><td class="link"><a href="ft2-sfnt_names.html">SFNT Names</a></td><td class="desc">
<p>Access the names embedded in TrueType and OpenType files.</p>
</td></tr>
<tr><td class="link"><a href="ft2-bdf_fonts.html">BDF and PCF Files</a></td><td class="desc">
<p>BDF and PCF specific API.</p>
</td></tr>
<tr><td class="link"><a href="ft2-cid_fonts.html">CID Fonts</a></td><td class="desc">
<p>CID-keyed font specific API.</p>
</td></tr>
<tr><td class="link"><a href="ft2-pfr_fonts.html">PFR Fonts</a></td><td class="desc">
<p>PFR/TrueDoc specific API.</p>
</td></tr>
<tr><td class="link"><a href="ft2-winfnt_fonts.html">Window FNT Files</a></td><td class="desc">
<p>Windows FNT specific API.</p>
</td></tr>
<tr><td class="link"><a href="ft2-font_formats.html">Font Formats</a></td><td class="desc">
<p>Getting the font format.</p>
</td></tr>
<tr><td class="link"><a href="ft2-gasp_table.html">Gasp Table</a></td><td class="desc">
<p>Retrieving TrueType &lsquo;gasp&rsquo; table entries.</p>
</td></tr>
</table>
</div>
<div class="section">
<h2>Controlling FreeType Modules</h2>
<table class="toc">
<tr><td class="link"><a href="ft2-auto_hinter.html">The auto-hinter</a></td><td class="desc">
<p>Controlling the auto-hinting module.</p>
</td></tr>
<tr><td class="link"><a href="ft2-cff_driver.html">The CFF driver</a></td><td class="desc">
<p>Controlling the CFF driver module.</p>
</td></tr>
<tr><td class="link"><a href="ft2-t1_cid_driver.html">The Type 1 and CID drivers</a></td><td class="desc">
<p>Controlling the Type&nbsp;1 and CID driver modules.</p>
</td></tr>
<tr><td class="link"><a href="ft2-tt_driver.html">The TrueType driver</a></td><td class="desc">
<p>Controlling the TrueType driver module.</p>
</td></tr>
<tr><td class="link"><a href="ft2-pcf_driver.html">The PCF driver</a></td><td class="desc">
<p>Controlling the PCF driver module.</p>
</td></tr>
<tr><td class="link"><a href="ft2-properties.html">Driver properties</a></td><td class="desc">
<p>Controlling driver modules.</p>
</td></tr>
<tr><td class="link"><a href="ft2-parameter_tags.html">Parameter Tags</a></td><td class="desc">
<p>Macros for driver property and font loading parameter tags.</p>
</td></tr>
</table>
</div>
<div class="section">
<h2>Cache Sub-System</h2>
<table class="toc">
<tr><td class="link"><a href="ft2-cache_subsystem.html">Cache Sub-System</a></td><td class="desc">
<p>How to cache face, size, and glyph data with FreeType&nbsp;2.</p>
</td></tr>
</table>
</div>
<div class="section">
<h2>Support API</h2>
<table class="toc">
<tr><td class="link"><a href="ft2-computations.html">Computations</a></td><td class="desc">
<p>Crunching fixed numbers and vectors.</p>
</td></tr>
<tr><td class="link"><a href="ft2-list_processing.html">List Processing</a></td><td class="desc">
<p>Simple management of lists.</p>
</td></tr>
<tr><td class="link"><a href="ft2-outline_processing.html">Outline Processing</a></td><td class="desc">
<p>Functions to create, transform, and render vectorial glyph images.</p>
</td></tr>
<tr><td class="link"><a href="ft2-quick_advance.html">Quick retrieval of advance values</a></td><td class="desc">
<p>Retrieve horizontal and vertical advance values without processing glyph outlines, if possible.</p>
</td></tr>
<tr><td class="link"><a href="ft2-bitmap_handling.html">Bitmap Handling</a></td><td class="desc">
<p>Handling FT_Bitmap objects.</p>
</td></tr>
<tr><td class="link"><a href="ft2-raster.html">Scanline Converter</a></td><td class="desc">
<p>How vectorial outlines are converted into bitmaps and pixmaps.</p>
</td></tr>
<tr><td class="link"><a href="ft2-glyph_stroker.html">Glyph Stroker</a></td><td class="desc">
<p>Generating bordered and stroked glyphs.</p>
</td></tr>
<tr><td class="link"><a href="ft2-system_interface.html">System Interface</a></td><td class="desc">
<p>How FreeType manages memory and i/o.</p>
</td></tr>
<tr><td class="link"><a href="ft2-module_management.html">Module Management</a></td><td class="desc">
<p>How to add, upgrade, remove, and control modules from FreeType.</p>
</td></tr>
<tr><td class="link"><a href="ft2-gzip.html">GZIP Streams</a></td><td class="desc">
<p>Using gzip-compressed font files.</p>
</td></tr>
<tr><td class="link"><a href="ft2-lzw.html">LZW Streams</a></td><td class="desc">
<p>Using LZW-compressed font files.</p>
</td></tr>
<tr><td class="link"><a href="ft2-bzip2.html">BZIP2 Streams</a></td><td class="desc">
<p>Using bzip2-compressed font files.</p>
</td></tr>
<tr><td class="link"><a href="ft2-lcd_filtering.html">LCD Filtering</a></td><td class="desc">
<p>Reduce color fringes of subpixel-rendered bitmaps.</p>
</td></tr>
</table>
</div>
<div class="section">
<h2>Error Codes</h2>
<table class="toc">
<tr><td class="link"><a href="ft2-error_enumerations.html">Error Enumerations</a></td><td class="desc">
<p>How to handle errors and error strings.</p>
</td></tr>
<tr><td class="link"><a href="ft2-error_code_values.html">Error Code Values</a></td><td class="desc">
<p>All possible error codes returned by FreeType functions.</p>
</td></tr>
</table>
</div>
<div class="section">
<h2>Miscellaneous</h2>
<table class="toc">
<tr><td class="link"><a href="ft2-ot_validation.html">OpenType Validation</a></td><td class="desc">
<p>An API to validate OpenType tables.</p>
</td></tr>
<tr><td class="link"><a href="ft2-incremental.html">Incremental Loading</a></td><td class="desc">
<p>Custom Glyph Loading.</p>
</td></tr>
<tr><td class="link"><a href="ft2-truetype_engine.html">The TrueType Engine</a></td><td class="desc">
<p>TrueType bytecode support.</p>
</td></tr>
<tr><td class="link"><a href="ft2-gx_validation.html">TrueTypeGX/AAT Validation</a></td><td class="desc">
<p>An API to validate TrueTypeGX/AAT tables.</p>
</td></tr>
</table>
</div>
<div class="section">
<h2><a href="ft2-index.html">Global Index</a></h2></div>
<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td></tr></table>

<div class="timestamp">generated on Tue May  1 23:34:43 2018</div></body>
</html>

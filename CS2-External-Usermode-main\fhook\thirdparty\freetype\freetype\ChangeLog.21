2005-06-08  <PERSON>  <<EMAIL>>


	* Version 2.1.10 released.
	==========================


	* src/pcf/readme: Renamed to...
	* src/pcf/README: This.

2005-06-07  <PERSON><PERSON><PERSON>  <<EMAIL>>

	* builds/amiga/*: Added copyright notes, reworked some comments.

2005-06-05  <PERSON>  <<EMAIL>>

	* Add copyright notices to all files which don't have one.

	* docs/license.txt: Renamed to...
	* docs/LICENSE.TXT: This.
	* docs/FTL.txt: Renamed to...
	* docs/FTL.TXT: This.
	* docs/GPL.txt: Renamed to...
	* docs/GPL.TXT: This.

	* docs/PATENTS: Slightly reworded.  Suggested by <PERSON><PERSON><PERSON><PERSON>
	<<EMAIL>>.

2005-06-04  <PERSON>  <<EMAIL>>

	* include/freetype/ftimage.h (FT_Outline_MoveTo<PERSON>,
	FT_Outline_<PERSON>o<PERSON>c, FT_Outline_ConicToFunc,
	FT_Outline_CubicToFunc, FT_Raster_RenderFunc),
	include/freetype/ftrender.h (FT_Glyph_TransformFunc,
	FT_Renderer_RenderFunc, FT_Renderer_TransformFunc): Don't use
	`const' to stay compatible with FreeType 2.1.9.

2005-06-01  Adam D. Moss  <<EMAIL>>

	* src/base/ftstroke.c (ft_stroker_inside): Revert `sigma' patch from
	2004-07-11; this gives much better results under normal
	circumstances.

2005-05-30  Chia I Wu  <<EMAIL>>

	* include/freetype/ftbitmap.h (FT_Bitmap_Embolden): Minor
	documentation improvements.

	* include/freetype/ftoutln.h (FT_Outline_Embolden): Fix typos.

	* src/base/ftbitmap.c (FT_Bitmap_Embolden): Add support for bitmap
	of pixel_mode FT_PIXEL_MODE_GRAY2 or FT_PIXEL_MODE_GRAY4.
	If xstr is larger than 8 and bitmap is of pixel_mode
	FT_PIXEL_MODE_MONO, set xstr to 8 instead of returning error.

2005-05-29  Chia I Wu  <<EMAIL>>

	* src/base/ftbitmap.c (FT_Bitmap_Embolden): Fix emboldening bitmap
	of mode FT_PIXEL_MODE_GRAY.  Also add support for mode
	FT_PIXEL_MODE_LCD and FT_PIXEL_MODE_LCD_V.
	(ft_bitmap_assure_buffer): FT_PIXEL_MODE_LCD and FT_PIXEL_MODE_LCD_V
	should have ppb (pixel per byte) 1.
	Zero the padding when there's no need to allocate memory.

	* src/base/ftsynth.c (FT_GlyphSlot_Embolden): Handle slot->advance
	too.
	More suited emboldening strength.

2005-05-28  Chia I Wu  <<EMAIL>>

	* src/base/ftbitmap.c (FT_Bitmap_Embolden): Handle negative pitch.
	Handle FT_PIXEL_MODE_GRAY with num_gray != 256.
	Improve speed for FT_PIXEL_MODE_GRAY.
	(ft_bitmap_assure_buffer): Accept FT_PIXEL_MODE_LCD and
	FT_PIXEL_MODE_LCD_V.

2005-05-27  Chia I Wu  <<EMAIL>>

	* src/base/ftsynth.c (FT_GlyphSlot_Embolden): Initialize `error'.

	* src/base/ftobjs.c (ft_cmap_done_internal): New function.
	(FT_CMap_Done): Remove cmap from cmap list.
	(destroy_charmaps, FT_CMap_New): Don't call FT_CMap_Done but
	ft_cmap_done_internal.

2005-05-26  Werner Lemberg  <<EMAIL>>

	* docs/GPL.txt: Update postal address of FSF.

2005-05-26  Chia I Wu  <<EMAIL>>

	* include/freetype/ftbitmap.h (FT_Bitmap_Embolden): Improve
	documentation.

	* src/base/ftsynth.c (FT_BOLD_THRESHOLD): Removed.
	(FT_GlyphSlot_Embolden): Check whether slot is bitmap owner.
	Always modify the metrics.

2005-05-24  Werner Lemberg  <<EMAIL>>

	* docs/CHANGES: Updated.

2005-05-24  Chia I Wu  <<EMAIL>>

	* include/freetype/ftbitmap.h (FT_Bitmap_Embolden): New declaration.

	* include/freetype/ftoutln.h (FT_Outline_Embolden): New declaration.

	* src/base/ftbitmap.c (ft_bitmap_assure_buffer): New auxiliary
	function.
	(FT_Bitmap_Embolden): New function.

	* src/base/ftoutln.c (FT_Outline_Embolden): New function.

	* src/base/ftsynth.c: Don't include FT_INTERNAL_CALC_H and
	FT_TRIGONOMETRY_H but FT_BITMAP_H.
	(FT_GlyphSlot_Embolden): Use FT_Outline_Embolden or
	FT_Bitmap_Embolden.

2005-05-24  Werner Lemberg  <<EMAIL>>

	* configure: Always remove config.mk, builds/unix/unix-def.mk, and
	builds/unix/unix-cc.mk.  This fixes repeated calls of the script.
	Reported by Nelson Beebe and Behdad Esfahbod.

	* README.CVS: Mention file permissions.

2005-05-23  Werner Lemberg  <<EMAIL>>

	* builds/amiga/makefile.os4 (WARNINGS), builds/compiler/gcc-dev.mk
	(CFLAGS), builds/compiler/gcc.mk (CFLAGS): Remove
	-fno-strict-aliasing.

	* src/sfnt/rules.mk (SFNT_DRV_SRC): Don't include ttsbit0.c --
	it is currently loaded from ttsbit.c.

2005-05-23  Behdad Esfahbod  <<EMAIL>>

	Say you have `(Foo*)x' and want to assign, pass, or return it as
	`(Bar*)'.  If you simply say `x' or `(Bar*)x', then the C compiler
	would warn you that type casting incompatible pointer types breaks
	strict-aliasing.  The solution is to cast to `(void*)' instead which
	is the generic pointer type, so the compiler knows that it should
	make no strict-aliasing assumption on `x'.  But the problem with
	`(void*)x' is that seems like in C++, unlike C, `void*' is not a
	generic pointer type and assigning `void*' to `Bar*' without a cast
	causes an error.  The solution is to cast to `Bar*' too, with
	`(Bar*)(void*)x' as the result -- this is what the patch does.

	* include/freetype/cache/ftccache.h (FTC_CACHE_LOOKUP_CMP),
	include/freetype/cache/ftcmru.h (FTC_MRULIST_LOOKUP_CMP): Remove
	cast on lvalue, use a temporary pointer instead.
	Cast temporarily to (void*) to not break strict aliasing.

	* include/freetype/internal/ftmemory.h (FT_MEM_ALLOC,
	FT_MEM_REALLOC, FT_MEM_QALLOC, FT_MEM_QREALLOC, FT_MEM_FREE),
	src/base/ftglyph.c (FT_Glyph_To_Bitmap): Cast temporarily to (void*)
	to not break strict aliasing.

	* src/base/ftinit.c (FT_USE_MODULE): Fix wrong type information.

	* builds/unix/configure.ac (XX_CFLAGS): Remove -fno-strict-aliasing.

2005-05-23  David Turner  <<EMAIL>>

	Fix Savannah bug #12213 (incorrect behaviour of the cache sub-system
	in low-memory conditions).

	* include/freetype/cache/ftccache.h (FTC_CACHE_TRYLOOP,
	FTC_CACHE_TRYLOOP_END): New macros.

	* src/cache/ftccache.c (FTC_Cache_NewNode), src/cache/ftcsbits.c
	(ftc_snode_compare): Use FT_CACHE_TRYLOOP and FTC_CACHE_TRYLOOP_END.

2005-05-23  Werner Lemberg  <<EMAIL>>

	* src/base/rules.mk (BASE_SRC): Don't add ftsynth.c here but...
	(BASE_EXT_SRC): Here.

2005-05-22  Werner Lemberg  <<EMAIL>>

	* src/base/ftrfork.c (raccess_guess_apple_generic): Mark
	`version_number' and `entry_length' as unused.
	(raccess_guess_linux_double_from_file_name): Remove `memory'.
	(raccess_make_file_name): Mark `error' as unused.

	* src/bdf/bdflib.c (_bdf_parse_properties): Remove `memory'.

	* src/cid/cidobjs.c (cid_face_init): Remove `psnames'.

	* src/sfnt/sfobjs.c (sfnt_load_face): Remove `memory'.

	* src/truetype/ttgxvar.c (ft_var_readpackedpoints,
	ft_var_readpackeddeltas, ft_var_load_avar): Mark `error' as unused.

	* src/base/rules.mk (BASE_SRC): Add ftsynth.c.

2005-05-21  David Turner  <<EMAIL>>

	* src/base/ftsynth.c (FT_GlyphSlot_Embolden): Fix a bug that
	produced unpleasant artefacts when trying to embolden very sharp
	corners.

2005-05-20  Werner Lemberg  <<EMAIL>>

	* docs/CHANGES: Updated.

2005-05-20  Chia I Wu  <<EMAIL>>

	* src/base/ftbitmap.c: Don't include FT_FREETYPE_H and FT_IMAGE_H
	but FT_BITMAP_H.
	(FT_Bitmap_Copy): New function (from ftglyph.c).

	* include/freetype/ftbitmap.h (FT_Bitmap_Copy): New public
	definition.

	* src/base/ftglyph.c: Include FT_BITMAP_H.
	(ft_bitmap_copy): Move to ftbitmap.c.
	(ft_bitmap_glyph_init): Remove `memory' variable.
	Create new bitmap object if FT_GLYPH_OWN_BITMAP isn't set.
	(ft_bitmap_glyph_copy): Use FT_Bitmap_Copy.
	(ft_bitmap_glyph_done): Use FT_Bitmap_Done.
	(ft_outline_glyph_init): Use FT_Outline_Copy.

	* src/base/ftoutln.c (FT_Outline_Copy): Handle source == target.
	(FT_Outline_Done_Internal): Check for valid `memory' pointer.
	(FT_Outline_Translate, FT_Outline_Reverse, FT_Outline_Render,
	FT_Outline_Transform): Check for valid `outline' pointer.

	* src/base/ftobjs.c (FT_New_GlyphSlot): Prepend glyph slot to
	face->glyph, otherwise a new second glyph slot cannot be created.
	(FT_Done_GlyphSlot): Fix memory leak.
	(FT_Open_Face): Updated -- face->glyph is already managed by
	FT_New_GlyphSlot.

	* src/type42/t42objs.c (T42_GlyphSlot_Done): Updated.

2005-05-20  Kirill Smelkov  <<EMAIL>>

	* include/freetype/ftimage.h (FT_Raster_Params),
	include/freetype/ftoutln.h (FT_Outline_Translate,
	FT_Outline_Transform), src/base/ftoutln.c (FT_Outline_Translate,
	FT_Outline_Transform): Decorate parameters with `const' where
	appropriate.
	Update all callers.

	* src/raster/ftraster.c (ft_black_reset), src/smooth/ftgrays.c
	(gray_raster_reset): Remove `const' from `pool_base' argument.

2005-05-18  Kirill Smelkov  <<EMAIL>>

	* src/raster/ftmisc.h: New file.  Only needed if ftraster.c is
	compiled as stand-alone.

	* src/raster/ftraster.c: Add comment how to compile as stand-alone.
	s/FT_CONFIG_OPTION_STATIC_RASTER/FT_STATIC_RASTER/.
	s/TT_STATIC_RASTER/FT_STATIC_RASTER/.
	[_STANDALONE_]: Include ftimage.h and ftmisc.h.
	(FT_TRACE1, FT_TRACE6, ft_memset, FT_MEM_ZERO): Define
	conditionally.
	(Render_Glyph, Render_Gray_Glyph): Return Raster_Err_None (or
	Raster_Err_Unsupported).
	(ft_black_new) [_STANDALONE_]: Fix type of `the_raster'.
	(ft_black_init, ft_black_reset, ft_black_set_mode, ft_black_render):
	Use `ras', not `raster'.
	(ft_black_done): Use FT_UNUSED_RASTER.
	(Horizontal_Sweep_Init, Horizontal_Sweep_Step,
	Horizontal_Gray_Sweep_Span): Use FT_UNUSED_RASTER.

2005-05-18  Werner Lemberg  <<EMAIL>>

	* docs/announce: Start updating.

	* docs/CHANGES: Updated.

2005-05-16  Vitaliy Pasternak  <<EMAIL>>

	* builds/win32/visualc/freetype.vcproj: Updated.
	Exclude debug info for `Release' versions to reduce library size.

2005-05-16  Werner Lemberg  <<EMAIL>>

	* src/base/ftobjs.c (FT_Open_Face): Make it work as documented, this
	is, ignore `aface' completely if face_index < 0.  Reported by David
	Osborn <<EMAIL>>.

2005-05-16  Kirill Smelkov  <<EMAIL>>

	* include/freetype/ftimage.h (FT_Outline_MoveToFunc,
	FT_Outline_LineTo_Func, FT_Outline_ConicToFunc,
	FT_Outline_CubicToFunc), src/smooth/ftgrays.c (gray_render_conic,
	gray_render_cubic, gray_move_to, gray_line_to, gray_conic_to,
	gray_cubic_to, gray_render_span, gray_sweep): Decorate parameters
	with `const' where appropriate.

2005-05-11  Kirill Smelkov  <<EMAIL>>

	* include/freetype/ftimage.h (FT_Raster_RenderFunc),
	include/freetype/ftrender.h (FT_Glyph_TransformFunc,
	FT_Renderer_Render_Func, FT_Renderer_TransformFunc),
	src/base/ftglyph.c (ft_outline_glyph_transform),
	src/raster/ftrend1.c (ft_raster1_transform, ft_raster1_render),
	src/smooth/ftgrays.c (FT_Outline_Decompose, gray_raster_render),
	src/smooth/ftsmooth.c (ft_smooth_transform,
	ft_smooth_render_generic, ft_smooth_render, ft_smooth_render_lcd,
	ft_smooth_render_lcd_v): Decorate parameters with `const' where
	appropriate.

	* src/raster/ftraster.c (RASTER_RENDER_POOL): Removed.  Obsolete.
	(ft_black_render): Decorate parameters with `const' where
	appropriate.

2005-05-11  Werner Lemberg  <<EMAIL>>

	* src/sfnt/ttcmap.c (tt_cmap4_set_range): Fix typo (FT_PEEK_SHORT ->
	FT_PEEK_USHORT) which caused crashes.  Reported by Ismail Donmez
	<<EMAIL>>.

2005-05-08  Werner Lemberg  <<EMAIL>>

	* include/freetype/internal/ftserv.h (FT_FACE_FIND_GLOBAL_SERVICE)
	[__cplusplus]: Fix typo.

2005-05-07  Werner Lemberg  <<EMAIL>>

	Handle unsorted SFNT type 4 cmaps correctly (reported by Dirck
	Blaskey <<EMAIL>>).

	* src/sfnt/ttcmap.h (TT_CMap): Add member `unsorted'.
	* src/sfnt/ttcmap.c: Use SFNT_Err_Ok where appropriate.

	(tt_cmap0_validate, tt_cmap2_validate, tt_cmap6_validate,
	tt_cmap8_validate, tt_cmap10_validate, tt_cmap12_validate): Use
	`FT_Error' as return type.
	(tt_cmap4_validate): Use `FT_Error' as return type.
	Return error code for unsorted cmap.
	(tt_cmap4_char_index, tt_cmap4_char_next): Use old code for unsorted
	cmaps.
	(tt_face_build_cmaps): Set `unsorted' variable in cmap.

2005-05-07  Werner Lemberg  <<EMAIL>>

	* src/truetype/ttpload.c (tt_face_get_location): Fix typo.

2005-05-06  Werner Lemberg  <<EMAIL>>

	* src/cff/cffobjs.c (cff_face_init): Set ppem value in top
	dictionary for SFNT-based CFF.

2005-05-05  Werner Lemberg  <<EMAIL>>

	Handle malformed `loca' table entries.

	* docs/TODO: Add some bugs which should be fixed.

	* include/freetype/internal/tttypes.h (TT_FaceRec): Add `glyf_len'
	element.

	* src/truetype/ttpload.c (tt_face_load_loca): Get length of `glyf'
	table.
	(tt_face_get_location): Fix computation of `asize' for malformed
	`loca' entries.

2005-05-01  David Turner  <<EMAIL>>

	* Jamfile: Remove `otvalid' from the list of compiled modules.

	* include/freetype/internal/ftserv.h: Add compiler pragmas to get
	rid of annoying warnings with Visual C++ compiler in maximum warning
	mode.

	* src/autofit/afhints.c, src/autofit/aflatin.c, src/base/ftstroke.c,
	src/bdf/bdfdrivr.c, src/cache/ftcbasic.c, src/cache/ftccmap.c,
	src/cache/ftcmanag.c, src/cff/cffload.c, src/cid/cidload.c,
	src/lzw/zopen.c, src/otvalid/otvgdef.c, src/pcf/pcfread.c,
	src/sfnt/sfobjs.c, src/truetype/ttgxvar.c: Remove compiler warnings.

2005-04-28  Werner Lemberg  <<EMAIL>>

	* docs/TODO: Updated.

2005-04-24  Werner Lemberg  <<EMAIL>>

	* src/otvalid/otvcommn.c
	(otv_GSUBGPOS_have_MarkAttachmentType_flag): Handle table == 0.

2005-04-16  Werner Lemberg  <<EMAIL>>

	* src/cff/cffobjs.c (cff_face_init): Set default upem value in top
	font dict also.
	Handle font matrix settings in subfonts.

	* src/cff/cffgload.c (cff_slot_load): Use the correct font matrix
	for CID-keyed fonts with subfonts.

	* docs/formats.txt: Updated.

2005-04-14  Kirill Smelkov  <<EMAIL>>

	* include/freetype/freetype.h (FT_Vector_Transform),
	include/freetype/ftimage.h (FT_Raster_Params),
	include/freetype/ftoutln.h, src/base/ftoutln.c (FT_Outline_Get_CBox,
	FT_Outline_Copy, FT_Outline_Transform, FT_Vector_Transform,
	FT_Outline_Get_Bitmap), src/raster/ftraster.c (ft_black_render),
	src/smooth/ftgrays.c (gray_raster_render): Decorate parameters with
	`const' where appropriate.

2005-04-14  Werner Lemberg  <<EMAIL>>

	* src/type1/t1load.c (parse_charstrings): Catch this non-standard
	beginning of the /CharStrings dictionary:

	  /CharStrings 118 dict def
	  Private begin
	  CharStrings begin

	* src/sfnt/ttsbit0.c (tt_sbit_decoder_load_image): Fix arguments
	to call of tt_sbit_decoder_load_bitmap.

2005-04-13  Werner Lemberg  <<EMAIL>>

	* docs/TODO: Updated.

	* autogen.sh: Use `--force' for all commands.

2005-04-09  Werner Lemberg  <<EMAIL>>

	* src/pshinter/pshalgo.c (ps_hints_apply): Change scaling values
	only if `fitted' is not zero.

2005-04-06  Werner Lemberg  <<EMAIL>>

	* src/truetype/ttgload.c (tt_face_get_metrics) [FT_OPTIMIZE_MEMORY]:
	Fix typo which sometimes causes wrong metrics for the last glyph.

2005-04-04  David Turner  <<EMAIL>>

	* devel/ftoption.h, include/freetype/config/ftoption.h
	(FT_OPTIMIZE_MEMORY): Comment out this macro for the upcoming 2.1.10
	release.
	(*_CHESTER_*): Removed.  No longer used.

	* src/autofit/afhints.c (af_axis_hints_new_segment,
	af_axis_hints_new_edge): Small tweak to use less heap memory.

2005-04-03  Werner Lemberg  <<EMAIL>>

	* src/type1/t1parse.c (T1_New_Parser): Relax the check for a valid
	first line in the font.

2005-04-03  Werner Lemberg  <<EMAIL>>

	* docs/CHANGES, include/freetype/freetype.h: Improve documentation
	of FT_Set_Pixel_Sizes and FT_Set_Char_Size.

2005-03-26  Detlef Würkner  <<EMAIL>>

	* builds/amiga/src/base/ftsystem.c (ft_amiga_stream_io): Fix buffer
	offsets after a large read.

2005-03-26  Werner Lemberg  <<EMAIL>>

	* src/autofit/afglobal.c (af_face_globals_get_metrics):
	s/index/gidx/.

	* src/sfnt/ttsbit0.c (tt_sbit_decoder_load_image): Fix compiler
	warnings.

	* src/sfnt/rules.mk (SFNT_DRV_SRC): Add ttsbit0.c.

	* src/sfnt/ttsbit0.h: Dummy file for build with `make'.

2005-03-26  Detlef Würkner  <<EMAIL>>

	Update of the Amiga port.

	* builds/amiga/makefile, builds/amiga/makefile.os4,
	builds/amiga/smakefile: Included the base extension files
	(ftbitmap.c, ftotval.c, ftpfr.c, ftstroke.c, ftxf86.c).

2005-03-25  Detlef Würkner  <<EMAIL>>

	Update of the Amiga port.

	* builds/amiga/makefile, builds/amiga/smakefile: Handle new modules.

	* builds/amiga/makefile.os4: Makefile for AmigaOS4 SDK.

	* builds/amiga/README: Updated.

	* builds/amiga/include/freetype/config/ftconfig.h: Handle gcc for
	AmigaOS4.

	* builds/amiga/include/freetype/config/ftmodule.h: Handle new
	modules.

	* builds/amiga/src/base/ftdebug.c: Updated to current version of
	default ftdebug.c.
	Add various include files and macros to have proper support for
	both AmigaOS4 and older AmigaOS versions.
	Don't declare KVPrintF explicitly.
	Replace getenv with GetVar.
	Actually enable debugging code.

	* builds/amiga/src/base/ftsystem.c: Major rewrite.

2005-03-23  Werner Lemberg  <<EMAIL>>

	* tests/*: Removed.

2005-03-23  Werner Lemberg  <<EMAIL>>

	* docs/CHANGES, docs/INSTALL.ANY: Updated.

	* include/freetype/ftmoderr.h: Replace `Autohint' with `Autofit'.
	Add `OTvalid'.

	* src/autofit/aferrors.h: New file.

	* src/autofit/afglobal.c, src/autofit/afhints.c,
	src/autofit/aflatin.c, src/autofit/afloader.c: s/FT_Err_/AF_Err_/.
	Include aferrors.h.

	* src/autofit/rules.mk (AUTOF_DRV_H): Include aferrors.h.

	* src/otvalid/otverror.h: s/FT_Mod_Err_OTV/FT_Mod_Err_OTvalid/.

2005-03-22  David Turner  <<EMAIL>>

	* src/autohint/*: Removed.
	* Jamfile: Updated.

2005-03-15  David Turner  <<EMAIL>>

	* src/bdf/bdflib.c: Remove compiler warnings.
	(hash_rehash, hash_init): Don't call FT_MEM_ZERO.
	(_bdf_list_t): Add `memory' field.
	(_bdf_list_init, _bdf_list_done, _bdf_list_ensure): New functions.
	(_bdf_shift, _bdf_join): Rename to...
	(_bdf_list_shift, _bdf_list_join): This.
	(_bdf_split): Renamed to...
	(_bdf_list_split): This.  Use new functions.
	(bdf_internal_readstream): Removed.
	(NO_SKIP): New macro.
	(_bdf_readstream): Rewritten.
	(bdf_create_property, _bdf_add_comment): Improve allocation.
	(_bdf_set_default_spacing, _bdf_parse_glyphs): Updated.  Improve
	allocation.
	(_bdf_parse_properties, _bdf_parse_start): Updated.
	(bdf_load_font): Updated to use new functions.

	* src/type1/t1parse.c (check_type1_format): New function.
	(T1_New_Parser): Use it to check font header before allocating
	anything on the heap.

	* src/type42/t42parse.c	(t42_parser_init): Modify functions to check
	the font header before allocating anything on the heap.

	* include/freetype/internal/ftmemory.h (FT_ARRAY_MAX,
	FT_ARRAY_CHECK): New macros.

	* src/base/ftstream.c (FT_Stream_TryRead): New function.
	* include/freetype/internal/ftstream.h: Updated.

	* src/pcf/pcfread.c (pcf_read_TOC), src/pcf/pcfutil.c
	(BitOrderInvert, TwoByteSwap, FourByteSwap): Minor fixes and
	simplifications.  Try to protect the PCF driver from doing stupid
	things with broken fonts.

	* src/lzw/ftlzw.c (FT_Stream_OpenLZW): Check the LZW header before
	doing anything else.  This avoids unnecessary heap allocations
	(400KByte of heap memory for the LZW decoder).

	* src/gzip/ftgzip.c (FT_Stream_OpenGzip): Ditto for the gzip
	decoder, although the code savings are smaller.

	* docs/CHANGES: Updated.

2005-03-10  David Turner  <<EMAIL>>

	* src/tools/glnames.py: Add comment to explain the compression
	being used for the Adobe Glyph List.

2005-03-10  Werner Lemberg  <<EMAIL>>

	* src/truetype/ttpload.c (tt_face_load_cvt, tt_face_load_fpgm):
	Fix serious typo which prevented correct TT rendering.

	* include/freetype/internal/ftmemory.h: Undo change from 2005-03-03.
	To suppress warnings it is sufficient to use `-fno-strict-aliasing'.

2005-03-10  Werner Lemberg  <<EMAIL>>

	* src/tools/glnames.py: Formatted.
	Format output to be in sync with other FreeType code.
	Import `re' and `os.path'.
	(StringTable) <__init__>: Add parameter to initialize master table
	name.
	(StringTable) <dump>: Don't pass master table name.
	(StringTable) <dump_sublist>: Emit explanatory comment.
	Simplify and make output more human readable.
	(t1_bias, glyph_list, adobe_glyph_names): Removed.  Unused.
	(main): Use `basename' for file name in header.

	* src/psnames/pstables.h: Regenerated.

2005-03-09  David Turner  <<EMAIL>>

	* src/tools/glnames.py: Rewrite the generator for the `pstables.h'
	header file which contains various constant tables related to glyph
	names.  It now uses a different, more compact storage scheme that
	saves about 20KB.  This also closes Savannah bug #12262.

	* src/psnames/pstables.h: Regenerated.

	* src/psnames/psmodule.c (ps_unicode_value): Use
	`ft_get_adobe_glyph_index', a new function defined in `pstables.h'.
	(ps_get_macintosh_name, ps_get_standard_strings): Updated.

	* src/base/ftobjs.c (FT_Set_Char_Size): Handle fractional sizes
	more carefully.  This fixes Savannah bug #12263.

2005-03-06  David Turner  <<EMAIL>>

	* src/otvalid/otvgsub.c, src/otvalid/otvgpos.c: Make static tables
	constant.

	* src/autofit/aflatin.c (af_latin_metrics_init): Fix Savannah bug
	#12212 (auto-hinter refuses to work if no Unicode charmap in font).

2005-03-05  Werner Lemberg  <<EMAIL>>

	* autogen.sh: New script for bootstrapping.

	* README.CVS: New file which documents bootstrapping.

	* builds/unix/aclocal.m4, builds/unix/config.guess,
	builds/unix/config.sub, builds/unix/configure,
	builds/unix/ltmain.sh: Removed.

2005-03-04  Werner Lemberg  <<EMAIL>>

	* src/base/ftutil.c: Include FT_INTERNAL_OBJECTS_H.

2005-03-03  Werner Lemberg  <<EMAIL>>

	Various fixes for C and C++ compiling.

	* src/autofit/*: Add copyright messages.

	* src/autofit/afhints.c (af_glyph_hints_done): Don't use
	`AF_Dimension' but `int' for loop counter.

	* src/autofit/aflatin.c (af_latin_metrics_init_widths): Don't use
	`AF_Dimension' but `int' for loop counter.
	Use proper enumeration value for `render_mode'.
	(af_latin_metrics_scale_dim): Don't shadow variables.
	(af_latin_hints_compute_segments): Use proper cast for `major_dir'
	and `segment_dir'.
	(af_latin_align_linked_edge, af_latin_hint_edges): Fix arguments of call to
	`af_latin_compute_stem_width'.
	(af_latin_hints_apply): Don't use `AF_Dimension' but `int' for loop
	counter.

	* src/base/ftdbgmem.c (ft_mem_table_get_source, FT_DumpMemory): Use
	proper cast for memory allocation.

	* src/cff/cffdrivr.c (cff_get_kerning): Use proper cast for
	initialization of `sfnt'.

	* src/sfnt/sfdriver.c: Include `ttkern.h'.

	* src/sfnt/ttkern.c (tt_face_get_kerning): Don't shadow variables.

	* src/truetype/ttgload.c: Include `ttpload.h'.

2005-03-03  David Turner  <<EMAIL>>

	* include/freetype/internal/ftmemory.h (FT_ALLOC, FT_REALLOC,
	FT_QALLOC, FT_QREALLOC) [gcc >= 3.3]: Provide macro versions which
	avoid compiler warnings.
	(FT_NEW, FT_NEW_ARRAY, FT_RENEW_ARRAY, FT_QNEW, FT_QNEW_ARRAY,
	FT_QRENEW_ARRAY, FT_ALLOC_ARRAY, FT_REALLOC_ARRAY): Updated.

	* include/freetype/internal/ftserv.h (FT_FACE_FIND_SERVICE,
	FT_FACE_FIND_GLOBAL_SERVICE, FT_FACE_LOOKUP_SERVICE) [__cplusplus]:
	Provide macro versions which avoid compiler warnings.

	* src/base/ftutil.c (ft_highpow2): New utility function.

	* include/freetype/internal/ftobjs.h: Updated.

	* src/pfr/pfrload.c (pfr_get_gindex, pfr_compare_kern_pairs,
	pfr_sort_kerning_pairs): Don't define if FT_OPTIMIZE_MEMORY is set.
	(pfr_phy_font_done): Don't handle `kern_pairs' if FT_OPTIMIZE_MEMORY
	is set.
	(pfr_phy_font_load): Don't call `pfr_sort_kerning_pairs' if
	FT_OPTIMIZE_MEMORY is set.

	* src/pfr/pfrobjs.c (pfr_slot_load): Comment out some code which
	doesn't work with broken fonts.
	(pfr_face_get_kerning) [FT_OPTIMIZE_MEMORY]: Implement.

	* src/pfr/pfrtypes.h (PFR_KernItemRec): Optimize member types.
	(PFR_NEXT_KPAIR): New macro.
	(PFR_PhyFontRec): Don't define `kern_pairs' if FT_OPTIMIZE_MEMORY is
	set.

	* src/sfnt/ttsbit0.c (tt_sbit_decoder_load_image): Introduce
	temporary variable to avoid gcc warning.
	(tt_face_load_sbit_image): Mark unused variables with FT_UNUSED.

	* src/truetype/ttpload.c (tt_face_load_loca) [FT_OPTIMIZE_MEMORY]:
	Remove redundant variable.

	* include/freetype/config/ftmodule.h: Moving the order of drivers to
	speed up font loading.  The PCF and BDF loaders are still slow and
	consume far too much memory.

2005-03-03  Werner Lemberg  <<EMAIL>>

	* devel/ftoption.h: Updated to recent changes.

2005-03-02  Werner Lemberg  <<EMAIL>>

	* src/autofit/afdummy.c, src/autofit/afdummy.h
	(af_dummy_script_class): Fix type.

	* src/autofit/aflatin.c, src/autofit/aflatin.h
	(af_latin_script_class): Fix type.

	* src/autofit/rules.mk (AUTOF_DRV_SRC): Fix typo.

2005-03-01  David Turner  <<EMAIL>>

	* src/sfnt/ttkern.c (tt_face_load_kern, tt_face_get_kerning),
	src/sfnt/ttsbit0.c (tt_face_load_sbit_strikes,
	tt_sbit_decoder_load_byte_aligned, tt_sbit_decoder_load_compound,
	tt_sbit_decoder_load_image), src/sfnt/ttload.c
	(tt_face_load_metrics): Remove compiler warnings
	-- redundant variables, missing initializations, etc.

	* src/sfnt/ttsbit.h: Handle FT_OPTIMIZE_MEMORY.

	* src/autofit/rules.mk, src/autofit/module.mk,
	src/autofit/afangles.h: New files.

	* src/autofit/afhints.c (af_axis_hints_new_segment,
	af_axis_hints_new_edge): New functions.
	(af_glyph_hints_done): Do proper deallocation.
	(af_glyph_hints_reload): Only reallocate points array.  This
	drastically reduces heap usage.

	* src/autofit/afhints.h (AF_PointRec, AF_SegmentRec): Optimize
	member types and positions.
	(AF_AxisHintsRec): Add `max_segments' and `max_edges'.
	(af_axis_hints_new_segment, af_axis_hints_new_edge): New prototypes.

	* src/autofit/aflatin.c (af_latin_metrics_scale): Don't call
	AF_SCALER_EQUAL_SCALES.
	(af_latin_hints_compute_segments): Change return type to FT_Error.
	Update all callers.
	Improve segment allocation.
	(af_latin_hints_compute_edges): Change return type to FT_Error.
	Update all callers.
	Improve edge allocation and link handling.
	(af_latin_hints_detect_features): Change return type to FT_Error.
	Update all callers.

	* src/autofit/aflatin.h: Updated.

	* src/autofit/afloader.c (af_loader_load_g)
	<FT_GLYPH_FORMAT_OUTLINE>: Assure axis->num_edges > 1.  This fixes
	a bug with certain fonts.

	* include/freetype/config/ftmodule.h: The auto-fitter is now the
	only supported auto-hinting module.

	* include/freetype/config/ftstdlib.h (FT_INT_MAX): New macro.

2005-02-28  Werner Lemberg  <<EMAIL>>

	* src/truetype/ttpload.c (tt_face_load_loca): Fix typo.

	* src/sfnt/ttkern.c: Include `ttkern.h'.
	(FT_COMPONENT): Updated.

	* include/freetype/internal/fttrace.h: Add entry for `ttkern'.

	* src/sfnt/ttsbit0.c: s/FT_Err_/SFNT_Err_/.
	Decorate constants with `U' and `L' where necessary.

	* src/sfnt/ttcmap.c (tt_cmap4_next): Remove unused variable.

2005-02-28  David Turner  <<EMAIL>>

	* src/base/ftdbgmem.c (FT_DumpMemory): Added sorting of memory
	sources according to decreasing maximum cumulative allocations.
	(ft_mem_source_compare): New auxiliary function.

	* src/sfnt/ttsbit0.c: New file, implementing a heap-optimized
	embedded bitmap loader.

	* src/sfnt/ttsbit.c: Include `ft2build.h', FT_INTERNAL_DEBUG_H,
	FT_INTERNAL_STREAM_H, FT_TRUETYPE_TAGS_H.
	Load `ttsbit0.c' if FT_OPTIMIZE_MEMORY is set, otherwise use
	file contents.
	(tt_face_load_sbit_strikes): Set up root fields to indicate the
	strikes.  This fixes Savannah bug #12107.
	Use `static' keyword for `sbit_line_metrics_field',
	`strike_start_fields', `strike_end_fields'.

	* include/freetype/internal/tttypes.h (TT_FaceRec): Define
	`sbit_table', `sbit_table_size', `sbit_num_strikes' if
	FT_OPTIMIZE_MEMORY is set.
	Don't define `num_sbit_strikes' and `sbit_strikes' if
	FT_OPTIMIZE_MEMORY is set.

	* src/cff/cffobjs.c (sbit_size_reset): Handle FT_OPTIMIZE_MEMORY.

	* src/sfnt/sfobjs.c (sfnt_load_face): Fixed bug that prevented
	loading SFNT fonts without a `kern' table.
	Properly pass root->face_flags.
	Remove code for TT_CONFIG_OPTION_EMBEDDED_BITMAPS.

	* src/sfnt/sfdriver.c (sfnt_interface)
	[TT_CONFIG_OPTION_EMBEDDED_BITMAPS]: Don't use `tt_find_sbit_image'
	and `tt_load_sbit_metrics'.

	* src/sfnt/ttcmap.c: Optimize linear charmap scanning for Format 4.
	(OPT_CMAP4): New macro.
	(TT_CMap4Rec) [OPT_CMAP4]: New structure.
	(tt_cmap4_init, tt_cmap4_set_range, tt_cmap4_next, tt_cmap4_reset)
	[OPT_CMAP4]: New functions.
	(tt_cmap4_char_next) [OPT_CMAP4]: Use `tt_cmap4_next' and
	`tt_cmap4_reset'.
	(tt_cmap4_class_rec) [OPT_CMAP4]: Use `TT_CMap4Rec' and
	`tt_cmap4_init'.

	* src/truetype/ttobjs.c (Reset_SBit_Size): Handle
	FT_OPTIMIZE_MEMORY.

	* src/autofit/afhints.h (AF_PointRec, AF_SegmentRec, AF_EdgeRec):
	Optimize member types.

	* src/autofit/afloader.c (af_loader_done): Call
	`af_glyph_hints_done'.

2005-02-27  David Turner  <<EMAIL>>

	* src/sfnt/ttkern.c (tt_face_load_kern): Fix a small bug which
	caused invalid (random) return values for the horizontal kerning.

2005-02-25  David Turner  <<EMAIL>>

	Implement several memory optimizations to drastically reduce the
	heap usage of FreeType, especially in the case of memory-mapped
	files.  The idea is to avoid loading and decoding tables in the
	heap, and instead access the raw data whenever possible (i.e., when
	it doesn't compromise performance).

	This has several benefits: For example, opening vera.ttf now uses
	just a small amount of memory (even when the FT_Library footprint is
	accounted for), until you start loading glyphs.  Even then, you save
	at least 20KB compared to the non-optimized case.  Performance of
	various operations, including open and close, has also been
	dramatically improved.

	More optimizations to come, especially for the auto-hinter.

	* include/freetype/internal/sfnt.h (TT_Face_GetKerningFunc): New
	function type.
	(SFNT_Interface): Add it.

	* include/freetype/internal/tttypes.h (TT_HdmxEntryRec, TT_HdmxRec,
	TT_Kern0_PairRec): Don't define if FT_OPTIMIZE_MEMORY is set.
	(TT_FaceRec): Define `horz_metrics', `horz_metrics_size',
	`vert_metrics', `vert_metrics_size', `hdmx_table',
	`hdmx_table_size', `hdmx_record_count', `hdmx_record_size',
	`hdmx_record_sizes', `kern_table', `kern_table_size,
	`num_kern_tables', `kern_avail_bits', `kern_order_bits' if
	FT_OPTIMIZE_MEMORY is set.
	Don't define `hdmx', `num_kern_pairs', `kern_table_index',
	`kern_pairs' if FT_OPTIMIZE_MEMORY is set.

	* src/base/ftdbgmem.c (ft_mem_table_set): Don't shadow variable.
	Fix compiler warning.

	* src/cff/cffdrivr.c (Get_Kerning): Renamed to...
	(cff_get_kerning): This.  Simplify.
	(cff_driver_class): Updated.

	* src/sfnt/Jamfile (_sources): Add `ttkern'.
	* src/sfnt/rules.mk (SFNT_DRV_SRC): Add `ttkern.c'.

	* src/sfnt/sfdriver.c (sfnt_interface): Add `tt_face_get_kerning'.

	* src/sfnt/sfnt.c: Include `ttkern.c'.

	* src/sfnt/sfobjs.c: Include `ttkern.h'.
	(sfnt_load_face): Consider the `kern' and `gasp' table as optional.
	(sfnt_done_face): Call `tt_face_done_kern'.
	Handle horizontal metrics for FT_OPTIMIZE_MEMORY.

	* src/sfnt/ttkern.c, src/sfnt/ttkern.h: New files.  Code has been
	taken from `ttload.c' and `ttload.h'.
	Provide special versions of `tt_face_load_kern',
	`tt_face_get_kerning', and `tt_face_done_kern' for
	FT_OPTIMIZE_MEMORY.

	* src/sfnt/ttload.c (tt_face_load_metrics, tt_face_load_hdmx,
	tt_face_free_hdmx): Provide version for FT_OPTIMIZE_MEMORY.
	(tt_face_load_kern, tt_kern_pair_compare, TT_KERN_INDEX): Moved to
	`ttkern.c'.

	* src/sfnt/ttload.h: Updated.

	* src/sfnt/ttsbit.c (sbit_metrics_field): Add `static' keyword.

	* src/truetype/ttdriver.c (Get_Kerning): Renamed to...
	(tt_get_kerning): This.  Simplify.
	(tt_driver_class): Updated.

	* src/truetype/ttgload.c (TT_Get_Metrics): Renamed to...
	(tt_face_get_metrics): This.  Provide version for FT_OPTIMIZE_MEMORY.
	Update all callers.
	(Get_Advance_Widths): Replaced with...
	(Get_Advance_WidthPtr): This.  Provide version for
	FT_OPTIMIZE_MEMORY.
	Update all callers.

	* src/truetype/ttgload.h: Updated.

2005-02-22  David Turner  <<EMAIL>>

	* src/base/ftdbgmem.c: Partly rewritten.  Added the ability to list
	all allocation sites in the memory debugger.  Also a new function
	FT_DumpMemory() was added.  It is only available in builds with
	FT_DEBUG_MEMORY defined, and you must declare it in your own code to
	use it, i.e., with something like:

	  extern void FT_DumpMemory( FT_Memory );

	  ...

	  FT_DumpMemory( memory );

	* include/freetype/config/ftoption.h
	(TT_CONFIG_OPTION_BYTECODE_INTERPRETER): Comment out definition --
	again.
	(FT_OPTIMIZE_MEMORY): New configuration macro to control various
	optimizations for reducing the heap footprint of memory-mapped
	TrueType files.

	* include/freetype/internal/ftmemory.h (FT_ARRAY_ZERO): New
	convenience macro.

	* include/freetype/internal/tttypes.h (TT_FaceRec)
	[FT_OPTIMIZE_MEMORY]: Use optimized types for `num_locations' and
	`glyph_locations'.

	* src/truetype/ttgload.c (load_truetype_glyph): Call
	`tt_face_get_location'.

	* src/truetype/ttobjs.c (tt_face_init)
	[FT_CONFIG_OPTION_INCREMENTAL]: Improve error handling.
	(tt_face_done): Call `tt_face_done_loca'.

	* src/truetype/ttpload.c (tt_face_get_location, tt_face_done_loca):
	New functions.  If FT_OPTIMIZE_MEMORY is set, the locations table is
	read directly from memory-mapped streams, instead of being decoded
	into the heap.
	(tt_face_load_loca) [FT_OPTIMIZE_MEMORY]: New implementation.
	(tt_face_load_cvt, tt_face_load_fpgm): Only load table if the
	bytecode interpreter is compiled in.

	* src/truetype/ttpload.h: Updated.

	* src/autohint/ahglyph.c (ah_outline_load): Improve allocation
	logic.

2005-02-20  Werner Lemberg  <<EMAIL>>

	* builds/unix/ltmain.sh: Regenerated with `libtoolize --force
	--copy' from libtool 1.5.14.
	* builds/unix/aclocal.m4: Regenerated with `aclocal -I .' from
	automake 1.9.4.

	* builds/unix/config.guess, builds/unix/config.sub: Updated from
	`config' CVS module at subversions.gnu.org.

	* builds/unix/install-sh, builds/unix/mkinstalldirs: Updated from
	`texinfo' CVS module at subversions.gnu.org.

2005-02-14  Werner Lemberg  <<EMAIL>>

	* src/cff/cffcmap.c (cff_cmap_unicode_init): Don't try to build
	a cmap for a CID-keyed font which doesn't have SIDs.

2005-02-13  Werner Lemberg  <<EMAIL>>

	* src/type1/t1load.c (read_binary_data): Return more meaningful
	value.
	(parse_encoding, parse_subrs, parse_charstrings, parse_dict): Check
	parser error value after call to T1_Skip_PS_Token (where necessary).

	* src/type1/t1parse.c (T1_Get_Private_Dict): Check parser error
	value after call to T1_Skip_PS_Token.

	* src/cid/cidparse.c (cid_parser_new): Check parser error value
	after call to cid_parser_skip_PS_token.

	* src/type42/t42parse.c (t42_parse_encoding, t42_parse_sfnts,
	t42_parse_charstrings, t42_parse_dict): Check parser error value
	after call to T1_Skip_PS_Token (where necessary).

	* src/psaux/psobjs.c (skip_string, ps_parser_skip_PS_token,
	ps_tobytes): Add error messages.

2005-02-12  Werner Lemberg  <<EMAIL>>

	* configure: Output more variables to the created Makefile so that
	it can be used for ft2demos also (if the FT2DEMOS variable is
	defined).

2005-02-10  David Turner  <<EMAIL>>

	* src/pfr/pfrgload.c (pfr_glyph_load): Fix an unbounded growing
	dynamic array when loading a glyph from a PFR font (Savannah bug
	#11921).

	* src/base/ftbitmap.c (FT_Bitmap_Convert): Small improvements to the
	conversion function (mainly stupid optimization).

	* src/base/Jamfile: Adding ftbitmap.c to the list of compiled files.

2005-02-10  Werner Lemberg  <<EMAIL>>

	* builds/unix/freetype-config.in: Add new flag `--ftversion' to
	return the FreeType version.  Suggested by George Williams
	<<EMAIL>>.

	* docs/CHANGES: Updated.

2005-02-09  Werner Lemberg  <<EMAIL>>

	* src/otvalid/otvmod.c (otv_validate): Deallocate arrays in case
	of error.  Reported by YAMANO-UCHI Hidetoshi <<EMAIL>>.

2005-02-08  Werner Lemberg  <<EMAIL>>

	* src/psaux/t1decode.c (t1_decoder_parse_charstrings)
	<op_closepath>: Accept `T1_Parse_Have_Moveto' state also which can
	happen in empty glyphs.  Reported by Ian Brown
	<<EMAIL>> (Savannah bug #11856).

2005-02-04  Werner Lemberg  <<EMAIL>>

	* src/otlayout/*: Removed.  Obsolete.

2004-12-28  Werner Lemberg  <<EMAIL>>

	* builds/unix/ltmain.sh: Regenerated with `libtoolize --force
	--copy' from libtool 1.5.10.
	* builds/unix/aclocal.m4: Regenerated with `aclocal -I .' from
	automake 1.9.4.
	* builds/unix/configure: Regenerated with autoconf 2.59b.

	* builds/unix/config.guess, builds/unix/config.sub: Updated from
	`config' CVS module at subversions.gnu.org.

	* builds/unix/install-sh: Updated from
	`texinfo' CVS module at subversions.gnu.org.

	* builds/unix/ftsystem.c (FT_Stream_Open): Add proper cast for
	ft_alloc.
	Fix compiler warning.

2004-12-27  Dirck Blaskey  <<EMAIL>>

	* src/cff/cffobjs.c (cff_face_init): Improve computation of
	FT_STYLE_BOLD_FLAG.

2004-12-27  Werner Lemberg  <<EMAIL>>

	* src/cff/cffobjs.c (cff_face_init): A CFF within an SFNT can have
	only a single font.  This is undocumented but has been verified on
	the opentype list.

2004-12-26  Werner Lemberg  <<EMAIL>>

	* Jamfile (FT2_COMPONENTS): Add `otvalid'.

2004-12-25  Werner Lemberg  <<EMAIL>>

	* src/base/ftbitmap.c (FT_Bitmap_Convert): Fix compiler warning.

2004-12-15  Werner Lemberg  <<EMAIL>>

	* vms_make.com: Add ftbitmap.obj.

2004-12-14  Werner Lemberg  <<EMAIL>>

	* src/base/ftbitmap.c, include/freetype/ftbitmap.h: New files for
	handling various bitmap formats.

	* include/freetype/config/ftheader.h (FT_BITMAP_H): New macro.

	* src/base/rules.mk (BASE_EXT_SRC): Add ftbitmap.c.

	* src/bdf/bdfdrivr.c (BDF_Glyph_Load): Don't convert bitmaps to 8bpp
	but return them as-is.

	* docs/CHANGES: Mention new bitmap API.
	* include/freetype/ftchapters.h: Updated.

2004-12-11  Robert Clark  <<EMAIL>>

	* src/base/ftobjs.c (FT_Get_Kerning): Make kerning amount
	dependent on ppem by scaling down for ppem < 25, then do normal
	rounding.  This gives slightly better results than rounding towards
	zero.

2004-12-09  Werner Lemberg  <<EMAIL>>

	* src/base/ftobjs.c (FT_Get_Kerning): Always round towards zero
	for FT_KERNING_DEFAULT.  This greatly enhances the kerning for
	small ppem values.

2004-12-08  Werner Lemberg  <<EMAIL>>

	* src/base/ftobjs.c (ft_glyphslot_clear): Reset `lsb_delta' and
	`rsb_delta'.

2004-12-05  Werner Lemberg  <<EMAIL>>

	* builds/unix/install.mk (install): Use $(OBJ_BUILD) for ftconfig.h.

2004-12-03  Antoine Leca  <<EMAIL>>

	* include/freetype/ttnameid.h: Updated to latest
	specifications from Microsoft.

2004-11-26  Jouk Jansen  <<EMAIL>>

	* vms_make.com: Include ftbbox.c.
	Fix `ccopt'.
	Handle `otvalid' module.
	Update `vmslib.dat' default values.
	Fixes to `libs.opt'.

2004-11-23  Anders Kaseorg  <<EMAIL>>

	* src/base/ftoutln.c (FT_OrientationExtremumRec,
	ft_orientation_extremum_compute): Removed.
	(FT_Outline_Get_Orientation): Rewritten, simplified.

	* src/autohint/ahglyph.c: Include FT_OUTLINE_H.
	(ah_test_extremum, ah_get_orientation): Removed.
	(ah_outline_load): Use FT_Outline_Get_Orientation.

	* src/base/ftsynth.c (ft_test_extrema, ft_get_orientation): Removed.
	(FT_GlyphSlot_Embolden): Use FT_Outline_Get_Orientation.

2004-11-23  Fernando Papa  <<EMAIL>>

	* src/truetype/ttinterp.h: Fix typo.

2004-11-22  Antoine Leca  <<EMAIL>>

	* builds/win32/detect.mk: Corrected logic that detects Windows NT to
	use the previous change even if win32 is forced.  Corrected
	detection of win32 on Win9X.

	* builds/dos/detect.mk: Added same correction as for win32 about
	COPY on Windows NT.  Detection of plain DOS 7.x.

2004-11-22  Werner Lemberg  <<EMAIL>>

	* builds/detect.mk: Undo change from 2004-11-20.
	* builds/win32/detect.mk: If the `OS' environment variable contains
	`Windows_NT', use `cmd.exe /c copy' for copying files.

2004-11-20  Werner Lemberg  <<EMAIL>>

	* builds/detect.mk (dos_setup): Use `cmd.exe' for copying
	$(CONFIG_MK) to force lowercase file name under Windows.

2004-11-19  Werner Lemberg  <<EMAIL>>

	Fix a serious bug in the TT hinter.

	* src/truetype/ttgload.c (TT_Process_Simple_Glyph): Don't shift
	points vertically before hinting.

	* docs/CHANGES: Updated.

	* src/cache/ftcglyph.c (FTC_GNode_UnselectFamily,
	FTC_GCache_Lookup): A new try to fix comparison with zero.

2004-11-16  Werner Lemberg  <<EMAIL>>

	* builds/unix/configure.ac: Add `-fno-strict-aliasing' if gcc is
	used.
	* builds/unix/configure: Regenerated.
	* builds/unix/config.guess, builds/unix/config.sub: Updated from
	`config' CVS module at subversions.gnu.org.

2004-11-16  Dr. Martin P.J. Zinser  <<EMAIL>>

	* src/cache/ftcglyph.c (FTC_GNode_UnselectFamily,
	FTC_GCache_Lookup): Fix comparison with zero.

	* docs/INSTALL.VMS: Updated.

	* vms_make.com: Updated.  All `descrip.mms' files are now created
	automatically.

	* src/*/descrip.mms: Removed.

2004-11-16  Owen Taylor  <<EMAIL>>

	* builds/unix/freetype-config.in: Suppress -L$libdir for
	/usr/lib64 as well as /usr/lib.  (Reported by Dan Winship -
	https://bugzilla.redhat.com/bugzilla/show_bug.cgi?id=139199)

2004-11-11  Werner Lemberg  <<EMAIL>>

	* src/cff/cffdrivr.c (cff_service_ps_info): Updated.
	* src/cid/cidriver.c (cid_service_ps_info): Updated.
	* src/type42/t42drivr.c (t42_ps_get_font_private): New function.
	(t42_service_ps_info): Updated.

	* src/type42/t42parse.c (t42_parse_dict): Remove compiler warning.

2004-11-11  David Bevan  <<EMAIL>>

	Add new function FT_Get_PS_Font_Private().

	* include/freetype/internal/services/svpsinfo.h
	(PS_GetFontPrivateFunc): New service function.

	* include/freetype/t1tables.h, src/base/fttype1.c
	(FT_Get_PS_Font_Private): New function.

	* src/type1/t1driver.c (t1_ps_get_font_private): New function.
	(t1_service_ps_info): Updated.

2004-10-13  Werner Lemberg  <<EMAIL>>

	* include/freetype/config/ftstdlib.h: Include `stddef.h'.
	(ft_ptrdiff_t): Define.

	* include/freetype/fttypes.h (FT_PtrDist): Use `ft_ptrdiff_t'.

	* src/cid/cidload.c (cid_parse_dict), src/type1/t1load.c
	(parse_dict): Fix compiler warning.

2004-10-11  Joshua Neal  <<EMAIL>>

	* src/sfnt/ttcmap.c (tt_face_build_cmaps): Check for pointer
	overflow.

	* src/sfnt/ttload.c (tt_face_load_hdmx): Protect against bad input.
	Don't use FT_QNEW_ARRAY but FT_NEW_ARRAY to make deallocation work
	in case of failure.

	* src/sfnt/ttsbit.c (Load_SBit_Range): Check range intervals.
	(tt_face_load_sbit_strikes): Allocate `strike_sbit_ranges' after
	frame test.

	* src/truetype/ttgload.c (TTLoad_Simple_Glyph): Add assertion for
	`flag'.

2004-10-09  Werner Lemberg  <<EMAIL>>

	* docs/CHANGES: Updated.

2004-10-09  Boris Letocha  <<EMAIL>>

	Fix handling of NPUSHW if skipped in data stream.

	* src/truetype/ttinterp.c (opcode_length): Set value for NPUSHW
	to -2.
	(SkipCode, TT_RunIns): Use opcode_length value for computation of
	bytes to be skipped.

2004-09-10  Jouk Jansen  <<EMAIL>>

	* vms_make.com: Updated.

2004-09-09  Werner Lemberg  <<EMAIL>>

	Adding OpenType validation module.  The code is based on the
	(unfinished) `otlayout' module but has been heavily modified to make
	it much more compact.

	* src/otvalid/*: New module.

	* include/freetype/ftotval.h, src/base/ftotval.c,
	include/freetype/internal/services/svotval.h: New files.

	* include/freetype/config/ftmodule.h: Add otv_module_class.
	* include/freetype/config/ftheader.h (FT_OPENTYPE_VALIDATE_H): New
	macro.
	* include/freetype/internal/ftserv.h
	(FT_SERVICE_OPENTYPE_VALIDATE_H): New macro.
	* include/freetype/internal/fttrace.h (otvmodule, otvcommon,
	otvbase, otvgdef, otvgpos, otvgsub, otvjstf): New trace components.

	* include/freetype/ftchapters.h: Updated.

	* src/base/Jamfile (Library), src/base/descrip.mms (OBJS),
	src/base/rules.mk (BASE_EXT_SRC): Updated.

	* docs/CHANGES: Updated.

2004-09-08  Werner Lemberg  <<EMAIL>>

	* src/tools/docmaker/sources.py (re_source_block_format2) <column>:
	Use lookahead assertion to not match `*/'.  This removes spurious
	insertions of `/' in the HTML output.

2004-09-07  Werner Lemberg  <<EMAIL>>

	* src/truetype/ttgxvar.c (TT_Vary_Get_Glyph_Deltas): Fix call to
	FT_NEW_ARRAY.

2004-09-04  Werner Lemberg  <<EMAIL>>

	* include/freetype/internal/ftobjs.h: Don't include
	FT_CONFIG_STANDARD_LIBRARY_H.
	(FT_Validator, FT_ValidationLevel, FT_ValidatorRec, FT_VALIDATOR,
	ft_validator_init, ft_validator_run, ft_validator_error, FT_INVALID,
	FT_INVALID_TOO_SHORT, FT_INVALID_OFFSET, FT_INVALID_FORMAT,
	FT_INVALID_GLYPH_ID, FT_INVALID_DATA): Move to...

	* include/freetype/internal/ftvalid.h: New file.
	Make FT_INVALID return module-specific error codes.

	* include/freetype/internal/internal.h (FT_INTERNAL_VALIDATE_H): New
	macro.

	* include/freetype/fterrors.h: Undefine FT_ERR_PREFIX only if
	FT_KEEP_ERR_PREFIX isn't defined.

	* src/base/ftobjs.c: Include FT_INTERNAL_VALIDATE_H.

	* src/sfnt/ttcmap.h: Don't include FT_INTERNAL_OBJECTS_H but
	FT_INTERNAL_VALIDATE_H.

	* src/sfnt/ttcmap.c: Don't include FT_INTERNAL_OBJECTS_H but
	FT_INTERNAL_VALIDATE_H.
	Include sferrors.h before FT_INTERNAL_VALIDATE_H.
	s/FT_Err_Ok/SFNT_Err_Ok/.

	* src/sfnt/sferrors.h: Define FT_KEEP_ERR_PREFIX.

	* src/type1/t1afm.c: Include t1errors.h.

2004-09-03  Werner Lemberg  <<EMAIL>>

	* src/base/ftdebug.c (ft_debug_init): Highest debug level is 7,
	not 6.
	* docs/DEBUG: Updated.

2004-08-30  Werner Lemberg  <<EMAIL>>

	* include/freetype/tttags.h (TTAG_BASE, TTAG_GDEF, TTAG_GPOS,
	TTAG_JSTF): New tags.

	* include/freetype/fttypes.h (FT_Bytes, FT_Tag): New typedefs.
	(FT_Int): Add `signed'.

2004-08-29  Werner Lemberg  <<EMAIL>>

	* src/otlayout/otlgpos.c (otl_gpos_subtable_validate): Add argument
	to pass number of lookups.
	Update all callers.
	Don't call otl_lookup_list_validate but otl_lookup_validate.
	(otl_gpos_validate): Call otl_lookup_list_validate instead of
	otl_gpos_subtable_validate.

	* src/otlayout/otlgpos.h: Updated.

	* src/otlayout/otljstf.c (otl_jstf_max_validate): Add argument to
	pass number of lookups.
	Update all callers.


	* src/cff/cffparse.c (cff_parse_real): s/exp/exponent/ to avoid
	compiler warning.


	* src/sfnt/ttcmap0.c, src/sfnt/ttcmap0.h: Renamed to...
	* src/sfnt/ttcmap.c, src/sfnt/ttcmap.h: This.
	* src/sfnt/Jamfile, src/sfnt/rules.mk, src/sfnt/sfdriver.c,
	src/sfnt/sfnt.c, src/sfnt/sfobjs.c: Updated.


	* builds/compiler/gcc-dev.mk (CFLAGS): Don't add `-Wnested-externs'
	if compiler is g++ (v3.3.3 emits a warning otherwise).

2004-08-28  Werner Lemberg  <<EMAIL>>

	* src/otlayout/otlgpos.c (otl_value_length): Return number of bytes,
	not number of 16bit entities.
	(otl_gpos_lookup2_validate): Check class definition tables for
	format 2.
	Fix loop for format 2.
	(otl_liga_mark2_validate): Fix offset for otl_anchor_validate.

2004-08-27  Werner Lemberg  <<EMAIL>>

	* src/base/ftmac.c: Don't include truetype/ttobjs.h.
	Don't include type1/t1objs.h.
	(FT_New_Face_From_FSSpec) [!__MWERKS__]: Remove compiler warnings.

2004-08-27  Mathieu Malaterre  <<EMAIL>>

	* src/base/ftmac.c: Handle OS_INLINE for xlc compiler also.

2004-08-27  Werner Lemberg  <<EMAIL>>

	* src/otlayout/otlayout.h: Add copyright.
	(OTL_INVALID_OFFSET): Removed.

	* src/otlayout/otlgdef.h: Include otlayout.h.
	Comment out inclusion of otltable.h.

	* src/otlayout/otlgpos.c (otl_gpos_lookup4_validate): Fix call
	to otl_base_array_validate.
	(otl_liga_mark2_validate): Fix `for' loop.

	* src/otlayout/otlgsub.c (otl_ligature_validate): Check `glyph_id',
	not components array.

	* src/otlcommn.c (otl_lookup_get_count, otl_feature_get_count):
	Comment out.
	(otl_lookup_list_get_count, otl_feature_list_get_count): Activate.
	(otl_feature_list_validate, otl_gsubgpos_get_lookup_count):
	s/otl_lookup_get_count/otl_lookup_list_get_count/.
	(otl_script_list_validate):
	s/otl_feature_get_count/otl_feature_list_get_count/.
	(otl_script_validate): Call otl_lang_validate for default language.

	* src/otlayout/otlcommn.h: Updated.

2004-08-16  Werner Lemberg  <<EMAIL>>

	* src/otlayout/otlgpos.c (otl_gpos_lookup1_validate,
	otl_gpos_lookup2_validate, otl_gpos_lookup3_validate,
	otl_gpos_lookup4_validate, otl_gpos_lookup5_validate,
	otl_gpos_lookup6_validate, otl_gpos_lookup9_validate,
	otl_gpos_validate): Update
	function arguments.
	(otl_gpos_lookup7_validate, otl_gpos_lookup8_validate): Update
	function arguments.
	Handle NULL offsets correctly.
	Check sequence and lookup indices for format 3.
	(otl_pos_rule_validate, otl_chain_pos_rule_validate): Add argument
	to pass lookup count.
	Check sequence and glyph indices.
	(otl_gpos_subtable_validate): Update function arguments.
	Update callers.

	* src/otlayout/otlgpos.h: Updated.

	* src/otlayout/otlgsub.c (otl_gsub_lookup1_validate,
	otl_gsub_lookup3_validate, otl_gsub_lookup8_validate): Update
	function arguments.
	Add glyph index checks.
	(otl_sequence_validate, otl_alternate_set_validate,
	otl_ligature_validate): Add argument to pass glyph count.
	Update callers.
	Add glyph index check.
	(otl_gsub_lookup2_validate, otl_gsub_lookup4_validate): Update
	function arguments.
	(otl_ligature_set_validate): Add argument to pass glyph count.
	Update caller.
	(otl_sub_class_rule_validate,
	otl_sub_class_rule_set_validate): Removed.
	(otl_sub_rule_validate, otl_chain_sub_rule_validate): Add argument
	to pass lookup count.
	Update callers.
	Add lookup index check.
	(otl_sub_rule_set_validate, otl_chain_sub_rule_set_validate): Add
	argument to pass lookup count.
	Update callers.
	(otl_gsub_lookup5_validate): Update function arguments.
	Handle NULL offsets correctly.
	Don't call otl_sub_class_rule_set_validate but
	otl_sub_rule_set_validate.
	Check sequence and lookup indices for format 3.
	(otl_gsub_lookup6_validate): Update function arguments.
	Handle NULL offsets correctly.
	Check sequence and lookup indices for format 3.
	(otl_gsub_lookup7_validate, otl_gsub_validate): Update function
	arguments.

	* src/otlayout/otlgsub.h: Updated.

	* src/otlayout/otlbase.c (otl_base_validate): Handle NULL offsets
	correctly.

	* src/otlayout/otlcommn.c (otl_class_definition_validate): Fix
	compiler warning.
	(otl_coverage_get_first, otl_coverage_get_last): New functions.
	(otl_lookup_validate): Add arguments to pass lookup and glyph
	counts.
	Update callers.
	(otl_lookup_list_validate): Add argument to pass glyph count.
	Update callers.

	* src/otlayout/otlcommn.h: Updated.

	* src/otlayout/otljstf.c (otl_jstf_extender_validate,
	otl_jstf_max_validate, otl_jstf_script_validate,
	otl_jstf_priority_validate, otl_jstf_lang_validate): Add parameter
	to validate glyph indices.
	Update callers.
	(otl_jstf_validate): Add parameter which specifies number of glyphs
	in font.

	* src/otlayout/otljstf.h: Updated.

2004-08-15  Werner Lemberg  <<EMAIL>>

	* src/otlayout/otlgpos.c (otl_liga_mark2_validate): Add parameter
	to handle possible NULL values properly.
	Update all callers.

2004-08-15  Werner Lemberg  <<EMAIL>>

	* src/otlayout/gpos.c: Rename counting variables to be more
	meaningful.
	Add copyright.
	(otl_liga_attach_validate): Renamed to...
	(otl_liga_mark2_validate): This.
	Update all callers.
	(otl_mark2_array_validate): Removed.
	(otl_gpos_lookup6_validate): Call otl_liga_mark2_validate, not
	otl_mark2_array_validate.
	(otl_pos_class_set_validate, otl_pos_class_rule_validate): Removed.
	(otl_gpos_lookup7_validate): Complete code for format 2.
	(otl_chain_pos_class_rule_validate,
	otl_chain_pos_class_set_validate): Removed.
	(otl_gpos_lookup8_validate): Don't call
	otl_chain_pos_class_set_validate but
	otl_chain_pos_rule_set_validate.
	Simplify some code.

	* src/otlayout/otlgpos.h: Add copyright.

2004-08-14  Werner Lemberg  <<EMAIL>>

	* src/otlayout/otljstf.c (otl_jstf_gsub_mods_validate): Removed.
	(otl_jstf_gpos_mods_validate): Renamed to...
	(otl_jstf_gsubgpos_mods_validate): This.
	Test whether lookup_count is zero.
	(otl_jstf_priority_validate): Use otl_jstf_gsubgpos_mods_validate.
	(otl_jstf_validate): Initialize gsub_lookup_count and
	gpos_lookup_count if gsub or gpos is zero.

	* src/otlayout/otlgsub.c: Rename counting variables to be more
	meaningful.
	Add copyright.
	(otl_gsub_lookup1_validate): Simplify code.
	(otl_gsub_lookup2_validate, otl_gsub_lookup3_validate,
	otl_gsub_lookup4_validate, otl_gsub_lookup7_validate): Remove unused
	variables.
	(otl_gsub_lookup5_validate): Remove unused variable.
	Fix call to otl_sub_rule_set_validate and
	otl_sub_class_rule_set_validate.
	(otl_chain_sub_class_rule_validate,
	otl_chain_sub_class_set_validate): Removed.
	(otl_gsub_lookup6_validate): Remove unused variable.
	Fix call to otl_chain_sub_rule_set_validate.
	(otl_gsub_lookup7_validate): Handle lookup type 8 also.
	(otl_gsub_lookup8_validate: New function.
	(otl_gsub_lookup1_apply, otl_gsub_lookup2_apply,
	otl_gsub_lookup3_apply): Commented out.
	(otl_gsub_validate_funcs): Add otl_gsub_lookup7_validate and
	otl_gsub_lookup8_validate.
	(otl_gsub_validate): Updated.

	* src/otlayout/otlgsub.h: Add copyright.

	* src/otlayout/otlcommn.c, src/otlayout/otlcommn.h
	(otl_coverage_get_index): Comment out.

2004-08-13  Werner Lemberg  <<EMAIL>>

	* src/otlayout/otlcommn.c (otl_gsubgpos_get_lookup_count): New
	function.
	* src/otlayout/otlcommn.h: Updated.

	* src/otlayout/otlbase.c: Rename counting variables to be more
	meaningful.
	Add copyright message.
	* src/otlayout/otlbase.h: Add copyright message.

	* src/otlayout/otlgdef.c: Rename counting variables to be more
	meaningful.
	Add copyright message.
	Use OTL_CHECK everywhere.
	(otl_caret_value_validate): Remove unused variable.
	(otl_gdef_validate): All tables are optional.
	* src/otlayout/otlgdef.h: Add copyright message.

	* src/otlayout/otljstf.c: Rename counting variables to be more
	meaningful.
	Add copyright message.
	(otl_jstf_gsub_mods_validate, otl_jstf_gpos_mods_validate): Add
	parameter to pass lookup count.
	Update all callers.
	Check lookup array.
	(otl_jstf_max_validate):
	s/otl_gpos_subtable_check/otl_gpos_subtable_validate/.
	(otl_jstf_priority_validate, otl_jstf_lang_validate,
	otl_jstf_script_validate): Add two parameters to pass lookup counts.
	Update all callers.
	(otl_jstf_validate): Add two parameters to pass GPOS and GSUB
	table offsets; use otl_gsubgpos_get_lookup_count to convert extract
	lookup counts.
	Fix typo.
	* src/otlayout/otljstf.h: Updated.
	Add copyright message.

	* src/otlayout/otlgpos.c (otl_gpos_subtable_validate): New function.
	(otl_gpos_validate): Use it.
	* src/otlayout/otlgpos.h: Updated.

2004-08-13  Werner Lemberg  <<EMAIL>>

	* src/otlayout/otcommn.c: Use OTL_CHECK everywhere.
	(otl_coverage_validate): Initialize `p',
	s/count/num_glyphs/.
	s/start_cover/start_coverage/.
	(otl_coverage_get_index): Return OTL_Long, not OTL_Int.
	Remove unused variables.
	(otl_class_definition_validate): s/count/num_glyphs/.
	Remove unused variables.
	(otl_class_definition_get_value, otl_device_table_get_start,
	otl_device_table_get_end, otl_device_table_get_delta,
	otl_lookup_get_table, otl_lookup_list_get_count,
	otl_lookup_list_get_lookup, otl_lookup_list_get_table,
	otl_feature_get_lookups, otl_feature_list_get_count,
	otl_feature_list_get_feature, otl_lang_get_count,
	otl_lang_get_req_feature, otl_lang_get_features): Commented out
	temporarily until we really need it.
	(otl_lookup_validate): Removed.
	(otl_lookup_table_validate): Renamed to ...
	(otl_lookup_validate): This.  Update callers.
	(otl_lookup_list_validate): Remove already commented out definition
	and move the other definition up.
	(otl_feature_validate): Add parameter to pass number of lookups.
	Update callers.
	Check lookup indices.
	(otl_feature_list_validate): Add parameter to pass lookup table.
	Update callers.
	(otl_lang_validate): Add parameter to pass number of features.
	Update callers.
	Handle req_feature and check feature indices.
	(otl_script_validate): Add parameter to pass number of features.
	Update callers.
	(otl_script_list_validate): Add parameter to pass feature table.
	Update callers.

	* src/otlayout/otcommn.h: s/LOCALDEF/LOCAL/.
	Comment out the same functions as in otcommn.c.
	(otl_script_list_get_script): Removed.

	* src/otlayout/otlgsub.c (otl_gsub_lookup1_apply): Change `index' to
	type OTL_Long.
	(otl_gsub_lookup2_apply, otl_gsub_lookup3_apply): Change `index' to
	type OTL_Long.
	Fix test.
	(otl_gsub_validate): Fix order of validation.

	* src/otlayout/otlgpos.c (otl_gpos_validate): Fix order of
	validation.

2004-08-12  Werner Lemberg  <<EMAIL>>

	Make otlayout module compile (without actually working).

	* src/otlayout/*: s/OTL_Valid/OTL_Validator/.
	s/NULL/0/.

	* src/otlayout/otlayout.h: Fix various typos.
	(OTL_Bool): New typedef.
	(OTL_Int, OTL_Long, OTL_Int16, OTL_Int32): Use `signed' keyword.
	(OTL_Err_InvalidArgument): Removed.
	(OTL_Err_InvalidData, OTL_Err_InvalidSize): New enum values.
	(OTL_MAKE_TAG): Add missing parenthesis.
	(OTL_INVALID_DATA): Use OTL_Err_InvalidData.
	(OTL_INVALID_TOO_SHORT): Use OTL_Err_InvalidSize.
	(OTL_INVALID_FORMAT, OTL_INVALID_OFFSET): New macros.

	* src/otlayout/otlgpos.c: s/FT_/OTL_/.
	s/OTL_Short/OTL_Int16/.
	(otl_gpos_pairset_validate): Add return type.
	(otl_base_array_validate): Fix call to otl_anchor_validate.
	(otl_liga_array_validate): Fix call to otl_liga_attach_validate.
	(otl_gpos_lookup5_validate): Fix typos.
	(otl_gpos_lookup6_validate): Fix call to otl_mark2_array_validate.
	(otl_gpos_lookup7_validate): Comment out unfinished code.
	Fix typos.

	* src/otlayout/otlgsub.c: Add forward declaration for
	otl_gsub_validate_funcs.
	(otl_gsub_lookup1_apply, otl_gsub_lookup2_apply,
	otl_gsub_lookup3_apply): Fix call to otl_parser_check_property.
	s/otl_coverage_lookup/otl_coverage_get_index/.
	(otl_ligature_validate): Add missing variable declaration.
	(otl_sub_rule_validate): Fix typo.
	(otl_sub_class_rule_validate): Add missing variable declaration.
	Fix typo.
	(otl_gsub_lookup5_validate): Fix typo.
	(otl_gsub_lookup6_validate): Fix call to
	otl_chain_sub_class_set_validate.
	(otl_gsub_validate_funcs): Don't use `const'.

	* src/otlayout/otlcommn.c (otl_class_definition_get_value,
	otl_device_table_validate, otl_device_table_get_delta,
	otl_lookup_validate, otl_script_validate): Add missing
	variable declarations.
	(otl_lookup_list_validate): Comment out first definition.
	(otl_lookup_list_foreach, otl_feature_list_foreach): Comment out.
	(otl_feature_list_validate):
	s/otl_feature_table_validate/otl_feature_validate/.
	(otl_script_list_validate):
	s/otl_script_table_validate/otl_script_validate/.

	* src/otlayout/otlcommn.h: Comment out first declaration.
	(otl_lookup_list_foreach, otl_feature_list_foreach): Comment out.

	* src/otlayout/otlbase.c (otl_base_coord_validate): Fix call to
	otl_device_table_validate.
	(otl_base_script_validate): Add missing variable declarations.
	(otl_base_script_list_validate): Fix call to
	otl_base_script_validate.
	(otl_axis_table_validate): Fix calls to otl_base_tag_list_validate
	and otl_base_script_list_validate.
	(otl_base_validate): Fix calls to otl_axis_table_validate.

	* src/otlayout/otlgdef.c (otl_attach_list_validate): Fix call to
	otl_attach_point_validate.
	(otl_caret_value_validate): Add missing variable declaration.
	Fix call to otl_device_table_validate.
	(otl_ligature_glyph_validate): Fix call to otl_caret_value_validate.
	(otl_ligature_caret_list_validate): Fix call to
	otl_ligature_glyph_validate.
	(otl_gdef_validate): Fix calls to otl_class_definition_validate,
	otl_attach_list_validate, otl_ligature_caret_list_validate, and
	otl_class_definition_validate.

	* src/otlayout/otltable.h (otl_table_validate, otl_table_init,
	otl_table_set_script): Comment out.

	* src/otlayout/otlparse.h (OTL_ParserRec):
	s/OTL_Alternate/OTL_GSUB_Alternate/.
	(OTL_ParseError): Add OTL_Err_Parser_Memory and
	OTL_Err_Parser_Internal.
	(otl_parser_error): Fix typo.
	(otl_parser_check_property): Remove third argument.

	* src/otlayout/otlparse.c (otl_string_ensure):
	s/OTL_Parse_Err_Memory/OTL_Err_Parser_Memory/.
	(OTL_STRING_ENSURE, otl_parser_error, otl_parser_get_index,
	otl_parser_replace_1, otl_parser_replace_n): Fix typos.
	(OTL_PARSER_UNCOVERED): Removed.
	(otl_parser_check_property): Remove third argument.

	* src/otlayout/otljstf.c (otl_jstf_priority_validate): Add missing
	variable declaration.

	* src/otlayout/otlutils.h (OTL_MEM_REALLOC): Fix typo.

2004-08-11  Danny  <<EMAIL>>

	* src/base/ftstream.c (FT_Stream_Close): Don't reset stream->close
	to NULL.  This allows custom close functions to delete the FT_STREAM
	object.

2004-08-11  Werner Lemberg  <<EMAIL>>

	Add API to get information about SFNT tables.

	* include/freetype/internal/services/svsfnt.h
	(FT_SFNT_Table_Info_Func): New typedef.
	(SFNT_Table): Add it.

	* src/base/ftobjs (FT_Sfnt_Table_Info): New function.

	* include/freetype/tttables.h: Updated.

	* src/sfnt/sfdriver.c (sfnt_table_info): New function.
	(sfnt_service_sfnt_table): Add it.

	* docs/CHANGES: Updated.


	* include/freetype/freetype.h (FREETYPE_PATCH): Set to 10.

	* builds/unix/configure.ac (version_info): Set to 9:8:3.
	* builds/unix/configure: Updated.

	* builds/win32/visualc/index.html,
	builds/win32/visualc/freetype.dsp,
	builds/win32/visualc/freetype.vcproj: s/219/2110/, s/2.1.9/2.1.10/.

	* builds/freetype.mk (refdoc), README, Jamfile (RefDoc):
	s/2.1.9/2.1.10/.

	* docs/CHANGES, docs/VERSION.DLL: Updated.

2004-08-11  Detlef Würkner  <<EMAIL>>

	* src/base/ftrfork.c (FT_Raccess_Guess)
	[!FT_CONFIG_OPTION_GUESSING_EMBEDDED_RFORK]: Remove compiler
	warnings.

2004-08-06  Adam Piotrowski  <<EMAIL>>

	* src/pfr/pfrload.c (pfr_sort_kerning_pairs): Single-byte
	adjustments are unsigned, not signed.

2004-08-05  David Turner  <<EMAIL>>

	`Activate' gray-scale specifying hinting within the TrueType
	bytecode interpreter.  This is an experimental feature which
	should probably be made optional.

	* src/truetype/ttgload.c (TT_Process_Simple_Glyph,
	load_truetype_glyph): Move the code to set the pedantic_hinting flag
	to...
	(TT_Load_Glyph): Here.
	Set `grayscale' flag except for `FT_LOAD_TARGET_MONO'.

	* src/truetype/ttinterp.c (Ins_GETINFO): Return MS rasterizer
	version 1.7.
	Return rotation and stretching info only if glyph is rotated or
	stretched, respectively.
	Handle grayscale info.

	* src/truetype/ttinterp.h (TT_ExecContextRec): Add `grayscale'
	member.

2004-08-02  George Williams  <<EMAIL>>

	* src/base/ftobjs.c (FT_Attach_File): Initialize `open.stream'.

2004-08-01  Werner Lemberg  <<EMAIL>>

	* docs/CHANGES: Updated.

2004-08-01  George Williams  <<EMAIL>>

	FreeType now can read kerning values from PFM files.

	* src/type1/t1afm.c (T1_Done_AFM): Renamed to...
	(T1_Done_Metrics): This.
	Update all callers.
	(T1_Read_AFM): Make it static.
	Don't enter and leave a frame.
	(LITTLE_ENDIAN_USHORT, LITTLE_ENDIAN_UINT): New macros.
	(T1_Read_PFM): New function.
	(T1_Read_Metrics): New higher-level function to be used instead of
	T1_Read_AFM.
	Update all callers.

2004-07-31  Werner Lemberg  <<EMAIL>>

	* src/pcf/pcfread (pcf_load_font), src/bdf/bdfdrivr.c
	(BDF_Face_Init), src/truetype/ttgxvar (TT_Get_MM_Var,
	tt_face_vary_cvt): Fix compiler warnings.

2004-07-26  Søren Sandmann  <<EMAIL>>

	* src/pcf/pcfread.c (pcf_interpret_style): Always allocate memory for
	face->style_name.
	* src/pcf/pcfdrivr.c (PCF_Face_Done): Free `style_name'.

2004-07-26  Darren J Longhorn  <<EMAIL>>

	* include/freetype/config/ftconfig.h (FT_SIZEOF_LONG): Recognize
	five-byte `long' (which is avoided then).

2004-07-25  Detlef Würkner  <<EMAIL>>

	* src/pcf/pcfdrivr.c (PCF_Set_Pixel_Size): Compare heights, not
	ppem values.
	(PCF_Set_Point_Size): Don't call PCF_Set_Pixel_Size but provide own
	code to compare ppem values.
	* src/bdf/bdfdrivr.c (BDF_Set_Pixel_Size): Compare heights, not
	ppem values.
	(BDF_Set_Point_Size): Don't call BDF_Set_Pixel_Size but provide own
	code to compare ppem values.

2004-07-25  Kornfeld Eliyahu Peter  <<EMAIL>>

	* src/sfnt/sfobjs.c (sfnt_load_face): Handle
	TT_NAME_ID_PREFERRED_FAMILY and TT_NAME_ID_PREFERRED_SUBFAMILY.

2004-07-24  Derek B. Noonburg  <<EMAIL>>

	* src/cff/cffload.c (cff_font_load): Always create inverse mapping.
	Even if the charstring count is the same as the CID count, it is
	still possible that the font uses a different CID -> GID mapping.

2004-07-23  Werner Lemberg  <<EMAIL>>

	* src/truetype/ttobjs.c (tt_face_init): Accept 0x00020000 format tag
	found in some Arphic fonts made for Chinese version of Windows 3.1.

2004-07-17  David Turner  <<EMAIL>>

	Fixed a dangling pointer bug in the cache code that happened in very
	rare cases, i.e., when a new family object was destroyed by an
	out-of-memory condition during a glyph node initialization.  The
	function FTC_Cache_Lookup would flush the cache and restart the
	lookup with a bad pointer.

	* include/freetype/cache/ftcglyph.h (FTC_FAMILY_TREE): New macro.
	(FTC_GCACHE_LOOKUP_CMP): Use it.
	Handle reference count in `num_nodes' correctly.

	* src/cache/ftcglyph.c (FTC_GNode_UnselectFamily): Use
	FTC_FAMILY_FREE.
	(FTC_GCache_Lookup): Handle reference count in `num_nodes' correctly.

	* src/cache/ftcmanag.c (FTC_Manager_FlushN): Fixed a cache flushing
	bug.

	* src/truetype/ttinterp.c (Normalize): Fixed a bug that caused
	long and unnecessary delays while normalizing huge vectors.

2004-07-15  Werner Lemberg  <<EMAIL>>

	* docs/CHANGES: Updated.

	* src/base/ftstroke.c (FT_Stroker_ParseOutline): Fix compiler
	warning.

2004-07-15  David Turner  <<EMAIL>>

	* src/base/ftstroke.c (FT_Stroker_ParseOutline): Single points
	are not stroked, preventing a bug with pala.ttf and other
	fonts.

	* include/freetype/ftstroke.h: Updating documentation comments.

2004-07-13  Werner Lemberg  <<EMAIL>>

	* src/base/ftstroke.c (ft_stroke_border_reverse): Removed.  Unused.

2004-07-12  David Turner  <<EMAIL>>

	* src/base/ftstroke.c (ft_stroke_border_close): Add second parameter
	to indicate reversion of points.
	Update all callers.
	(ft_stroke_border_reverse): Fix initialization of `point1' and
	`tag1'.

	* src/cache/ftcsbits.c (ftc_snode_load): Fixing advance computation
	for transformed glyphs.

2004-07-11  David Turner  <<EMAIL>>

	Fix bugs that prevented the stroker to correctly generate stroked
	paths from closed paths, i.e., nearly all glyphs in vectorial fonts.

	The code is still _very_ buggy though; treat with special care.

	* src/base/ftstroke.c (FT_STROKE_TAG_BEGIN_END): New macro.
	(ft_stroke_border_reverse): New function.
	(ft_stroker_inside): Remove local variable `sigma'; use different
	threshold.
	(ft_stroker_add_reverse_left): Switch begin/end tags if necessary.
	(FT_Stroker_EndSubPath): Call ft_stroker_inside and
	ft_stroke_border_reverse.

2004-06-26  Peter Kovar  <<EMAIL>>

	* src/truetype/ttgload.c (load_truetype_glyph): Fix typo.

2004-06-25  Werner Lemberg  <<EMAIL>>

	* src/type1/t1afm.c (afm_atoindex): Fix boundary test.  Reported
	by Dirck Blaskey.

2004-06-24  David Turner  <<EMAIL>>


	* Version 2.1.9 released.
	=========================


	* src/truetype/ttgload.c, src/truetype/ttxgvar.c: Removing
	compiler warnings.

2004-06-23  Werner Lemberg  <<EMAIL>>

	* include/freetype/internal/ftmemory.h [FT_DEBUG_MEMORY]: Declare
	FT_QAlloc_Debug and FT_QRealloc_Debug.

	* src/base/ftutil.c (FT_QAlloc): Fix error and debug messages.
	(FT_QRealloc): Call FT_QAlloc if original pointer is NULL.
	Fix error message.

2004-06-23  David Turner  <<EMAIL>>

	* include/freetype/internal/ftmemory.h, src/base/ftutil.c
	(FT_QAlloc, FT_QRealloc), src/base/ftdbgmem.c (FT_QAlloc_Debug,
	FT_QRealloc_Debug): New functions that perform allocation without
	zero-ing out the corresponding blocks.

	* include/freetype/internal/ftmemory.h (FT_MEM_QALLOC,
	FT_MEM_QREALLOC, FT_MEM_QNEW, FT_MEM_QNEW_ARRAY,
	FT_MEM_QRENEW_ARRAY, FT_QALLOC, FT_QREALLOC, FT_QNEW, FT_QNEW_ARRAY,
	FT_QRENEW_ARRAY): New macros.

	* src/base/ftstream.c (FT_Stream_EnterFrame): Use FT_QALLOC.
	* src/gzip/ftgzip.c (FT_Stream_OpenGzip):  Use FT_QNEW_ARRAY.
	* src/sfnt/sfobjs.c (tt_face_get_name): Use FT_QNEW_ARRAY.

	* src/sfnt/ttload.c (tt_face_load_directory, tt_face_load_metrics,
	tt_face_load_gasp): Use FT_QNEW_ARRAY.
	(tt_face_load_kern): Use FT_QNEW_ARRAY.
	Small optimization in the kerning table verifier; this speeds up
	TrueType face opening by about 7%.
	(tt_face_load_hdmx): Use FT_QNEW_ARRAY and FT_QALLOC.

	* include/freetype/config/ftmodule.h: Changed the order of modules,
	putting TrueType and Type 1 first.  This dramatically improves the
	performance of face open/close operations.  For example, putting the
	TrueType driver first in the list results in a 5x speedup when
	opening `Vera.ttf'.

	The very problem is that both the PCF and BDF drivers do a lot more
	than necessary to detect that they cannot handle a font file.

2004-06-22  Werner Lemberg  <<EMAIL>>

	* src/pcf/pcfread.c (pcf_read_TOC, pcf_get_properties,
	pcf_get_metrics, pcf_get_bitmaps, pcf_get_encodings): Improve
	debugging messages.

	* src/pcf/pcfdrivr.c (FT_COMPONENT): Move up.
	(PCF_Face_Init): Simplify code.

	* src/bdf/bdfdrivr.h (BDF_FaceRec): New element `default_glyph'.

	* src/bdf/bdflib.c (_bdf_add_property, _bdf_parse_start),
	src/bdf/bdf.h (bdf_font_t): s/default_glyph/default_char/.

	* src/bdf/bdfdrivr.c (BDF_Face_Init): Fix number of glyphs.
	Set `default_glyph'.
	(BDF_Glyph_Load): Use `default_glyph' for undefined glyph.

	* docs/CHANGES: Updated.

2004-06-21  Werner Lemberg  <<EMAIL>>

	* docs/CHANGES: Updated.

2004-06-21  David Turner  <<EMAIL>>

	* src/truetype/ttgload.c (TT_Process_Simple_Glyph,
	load_truetype_glyph): Don't access (unrounded)
	`TT_Size.root.metrics' but (rounded) `TT_Size.metrics'.  This fixes
	a scaling bug that caused incorrect rendering when the bytecode
	interpreter was enabled.

2004-06-14  Huw D M Davies  <<EMAIL>>

	* src/winfonts/winfnt.c (FNT_Face_Init): Set x_ppem and y_ppem
	based on pixel_width and pixel_height.
	(FNT_Size_Set_Pixels): Updated.

2004-06-14  Werner Lemberg  <<EMAIL>>

	* src/lzw/zopen.c: Comment out inclusion of signal.h and unistd.h.
	Reported by Hyvärinen Jyrki Juhani.

2004-06-11  Werner Lemberg  <<EMAIL>>

	* docs/CHANGES: Updated.

2004-06-10  David Turner  <<EMAIL>>

	* src/base/ftobject.c, src/base/fthash.c, src/base/ftexcept.c,
	src/base/ftsysio.c, src/base/ftsysmem.c, src/base/ftlist.c: Removed.
	Obsolete.

	* src/raster/ftraster.c (Alignment, PAlignment): New union to fix
	problems with 64bit systems.
	(AlignProfileSize): Use it.

2004-06-08  David Turner  <<EMAIL>>

	* include/freetype/freetype.h (FT_Glyph_Metrics): Move `lsb_delta'
	and `rsb_delta' elements to...
	(FT_GlyphSlotRec): Here to retain binary compatibility with older
	FreeType versions.
	Update all users.

	* src/sfnt/sfobjs.c (tt_face_get_name): Remove compiler warning.

	* src/winfonts/winfnt.c (FNT_Load_Glyph): Add missing initialization
	of slot->metrics.width and slot->metrics.height when loading a
	Windows FNT glyph.  Thanks to Huw Davies.

	* include/freetype/cache/ftcmru.h (FTC_MruNode_CompareFunc): Change
	return type to FT_Bool.

	* src/cache/ftcbasic.c (ftc_basic_family_compare): Change return
	type to FT_Bool.

	* src/cache/ftccache.c (FTC_Cache_Init, ftc_cache_init): Make
	the former call the latter, not vice versa.
	(FTC_Cache_Done, ftc_cache_done): Ditto.

	* src/cache/ftcglyph.c (FTC_GNode_Compare, ftc_gnode_compare): Make
	the former call the latter, not vice versa.
	(FTC_GCache_Init, ftc_gcache_init): Ditto.
	(FTC_GCache_Done, ftc_gcache_done): Ditto.

	* src/cache/ftcimage.c (FTC_INode_Free, ftc_inode_free): Make the
	former call the latter, not vice versa.
	(FTC_INode_Weight, ftc_inode_weight): Ditto.

	* src/cache/ftcmanag.c (ftc_size_node_compare,
	ftc_size_node_compare_faceid, ftc_face_node_compare): Change return
	type to FT_Bool.

	* src/cache/ftcsbits.c (FTC_SNode_Free, ftc_snode_free): Make the
	former call the latter, not vice versa.
	(FTC_SNode_Weight, ftc_snode_weight): Ditto.
	(FTC_SNode_Compare, ftc_snode_compare): Ditto.

	* src/cache/ftcsbits.c: Fix some bugs and inefficiencies in the cache
	sub-system.

2004-06-05  Werner Lemberg  <<EMAIL>>

	* src/autofit/afloader.c (af_loader_load_g): Set `lsb_delta' and
	`rsb_delta' in slot->metrics and tune side bearings slightly.

2004-06-04  Werner Lemberg  <<EMAIL>>

	* docs/CHANGES: Updated.

2004-06-04  David Chester  <<EMAIL>>

	Improve inter-letter spacing for autohinted glyphs.

	* include/freetype/freetype.h (FT_Glyph_Metrics): Add elements
	`lsb_delta' and `rsb_delta'.

	* src/autohint/ahhint.c (ah_hinter_load): Set `lsb_delta' and
	`rsb_delta' in slot->metrics and tune side bearings slightly.

2004-06-04  David Turner  <<EMAIL>>

	* src/autofit/*: Important fixes to the auto-fitter.  The output
	now seems to be 100% equivalent to the auto-hinter, while being
	about 2% faster (which proves that script-specific algorithm
	selection isn't a performance problem).

	To test it, change `autohint' to `autofit' in
	<freetype/config/ftmodule.h> and recompile.

	A few more testing is needed before making this the official
	auto-hinting module.

2004-06-02  Werner Lemberg  <<EMAIL>>

	* src/truetype/ttgload.c (compute_glyph_metrics): Fix compiler
	warnings.

2004-06-01  Werner Lemberg  <<EMAIL>>

	* src/sfnt/sfobjs.c (tt_face_get_name): Make sure that an English
	name record for the Apple platform is preferred to a non-English
	entry for the Microsoft platform.  Problem reported by HANDA
	Ken'ichi.

2004-05-19  George Williams  <<EMAIL>>

	* src/type1/t1load.c (mm_axis_unmap, mm_weights_unmap): New
	auxiliary functions.
	(T1_Get_MM_Var): Provide axis tags.
	Use mm_axis_unmap and mm_weights_unmap to provide default values
	for design and normalized axis coordinates.

	* include/freetype/t1tables.h (PS_DesignMapRec): Change type of
	`design_points' to FT_Long.
	Update all users.

2004-05-17  Werner Lemberg  <<EMAIL>>

	* src/base/ftbbox.c (BBox_Conic_Check): Fix boundary cases.
	Reported by Mikey Anbary <<EMAIL>>.

2004-05-15  Werner Lemberg  <<EMAIL>>

	* src/sfnt/sfobjs.c (sfnt_done_face): Free face->postscript_name.

2004-05-15  George Williams  <<EMAIL>>

	* src/sfnt/ttload.c (tt_face_load_max_profile): Always set
	face->root.num_glyphs.

2004-05-14  Masatake YAMATO  <<EMAIL>>
	    George Williams  <<EMAIL>>

	* src/sfnt/ttload.c (sfnt_dir_check): Handle `bhed' properly.

2004-05-14  Werner Lemberg  <<EMAIL>>

	* src/cache/ftcbasic.c (ftc_basic_family_compare,
	ftc_basic_family_init, ftc_basic_family_get_count,
	ftc_basic_family_load_bitmap, ftc_basic_family_load_glyph,
	ftc_basic_gnode_compare_faceid): Adjust parameters and return types
	to prototypes given in header files from include/freetype/cache.
	Use casts to proper types locally.
	(ftc_basic_image_family_class, ftc_basic_image_cache_class,
	ftc_basic_sbit_family_class, ftc_basic_sbit_cache_class): Remove
	casts.

	* src/cache/ftccback.h: Adjust parameters and return types to
	prototypes given in header files from include/freetype/cache.

	* src/cache/ftcimage.c (ftc_inode_free, ftc_inode_new,
	ftc_inode_weight): Adjust parameters and return types to prototypes
	given in header files from include/freetype/cache.  Use casts to
	proper types locally.

	* src/cache/ftcsbits.c (ftc_snode_free, ftc_snode_new,
	ftc_snode_weight, ftc_snode_compare): Adjust parameters and return
	types to prototypes given in header files from
	include/freetype/cache.  Use casts to proper types locally.

	* src/cache/ftccmap.c (ftc_cmap_node_free, ftc_cmap_node_new,
	ftc_cmap_node_weight, ftc_cmap_node_compare,
	ftc_cmap_node_remove_faceid): Adjust parameters and return types to
	prototypes given in header files from include/freetype/cache.  Use
	casts to proper types locally.
	(ftc_cmap_cache_class): Remove casts.

	* src/cache/ftcglyph.c (ftc_gnode_compare, ftc_gcache_init,
	ftc_gcache_done): Adjust parameters and return types to prototypes
	given in header files from include/freetype/cache.  Use casts to
	proper types locally.

	* src/cache/ftcmanag.c (ftc_size_node_done, ftc_size_node_compare,
	ftc_size_node_init, ftc_size_node_reset,
	ftc_size_node_compare_faceid, ftc_face_node_init,
	ftc_face_node_done, ftc_face_node_compare: Adjust parameters and
	return types to prototypes given in header files from
	include/freetype/cache.  Use casts to proper types locally.

	(ftc_size_list_class, ftc_face_list_class): Remove casts.

2004-05-13  Werner Lemberg  <<EMAIL>>

	* src/autohint/ahmodule.c (ft_autohinter_init, ft_autohinter_done):
	Use FT_Module as parameter and do a cast to FT_AutoHinter locally.
	(autohint_module_class): Remove casts.

	* src/base/ftglyph.c (ft_bitmap_glyph_init, ft_bitmap_glyph_copy,
	ft_bitmap_glyph_done, ft_bitmap_glyph_bbox, ft_outline_glyph_init,
	ft_outline_glyph_done, ft_outline_glyph_copy,
	ft_outline_glyph_transform, ft_outline_glyph_bbox,
	ft_outline_glyph_prepare): Use FT_Glyph as parameter and do a cast
	to FT_XXXGlyph locally.
	Use FT_CALLBACK_DEF throughout.
	(ft_bitmap_glyph_class, ft_outline_glyph_class): Remove casts.

	* src/bdf/bdfdrivr.c (bdf_cmap_init, bdf_cmap_done,
	bdf_cmap_char_index, bdf_cmap_char_next): Use FT_CMap as parameter
	and do a cast to BDF_CMap locally.
	(bdf_cmap_class): Remove casts.

2004-05-12  Werner Lemberg  <<EMAIL>>

	* src/cff/cffgload.h (CFF_Builder): Remove `error'.
	* src/cff/cffgload.c (cff_decoder_parse_charstrings): Replace
	`Memory_Error' with `Fail' and update all users.

2004-05-11  Werner Lemberg  <<EMAIL>>

	* include/freetype/internal/psaux.h (T1_ParseState): New
	enumeration.
	(T1_BuilderRec): Replace `path_begun' with `parse_state'.
	Remove `error'.
	* src/psaux/t1decode.c (t1_decoder_parse_charstrings): Replace
	`Memory_Error' with `Fail' and update all users.
	Don't use `builder->error'.
	Replace `path_begun' with `parse_state' and check parsing states.

	* src/psaux/psobjs.c (t1_builder_init, t1_builder_start_point):
	Replace `path_begun' with `parse_state' and check parsing states.

2004-05-10  George Williams  <<EMAIL>>

	* src/truetype/ttxgvar.c (ft_var_load_avar): Do free arrays in case
	of error -- `avar' is optional so we can't rely on tt_done_blend
	being called automatically.

2004-05-09  George Williams  <<EMAIL>>

	* src/truetype/ttxgvar.c (ft_var_load_avar, ft_var_load_gvar): Fix
	error handling.

2004-05-07  Werner Lemberg  <<EMAIL>>

	* src/pfr/pfrobjs.c, src/pfr/pfrobjs.h (pfr_face_init,
	pfr_face_done, pfr_face_get_kerning, pfr_slot_init, pfr_slot_done,
	pfr_slot_load): Don't use PFR_XXX but FT_XXX arguments which are
	typecast to the proper PFR_XXX types within the function.
	Update code accordingly.

	* src/pfr/pfrdrivr.c (pfr_get_kerning, pfr_get_advance,
	pfr_get_metrics, pfr_get_service): Don't use PFR_XXX but FT_XXX
	arguments which are typecast to the proper PFR_XXX types within the
	function.
	Update code accordingly.
	Use FT_CALLBACK_DEF throughout.
	(pfr_metrics_service_rec, pfr_driver_class): Remove casts.

2004-05-06  Masatake YAMATO  <<EMAIL>>

	* src/truetype/ttgxvar.c (ft_var_load_gvar): Use FT_FACE_STREAM.
	(*): Rename local variable OffsetToData to offsetToData.

2004-05-06  Werner Lemberg  <<EMAIL>>

	* src/cff/cffobjs.c (cff_size_done, cff_size_init, cff_size_reset,
	cff_slot_done, cff_slot_init, cff_face_init, cff_face_done): Access
	root fields directly.
	* src/cff/cffdrivr.c (Load_Glyph): Access root fields directly.

	* src/truetype/ttgload.c (TT_Process_Simple_Glyph): Save current
	frame before calling TT_Vary_Get_Glyph_Deltas.

	* src/pcf/pcfdrivr.c (PCF_CMapRec): Rename `cmap' to `root' for
	consistency.
	(pcf_cmap_init, pcf_cmap_done, pcf_cmap_char_index,
	pcf_cmap_char_next): Don't use PCF_XXX but FT_XXX arguments which
	are typecast to the proper PCF_XXX types within the function.
	Update code accordingly.
	(pcf_cmap_class): Remove casts.
	(PCF_Face_Done, PCF_Face_Init, PCF_Set_Pixel_Size): Don't use
	PCF_XXX but FT_XXX arguments which are typecast to the proper
	PCF_XXX types within the function.
	Update code accordingly.
	Use FT_CALLBACK_DEF throughout.
	(PCF_Set_Point_Size): New wrapper function.
	(PCF_Glyph_Load, pcf_driver_requester): Use FT_CALLBACK_DEF.
	(pcf_driver_class): Remove casts.

2004-05-04  Steve Hartwell  <<EMAIL>>

	* src/truetype/ttobjs.c (tt_driver_done): Fix typo.

2004-05-04  Werner Lemberg  <<EMAIL>>

	* src/bdf/bdfdrivr.c (BDF_Face_Done, BDF_Face_Init,
	BDF_Set_Pixel_Size): Don't use BDF_XXX but FT_XXX arguments which
	are typecast to the proper BDF_XXX types within the function.
	Update code accordingly.
	Use FT_CALLBACK_DEF throughout.
	(BDF_Set_Point_Size): New wrapper function.
	(bdf_driver_class): Remove casts.

	* src/cff/cffdrivr.c (Get_Kerning, Load_Glyph, cff_get_interface):
	Don't use CFF_XXX but FT_XXX arguments which are typecast to the
	proper CFF_XXX types within the function.
	Update code accordingly.
	Use FT_CALLBACK_DEF throughout.
	(cff_driver_class): Remove casts.

	* src/cff/cffobjs.h, src/cff/cffobjs.c (cff_size_done,
	cff_size_init, cff_size_reset, cff_slot_done, cff_slot_init,
	cff_face_init, cff_face_done, cff_driver_init, cff_driver_done):
	Don't use CFF_XXX but FT_XXX arguments which are typecast to the
	proper CFF_XXX types within the function.
	Update code accordingly.
	(cff_point_size_reset): New wrapper function.

	* src/cid/cidobjs.h, src/cid/cidobjs.c (cid_slot_done,
	cid_slot_init, cid_size_done, cid_size_init, cid_size_reset,
	cid_face_done, cid_face_init, cid_driver_init, cid_driver_done):
	Don't use CID_XXX but FT_XXX arguments which are typecast to the
	proper CID_XXX types within the function.
	Update code accordingly.
	(cid_point_size_reset): New wrapper function.

	* src/cid/cidgload.c, src/cid/cidgload.h (cid_slot_load_glyph):
	Don't use CID_XXX but FT_XXX arguments which are typecast to the
	proper CID_XXX types within the function.
	Update code accordingly.

	* src/cid/cidriver.c (cid_get_interface):
	Don't use CID_XXX but FT_XXX arguments which are typecast to the
	proper CID_XXX types within the function.
	Update code accordingly.
	Use FT_CALLBACK_DEF.
	(t1cid_driver_class): Remove casts.

	* src/truetype/ttdriver.c (tt_get_interface): Use FT_CALLBACK_DEF.
	* src/truetype/ttgxvar.c (ft_var_load_avar): Don't free non-local
	variables (this is done later).
	(ft_var_load_avar): Fix call to FT_FRAME_ENTER.
	(TT_Get_MM_Var): Fix size for `fvar_fields'.
	(TT_Vary_Get_Glyph_Deltas): Handle deallocation of local variables
	correctly.

	* src/base/ftdbgmem.c (ft_mem_debug_realloc): Don't abort if
	current size is zero.

2004-05-03  Steve Hartwell  <<EMAIL>>

	* src/truetype/ttobjs.h, src/truetype/ttobjs.c (tt_face_init,
	tt_face_done, tt_size_init, tt_size_done, tt_driver_init,
	tt_driver_done): Don't use TT_XXX but FT_XXX arguments which are
	typecast to the proper TT_XXX types within the function.
	Update code accordingly.

	* src/truetype/ttdriver.c (Get_Kerning, Set_Char_Sizes,
	Set_Pixel_Sizes, Load_Glyph, tt_get_interface): Don't use TT_XXX but
	FT_XXX arguments which are typecast to the proper TT_XXX types
	within the function.
	Update code accordingly.
	(tt_driver_class): Remove casts.

2004-05-02  Werner Lemberg  <<EMAIL>>

	* src/sfnt/ttload.c (tt_face_free_names): Check that `table->names'
	is not NULL.  Reported by Gordon Childs <<EMAIL>>.

2004-04-29  Werner Lemberg  <<EMAIL>>

	* docs/formats.txt: Add more information on PFR format.

2004-04-28  Werner Lemberg  <<EMAIL>>

	* docs/formats.txt: New file.
	* docs/CHANGES: Updated.

2004-04-28  Masatake YAMATO  <<EMAIL>>

	* include/freetype/internal/tttypes.h (GX_BlendRec_)
	[TT_CONFIG_OPTION_GX_VAR_SUPPORT]: Fix a typo.

	* src/truetype/ttgxvar.h (GX_BlendRec_): Fix a typo.

2004-04-27  Masatake YAMATO  <<EMAIL>>

	* src/truetype/ttgxvar.h: Use FT_LOCAL instead of FT_LOCAL_DEF
	for function declarations.

2004-04-25  George Williams  <<EMAIL>>

	* src/truetype/ttgxvar.c (ft_var_apply_tuple): Fix typo.

2004-04-25  Werner Lemberg  <<EMAIL>>

	* src/truetype/Jamfile, docs/CHANGES: Updated.

2004-04-24  Werner Lemberg  <<EMAIL>>

	* src/pcf/pcfdrivr.c: Revert change from 2004-04-17.
	* src/pcf/pcfutil.c: Use FT_LOCAL_DEF.
	* src/pcf/pcfutil.h: Include FT_CONFIG_CONFIG_H.
	Use FT_BEGIN_HEADER and FT_END_HEADER.
	Use FT_LOCAL.

2004-04-24  George Williams  <<EMAIL>>

	Add support for Apple's distortable font technology (in GX fonts).

	* devel/ftoption.h, include/freetype/config/ftoption.h
	(TT_CONFIG_OPTION_GX_VAR_SUPPORT): New macro.

	* include/freetype/ftmm.h (FT_Var_Axis, FT_Var_Named_Style,
	FT_MM_Var): New structures.
	(FT_Get_MM_Var, FT_Set_Var_Design_Coordinates,
	FT_Set_Var_Blend_Coordinates): New function declarations.

	* include/freetype/internal/services/svmm.h (FT_Get_MM_Var_Func,
	FT_Set_Var_Design_Func): New typedefs.
	Update MultiMasters service.

	* include/freetype/internal/tttypes.h
	[TT_CONFIG_OPTION_GX_VAR_SUPPORT]: Include FT_MULTIPLE_MASTERS_H.
	(GX_Blend) [TT_CONFIG_OPTION_GX_VAR_SUPPORT]: New typedef.
	(TT_Face) [TT_CONFIG_OPTION_GX_VAR_SUPPORT]: New members `doblend'
	and `blend'.

	* include/freetype/tttags.h (TTAG_avar, TTAG_cvar, TTAG_gvar): New
	macros.

	* include/freetype/internal/fttrace.h: Add `ttgxvar'.

	* src/base/ftmm.c (FT_Get_MM_Var, FT_Set_Var_Design_Coordinates,
	FT_Set_Var_Blend_Coordinates): New functions.

	* src/sfnt/sfobjs.c (sfnt_load_face)
	[TT_CONFIG_OPTION_GX_VAR_SUPPORT]: Set FT_FACE_FLAG_MULTIPLE_MASTERS
	flag for GX var fonts.

	* src/truetype/ttgxvar.c, src/truetype/ttgxvar.h: New files.

	* src/truetype/truetype.c [TT_CONFIG_OPTION_GX_VAR_SUPPORT]: Include
	ttgxvar.c.

	* src/truetype/ttdriver.c [TT_CONFIG_OPTION_GX_VAR_SUPPORT]: Include
	FT_MULTIPLE_MASTERS_H, FT_SERVICE_MULTIPLE_MASTERS_H, and ttgxvar.h.
	(tt_service_gx_multi_masters) [TT_CONFIG_OPTION_GX_VAR_SUPPORT]:
	New service.
	(tt_services) [TT_CONFIG_OPTION_GX_VAR_SUPPORT]: Updated.

	* src/truetype/ttgload.c [TT_CONFIG_OPTION_GX_VAR_SUPPORT]: Include
	ttgxvar.h.
	(TT_Process_Simple_Glyph, load_truetype_glyph)
	[TT_CONFIG_OPTION_GX_VAR_SUPPORT]: Support GX var fonts.

	* src/truetype/ttobjs.c [TT_CONFIG_OPTION_GX_VAR_SUPPORT]: Include
	ttgxvar.h.
	(tt_done_face) [TT_CONFIG_OPTION_GX_VAR_SUPPORT]: Call
	tt_done_blend.

	* src/truetype/ttpload.c [TT_CONFIG_OPTION_GX_VAR_SUPPORT]: Include
	ttgxvar.h.
	(tt_face_load_cvt)  [TT_CONFIG_OPTION_GX_VAR_SUPPORT]: Call
	tt_face_vary_cvt.

	* src/truetype/rules.mk (TT_DRV_SRC): Add ttgxvar.c.

	* src/type1/t1driver.c (t1_service_multi_masters): Add T1_Get_MM_Var
	and T1_Set_Var_Design.

	* src/type1/t1load.c (FT_INT_TO_FIXED, FT_FIXED_TO_INT): New macros.
	(T1_Get_MM_Var, T1_Set_Var_Design): New functions.

	* src/type1/t1load.h (T1_Get_MM_Var, T1_Set_Var_Design): New
	function declarations.

2004-04-23  Werner Lemberg  <<EMAIL>>

	* include/freetype/ftcache.h (FT_Get_CharMap_Index): Rename
	declaration and move to...
	* include/freetype/freetype.h (FT_Get_Charmap_Index): Here.
	(FREETYPE_PATCH): Set to 9.

	* src/base/ftobjs.c (FT_Get_Charmap_Index): New function.

	* builds/unix/configure.ac (version_info): Set to 9:7:3.
	* builds/unix/configure: Updated.

	* builds/win32/visualc/index.html,
	builds/win32/visualc/freetype.dsp,
	builds/win32/visualc/freetype.vcproj: s/218/219/.

	* builds/freetype.mk (refdoc), README, Jamfile (RefDoc):
	s/2.1.8/2.1.9/.

	* docs/CHANGES, docs/VERSION.DLL: Updated.

2004-04-21  Werner Lemberg  <<EMAIL>>

	* src/cff/cffparse.c (cff_parser_run), src/psaux/psobjs.c
	(ps_parser_load_field): Use FT_CHAR_BIT.

2004-04-21  David Turner  <<EMAIL>>


	* Version 2.1.8 released.
	=========================


	* src/cff/cffobjs.c (cff_face_init): Fix a small memory leak.

	* src/autofit/afloader.c (af_loader_load_g), src/autofit/afmodule.c
	(af_autofitter_load_glyph), src/base/ftdebug.c (FT_Trace_Get_Name):
	Remove compiler warnings.

	* src/autofit/aftypes.h: Undefine AF_DEBUG.

	* src/lzw/zopen.c (rmask), src/pcf/pcfdrivr.c (pcf_service_bdf,
	pcf_services), src/pcf/pcfread.c (tableNames), src/psaux/psobjs.c
	(ft_char_table), src/type42/t42drivr.c (t42_service_glyph_dict,
	t42_service_ps_font_name): Decorate data arrays with `const' to
	avoid populating the `.data' segment.

	* src/lzw/Jamfile: New file.

2004-04-20  Werner Lemberg  <<EMAIL>>

	* src/psaux/psobjs.c (T1Radix): Renamed to...
	(ps_radix): This.
	Update current cursor position.

	* docs/CHANGES: Updated.

2004-04-18  Werner Lemberg  <<EMAIL>>

	* src/truetype/ttgload.c, src/truetype/ttgload.h (TT_Load_Glyph),
	src/ttdriver.c (Load_Glyph): Change type of `glyph_index' to
	FT_UInt.  From Lex Warners.

2004-04-17  Chisato Yamauchi  <<EMAIL>>

	* src/sfnt/ttload.c (tt_face_load_sfnt_header): Really fix change
	from 2004-03-19.

	* src/bdf/bdfdrivr.c (BDF_Face_Init): Use `ft_strlen'.

	* src/pcf/pcfutil.c, src/pcf/pcfutil.h: Decorate functions with
	`static'.
	Remove unused function `RepadBitmap'.
	* src/pcf/pcfdrivr.c: Don't include pcfutil.h.

2004-04-16  Werner Lemberg  <<EMAIL>>

	* builds/unix/freetype-config.in (usage): Fix and improve usage
	information.

2004-04-15  Werner Lemberg  <<EMAIL>>

	* builds/unix/ftconfig.in, builds/vms/ftconfig.h: Define
	FT_CHAR_BIT.

	* src/base/ftobjs.c (FT_Load_Glyph): Don't apply autohinting if
	glyph is vertically distorted or mirrored.

	* src/cff/cffgload.c (cff_slot_load): Handle zero `size' properly
	for embedded bitmaps.

	* docs/CHANGES: Updated.

2004-04-15  bytesoftware  <<EMAIL>>

	* include/freetype/config/ftconfig.h, src/base/ftstream.c
	(FT_Stream_ReadFields): More fixes using FT_CHAR_BIT.

2004-04-14  Werner Lemberg  <<EMAIL>>

	* include/freetype/config/ftconfig.h (FT_CHAR_BIT): New macro.

2004-04-14  Alex Strelnikov  <<EMAIL>>

	* src/cache/ftcsbits.c (ftc_snode_load): Initialize `*asize' in case
	of error.

2004-04-14  Werner Lemberg  <<EMAIL>>

	* src/base/ftmac.c [__GNUC__]: Define OS_INLINE.
	* builds/unix/configure.ac: Don't try to remove `-ansi' compilation
	switch on the Mac.

	* builds/unix/ltmain.sh: Regenerated with `libtoolize --force
	--copy' from libtool 1.5.6.
	* builds/unix/aclocal.m4: Regenerated with `aclocal -I .' from
	automake 1.8a.
	* builds/unix/configure: Regenerated with autoconf 2.59a.

2004-04-13  Werner Lemberg  <<EMAIL>>

	* include/freetype/config/ftconfig.h: Use CHAR_BIT to define
	size of FT_SIZEOF_xxx.

2004-04-12  Chisato Yamauchi  <<EMAIL>>

	* include/freetype/internal/sfnt.h (TT_Find_SBit_Image_Func,
	TT_Load_SBit_Metrics_Func): New typedefs.
	(SFNT_Interface): Add find_sbit_image and load_sbit_metrics.

	* src/sfnt/sfdriver.c (sfnt_interface): Updated.
	* src/sfnt/ttsbit.h (tt_find_sbit_image, tt_load_sbit_metrics): New
	declarations.
	* src/sfnt/ttsbit.c (find_sbit_image): Renamed to...
	(tt_find_sbit_image): This.
	Updated all callers.
	(load_sbit_metrics): Renamed to...
	(tt_load_sbit_metrics): This.
	Updated all callers.

2004-04-12  Werner Lemberg  <<EMAIL>>

	* configure: Accept makepp also.

	* builds/unix/detect.mk: Use proper path to unix-def.mk.
	* builds/unix/unix-def.in (BUILD_DIR, PLATFORM): Remove.
	* builds/unix/unix.mk (BUILD_DIR, PLATFORM): Define.
	Use BUILD_DIR.

	* docs/INSTALL, docs/INSTALL.GNU, docs/INSTALL.UNX: Update
	documentation on makepp.

2004-04-11  Werner Lemberg  <<EMAIL>>

	* src/lzw/zopen.c: Don't include sys/param.h and sys/stat.h.

2004-04-10  Werner Lemberg  <<EMAIL>>

	* src/lzw/ftlzw.c: Include zopen.h dependent on
	FT_CONFIG_OPTION_USE_LZW.

	* src/base/ftdebug.c: s/index/idx/ to avoid compiler warnings.

2004-04-02  Werner Lemberg  <<EMAIL>>

	* builds/unix/ltmain.sh: Regenerated with `libtoolize --force
	--copy' from libtool 1.5.2.
	* builds/unix/aclocal.m4: Regenerated with `aclocal -I .' from
	automake 1.8a.
	* builds/unix/configure: Regenerated with autoconf 2.59a.

2004-04-01  Werner Lemberg  <<EMAIL>>

	* builds/unix/ft-munmap.m4 (FT_MUNMAP_PARAM): Fix arguments of
	AC_COMPILE_IFELSE.
	* builds/unix/aclocal.m4: Regenerated with `aclocal -I .' from
	automake 1.8a.
	* builds/unix/configure: Regenerated with autoconf 2.59a.
	* builds/unix/config.guess, builds/unix/config.sub: Updated from
	`config' CVS module at subversions.gnu.org.
	* builds/unix/install-sh, builds/unix/mkinstalldirs: Updated from
	`texinfo' CVS module at subversions.gnu.org.
	* builds/freetype.mk (refdoc): Updated.

2004-03-31  Werner Lemberg  <<EMAIL>>

	Handle broken FNT files which don't have a trailing NULL byte
	in the face name string.

	* src/winfonts/winfnt.h (FNT_FontRec): New member `family_name'.
	* src/winfonts/winfnt.c (fnt_font_done): Free font->family_name.
	(FNT_Face_Init): Append a final zero byte to the font face name.

2004-03-30  Werner Lemberg  <<EMAIL>>

	* src/sfnt/ttload.c (tt_face_load_sfnt_header): Fix change from
	2004-03-19.

2004-03-27  Werner Lemberg  <<EMAIL>>

	* src/base/descrip.mms (OBJS): Add ftbbox.obj.

2004-03-26  George Williams  <<EMAIL>>

	Add vertical phantom points.

	* include/freetype/internal/tttypes.h (TT_LoaderRec): Add
	`top_bearing', `vadvance', `pp3', and `pp4'.

	* src/autofit/afloader.c (af_loader_load_g): Handle two more points.

	* src/autohint/ahhint.c (ah_hinter_load): Handle two more points.
	* src/truetype/ttgload.c (Get_VMetrics): New function.
	(TT_Load_Simple_Glyph, TT_Process_Simple_Glyph): Handle two more
	points.
	(load_truetype_glyph): Use Get_VMetrics.
	Handle two more points.
	(compute_glyph_metrics): Thanks to vertical phantom points we now
	can always compute `advance_height' and `top_bearing'.
	* src/truetype/ttobjs.h (TT_SubglyphRec): Add vertical phantom
	points.


	* src/autohint/ahglyph.c (ah_outline_load): Fix allocation of
	`news'.

2004-03-21  Werner Lemberg  <<EMAIL>>

	* src/bdf/bdfdrivr.c (BDF_Glyph_Load): Fix left side bearing.

2004-03-20  Steve Hartwell  <<EMAIL>>

	* src/cache/ftcmru.c (FTC_MruList_RemoveSelection): Handle a NULL
	value for `selection' as `select all'.

2004-03-19  Steve Hartwell  <<EMAIL>>

	* src/sfnt/ttload.c (tt_face_load_sfnt_header): Reject face_index
	values > 0 if loading non-TTC fonts.

	* src/base/ftmac.c (open_face_from_buffer): Set positive face_index
	to zero before calling FT_Open_Face.

	* docs/CHANGES: Updated.

2004-03-04  Werner Lemberg  <<EMAIL>>

	* Jamfile, vms_make.com, builds/win32/visualc/freetype.dsp,
	builds/win32/visualc/freetype/vcproj, include/freetype/ftmoderr.h:
	Add LZW module.

	* Jamfile.in: Removed.

	* docs/CHANGES: Updated.

	* include/freetype/internal/ftobjs.h: s/MIN/FT_MIN/, s/MAX/FT_MAX/,
	s/ABS/FT_ABS/.  Updated all callers.

	* src/type1/t1load.c (parse_dict), src/pcf/pcfdrivr.c
	(PCF_Face_Init): Use FT_ERROR_BASE.

2004-03-04  Albert Chin  <<EMAIL>>

	Add support for PCF fonts compressed with LZW (extension .pcf.Z,
	created with `compress').

	* include/freetype/config/ftoption.h, devel/ftoption.h
	(FT_CONFIG_OPTION_USE_LZW): New macro.

	* include/freetype/ftlzw.h: New file.
	* include/freetype/config/ftheader.h (FT_LZW_H): New macro for
	ftlzw.h.

	* src/lzw/*: New files.

	* src/pcf/pcfdrivr.c: Include FT_LZW_H.
	(PCF_Face_Init): Try LZW also.

	* src/gzip/ftgzip.c: s/0/Gzip_Err_Ok/ where appropriate.
	Beautify.

2004-03-03  Werner Lemberg  <<EMAIL>>

	* src/pshinter/pshalgo.c (psh_hint_table_init): Simplify code.

2004-03-02  Werner Lemberg  <<EMAIL>>

	Add embedded bitmap support to CFF driver.

	* src/cff/cffobjs.h (CFF_SizeRec): New structure.

	* src/cff/cffgload.c (cff_builder_init): Updated.
	(cff_slot_load): Updated.
	[TT_CONFIG_OPTION_EMBEDDED_BITMAPS]: Load sbit.

	* src/cff/cffobjs.c (sbit_size_reset)
	[TT_CONFIG_OPTION_EMBEDDED_BITMAPS]: New function.
	(cff_size_get_globals_funcs, cff_size_done, cff_size_init): Updated.
	(cff_size_reset): Updated.
	[TT_CONFIG_OPTION_EMBEDDED_BITMAPS]: Call sbit_size_reset.

	* src/cff/cffdrivr.c (Load_Glyph): Updated.
	(cff_driver_class): Use CFF_SizeRec.

	* docs/CHANGES: Updated.

2004-03-01  Werner Lemberg  <<EMAIL>>

	* src/pshinter/pshglob.c (psh_globals_scale_widths): Don't use
	FT_RoundFix but FT_PIX_ROUND.
	(psh_blues_snap_stem): Don't use blue_shift but blue_threshold.

	* src/pshinter/pshalgo.c (PSH_STRONG_THRESHOLD_MAXIMUM): New macro.
	(psh_glyph_find_strong_points): Use PSH_STRONG_THRESHOLD_MAXIMUM.
	(psh_glyph_find_blue_points): New function.  Needed for fonts like
	p052003l.pfb (URW Palladio L Roman) which have flex curves at the
	base line within blue zones, but the flex curves aren't covered by
	hints.
	(ps_hints_apply): Use psh_glyph_find_blue_points.

2004-02-27  Garrick Meeker  <<EMAIL>>

	* builds/unix/configure.ac: Fix compiler flags for
	`--with-old-mac-fonts'.
	* builds/unix/configure: Regenerated.

	* src/base/ftmac.c: s/TARGET_API_MAC_CARBON/!TARGET_API_MAC_OS8/.
	(FT_New_Face_From_Resource): New function.
	(FT_New_Face): Use FT_New_Face_From_Resource.
	(FT_New_Face_From_FSSpec): Use FT_New_Face_From_Resource.
	[__MWERKS__]: Don't include FSp_fopen.h.

2004-02-26  Werner Lemberg  <<EMAIL>>

	* src/pshinter/pshglob.c (psh_globals_new): Fix value of
	`dim->stdw.count'.
	Don't assign default values to blue scale and blue shift.

2004-02-25  Werner Lemberg  <<EMAIL>>

	* docs/CHANGES: Updated.

2004-02-25  Garrick Meeker  <<EMAIL>>
	    Steve Hartwell  <<EMAIL>>

	Improve MacOS fond support.  Provide a new API
	`FT_New_Face_From_FSSpec' similar to `FT_New_Face'.

	* src/base/ftmac.c [__MWERKS__]: Include FSp_fopen.h.
	STREAM_FILE [__MWERKS__]: New macro.
	(ft_FSp_stream_close, ft_FSp_stream_io) [__MWERKS__]: New functions.
	(file_spec_from_path) [__MWERKS__]: Updated #if statement.
	(get_file_type, make_lwfn_spec): Use `const' for argument.
	(is_dfont) [TARGET_API_MAC_CARBON]: Removed.
	(count_face_sfnt, count_faces): New functions.
	(parse_fond): Do some range checking.
	(read_lwfn): Change type of second argument.
	No longer call FSpOpenResFile.
	(OpenFileAsResource): New function.
	(FT_New_Face_From_LWFN): Use `const' for second argument.
	Use OpenFileAsResource.
	(FT_New_Face_From_Suitcase): Change type of second argument.
	No longer call FSpOpenResFile.
	Loop over all resource indices.
	(FT_New_Face_From_dfont) [TARGET_API_MAC_CARBON]: Removed.
	(FT_GetFile_From_Mac_Name): Use `const' for first argument.
	(ResourceForkSize): Removed.
	(FT_New_Face): Updated to use new functions.
	(FT_New_Face_From_FSSpec): New function.

	* include/freetype/ftmac.h: Updated.

2004-02-24  Malcolm Taylor  <<EMAIL>>

	* src/autohint/ahhint.c (ah_hinter_load) <FT_GLYPH_FORMAT_OUTLINE>:
	Handle case where outline->num_vedges is zero while computing hinted
	metrics.

2004-02-24  Gordon Childs  <<EMAIL>>

	* src/cff/cffcmap.c (cff_cmap_unicode_init): Provide correct value
	for `count'.

2004-02-24  Werner Lemberg  <<EMAIL>>

	* include/freetype/t1tables.h (PS_PrivateRec): Add
	`expansion_factor'.

	* src/pshinter/pshglob (psh_blues_scale_zones): Fix computation
	of blues->no_overshoots -- `blues_scale' is stored with a
	magnification of 1000, and `scale' returns fractional pixels.

	* src/type1/t1load.c (T1_Open_Face): Initialize `blue_shift',
	`blue_fuzz', `expansion_factor', and `blue_scale' according to the
	Type 1 specification.

	* src/type1/t1tokens.h: Handle `ExpansionFactor'.

	* docs/CHANGES: Updated.

2004-02-24  Masatake YAMATO  <<EMAIL>>

	Provide generic access to MacOS resource forks.

	* src/base/ftrfork.c, include/freetype/internal/ftrfork.h: New
	files.

	* src/base/ftobjs.c: Include FT_INTERNAL_RFORK_H.
	(Mac_Read_POST_Resource, Mac_Read_sfnt_Resource): Remove arguments
	`resource_listoffset' and `resource_data' and adapt code
	accordingly.  These values are calculated outside of the function
	now.
	Add new argument `offsets'.
	(IsMacResource): Use `FT_Raccess_Get_HeaderInfo' and
	`FT_Raccess_Get_DataOffsets'.
	(load_face_in_embedded_rfork): New function.
	(load_mac_face): Use load_face_in_embedded_rfork.
	(ft_input_stream_new): Renamed to...
	(FT_Stream_New): This.  Use FT_BASE_DEF.  Updated all callers.
	(ft_input_stream_free): Renamed to...
	(FT_Stream_Free): This.  Use FT_BASE_DEF.  Updated all callers.

	* src/base/ftbase.c: Include ftrfork.c.

	* src/base/rules.mk (BASE_SRC), src/base/Jamfile: Updated.

	* include/freetype/internal/internal.h (FT_INTERNAL_RFORK_H):
	New macro.

	* include/freetype/internal/fttrace.h: Added `rfork' as a new
	trace definition.

	* include/freetype/internal/ftstream.h: Declare FT_Stream_New and
	FT_Stream_Free.

	* include/freetype/config/ftoption.h, devel/ftoption.h
	(FT_CONFIG_OPTION_GUESSING_EMBEDDED_RFORK): New option.

	* include/freetype/config/ftstdlib.h (ft_strrchr): New macro.

2004-02-23  Werner Lemberg  <<EMAIL>>

	* docs/CHANGES: Updated.

	* include/freetype/internal/ftdebug.h: Include FT_FREETYPE_H.

2004-02-23  Masatake YAMATO  <<EMAIL>>

	Provide a simple API to control FreeType's tracing levels.

	* include/freetype/internal/ftdebug.h (FT_Trace_Get_Count,
	FT_Trace_Get_Name): New declarations.

	* src/base/ftdebug.c (FT_Trace_Get_Count, FT_Trace_Get_Name): New
	functions.

2004-02-23  David Turner  <<EMAIL>>

	* src/autofit/afhints.c, src/autofit/afhints.h,
	src/autofit/aflatin.c, src/autofit/afloader.c, src/types.h: Grave
	bugs have been fixed.  The auto-fitter works, doesn't crash, but
	still produces unexpected results...

2004-02-21  Werner Lemberg  <<EMAIL>>

	* src/pshinter/pshalgo.c (PSH_STRONG_THRESHOLD): Changed to hold
	the accepted shift for strong points in fractional pixels (which
	is a heuristic value).
	(psh_glyph_find_strong_points): Compute threshold for
	psh_hint_table_find_strong_points.
	(psh_hint_table_find_strong_point): Add parameter to pass threshold.

2004-02-20  Werner Lemberg  <<EMAIL>>

	* src/pshinter/pshrec.c (ps_mask_table_set_bits): Don't call
	ps_mask_table_alloc but ps_mask_table_last.
	(ps_hints_t2mask): Use correct position and number for vertical
	and horizontal hinter mask bits.

	* docs/CHANGES: Updated.

2004-02-19  Werner Lemberg  <<EMAIL>>

	* src/base/ftstroke.c (FT_Glyph_StrokeBorder): Fix enum handling.
	* src/cff/cffdrivr.c (cff_get_cmap_info): Remove compiler warning.

2004-02-18  Werner Lemberg  <<EMAIL>>

	* include/freetype/freetype.h: Document FT_LOAD_TARGET_XXX properly.

	* src/base/ftglyph.c (ft_bitmap_glyph_class,
	ft_outline_glyph_class): Tag with FT_CALLBACK_TABLE_DEF.

	* src/smooth/ftsmooth.c (ft_smooth_render): Handle
	FT_RENDER_MODE_LIGHT.

2004-02-17  Werner Lemberg  <<EMAIL>>

	Fix callback functions in cache module.

	* src/cache/ftccback.h: New file for callback declarations.

	* src/cache/ftcbasic.c (ftc_basic_family_compare,
	ftc_basic_family_init, ftc_basic_family_get_count,
	ftc_basic_family_load_bitmap, ftc_basic_family_load_glyph,
	ftc_basic_gnode_compare_faceid): Use FT_CALLBACK_DEF.
	(ftc_basic_image_family_class, ftc_basic_image_cache_class,
	ftc_basic_sbit_family_class, ftc_basic_sbit_cache_class):
	Use FT_CALLBACK_TABLE_DEF and local wrapper functions.

	* src/cache/ftccache.c: Include ftccback.h.
	(ftc_cache_init, ftc_cache_done): New wrapper functions which use
	FT_LOCAL_DEF.

	* src/cache/ftccmap.c: Include ftccback.h.
	(ftc_cmap_cache_class): Use local wrapper functions.

	* src/cache/ftcglyph.c: Include ftccback.h.
	(ftc_gnode_compare, ftc_gcache_init, ftc_gcache_done): New wrapper
	functions which use FT_LOCAL_DEF.

	* src/cache/ftcimage.c: Include ftccback.h.
	(ftc_inode_free, ftc_inode_new, ftc_inode_weight): New wrapper
	functions which use FT_LOCAL_DEF.

	* src/cache/ftcmanag.c (ftc_size_list_class, ftc_face_list_class):
	Use FT_CALLBACK_TABLE_DEF.

	* src/cache;/ftcsbits.c: Include ftccback.h.
	(ftc_snode_free, ftc_snode_new, ftc_snode_weight,
	ftc_snode_compare): New wrapper functions which use FT_LOCAL_DEF.

	* src/cache/rules.mk (CACHE_DRV_H): Add ftccback.h.

2004-02-17  Masatake YAMATO  <<EMAIL>>

	* include/freetype/ftmac.h (FT_GetFile_From_Mac_Name): Fix a typo
	(FT_EXPORT_DEF -> FT_EXPORT).

	* include/freetype/ftxf86.h (FT_Get_X11_Font_Format): Ditto.

2004-02-15  Werner Lemberg  <<EMAIL>>

	* src/base/ftobjs.c (FT_Set_Char_Size): Fix typo.

2004-02-14  Masatake YAMATO  <<EMAIL>>

	* builds/unix/ftsystem.c: Include errno.h.
	(ft_close_stream): Renamed to...
	(ft_close_stream_by_munmap): This.
	(ft_close_stream_by_free): New function.
	(FT_Stream_Open): Use fallback method if mmap fails.
	Use proper function for closing the stream.

2004-02-14  Werner Lemberg  <<EMAIL>>

	* src/type1/t1load.c (parse_dict): Initialize `start_binary'.

2004-02-13  Robert Etheridge  <<EMAIL>>

	* src/type42/t42objs.c (T42_Face_Init), src/type1/t1objs.c
	(T1_Face_Init), src/cid/cidobjs.c (cid_face_init): Fix computation
	of underline_position and underline_thickness.

2004-02-12  Werner Lemberg  <<EMAIL>>

	* src/base/ftobjs.c (FT_Set_Char_Size): Return immediately if
	ppem values don't change.  Suggested by Graham Asher.

2004-02-11  Werner Lemberg  <<EMAIL>>

	* src/cid/cidload.c (cid_face_open): Always allocate
	face->cid_stream so that we can deallocate it safely.

2004-02-10  Werner Lemberg  <<EMAIL>>

	Make the PS parser more tolerant w.r.t. non-standard font data.  In
	general, an error is only reported in case of a syntax error; a
	wrong type is now simply ignored (if possible).  To be independent
	of the order of various MM-specific keywords, the parse_shared_dict
	routine has been removed -- the PS parser is now capable to skip
	this data.  It no longer fails on parsing e.g.

	  dup /WeightVector exch def

	Since the token following /WeightVector isn't `[' (starting an
	array) it is simply ignored.

	* include/freetype/fterrdef.h: Define `FT_Err_Ignore' (0xA2) as a
	new internal error value.

	* src/type1/t1load.c (parse_blend_axis_types,
	parse_blend_design_positions, parse_blend_design_map): Return
	T1_Err_Ignore if no proper array is following the keyword.
	(parse_weight_vector): Use T1_ToTokenArray, initializing `blend'
	structure, if necessary.
	Return T1_Err_Ignore if no proper array is following the keyword.
	(parse_shared_dict): Removed.
	(parse_encoding): Set parser->root.error to return T1_Err_Ignore
	if no result can be obtained.
	Check for errors before accessing `elements' array.
	(t1_keywords): Remove /shareddict.
	(parse_dict): Reset error if t1_load_keyword returns T1_Err_Ignore.
	Set keyword_flag only in case of success.
	Check error code if skipping an unrecognized token.
	(T1_Open_Face) [!T1_CONFIG_OPTION_NO_MM_SUPPORT]: Call T1_Done_Blend
	if blend commands haven't set up a proper MM font.

	* src/psaux/psobjs.c (ps_parser_load_field_table): Remove special
	code for synthetic fonts.
	Return PSaux_Err_Ignore if no proper value has been found.

2004-02-09  Werner Lemberg  <<EMAIL>>

	* src/cff/cffgload.c (cff_decoder_parse_charstrings)
	<cff_op_endchar>: Preserve glyph width before calling
	cff_operator_seac.

2004-02-09  Martin Muskens  <<EMAIL>>

	* src/cff/cffgload.c (cff_decoder_parse_charstrings): Handle special
	first argument for `hintmask' and `cntrmask' operators also.

2004-02-08  Werner Lemberg  <<EMAIL>>

	* builds/unix/configure.in: Call AC_SUBST for `enable_shared',
	`hardcode_libdir_flag_spec', and `wl'.
	* builds/unix/configure: Regenerated.

	* builds/unix/freetype-config.in: Make --prefix and --exec-prefix
	actually work.
	Report a proper --rpath (or -R) value for --libs argument if a
	shared library has been built.

	* docs/CHANGES: Updated.

2004-02-07  Keith Packard  <<EMAIL>>

	* src/bdf/bdfdrivr.c (BDF_Face_Init, BDF_Set_Pixel_Size): Fix
	computation of various vertical and horizontal metric values.

	* src/pcfdrivr.c (PCF_Set_Pixel_Size), src/pcfread (pcf_load_font):
	Ditto.

2004-02-07  Werner Lemberg  <<EMAIL>>

	* builds/win32/visualc/index.html,
	builds/win32/visualc/freetype.dsp,
	builds/win32/visualc/freetype.dsw, docs/CHANGES: Updated.

2004-02-07  Vitaliy Pasternak  <<EMAIL>>

	* builds/win32/visualc/freetype.sln,
	builds/win32/visualc/freetype.vcproj: New files for VS.NET 2003.

2004-02-03  Werner Lemberg  <<EMAIL>>

	* include/freetype/cache/ftccache.h (FTC_CACHE_LOOKUP_CMP):
	Initialize `node'.
	* src/type1/t1load.c (parse_dict): Initialize `have_integer'.

2004-02-02  Werner Lemberg  <<EMAIL>>

	* src/type1/t1load.c (parse_dict): Handle `RD' and `-|' commands
	outside of /Subrs or /CharStrings.  This can happen if there is
	additional code manipulating those two arrays so that FreeType
	doesn't recognize them properly.
	(T1_Open_Face): Improve an error message.

2004-02-01  Werner Lemberg  <<EMAIL>>

	* src/type1/t1load.c (parse_charstrings): Exit immediately if
	there are no elements in /CharStrings.  This is needed for fonts
	like Optima-Oblique which not only define /CharStrings but access it
	also.

2004-02-01  David Turner  <<EMAIL>>

	* src/sfnt/Jamfile: Removing `ttcmap' from the list of sources.

	* include/freetype/cache/ftccache.h (FTC_CACHE_LOOKUP_CMP)
	<FTC_INLINE>: Provide macro version which doesn't use inline code.
	* include/freetype/cache/ftcglyph.h (FTC_GCACHE_LOOKUP_CMP)
	<FTC_INLINE>: Ditto.
	Use FTC_MRULIST_LOOKUP_CMP.
	* include/freetype/cache/ftcmru.h (FTC_MRULIST_LOOKUP_CMP): New
	macro.
	(FTC_MRULIST_LOOKUP): Use it.

	* src/cache/Jamfile (_sources), src/cache/descrip.mms: Updated.
	* src/cache/ftcbasic.c: Fix compiler warnings.
	* src/cache/ftcmanag.c (FTC_Manager_LookupSize,
	FTC_Manager_LookupFace) <FTC_INLINE>: Use FTC_MRULIST_LOOKUP_CMP.
	* src/cache/ftcmru.c (FTC_MruList_Find): Fix a bug (found after
	heavy testing).

	* Jamfile: Updating `refdoc' target, and adding `autohint' to the
	list of modules to build.  Both the autohinter and autofitter will
	be built by default.  But which one will be used is determined by
	the content of `ftmodule.h'.

	* src/autofit/*: Many updates, but the code is still buggy...

2004-01-31  Werner Lemberg  <<EMAIL>>

	* src/cff/cffgload.c (cff_operator_seac): Fix magnitude of
	accent offset.
	Update code similarly to the seac support for Type 1 fonts.
	(cff_decoder_parse_charstrings) <cff_op_endchar>: Fix magnitude
	of accent offset.
	Don't hint glyphs twice if seac is emulated.
	<cff_op_flex>: Assign correct point tags.
	* docs/CHANGES: Updated.

2004-01-30  Werner Lemberg  <<EMAIL>>

	* src/type1/t1parse.c (T1_Get_Private_Dict): Use FT_MEM_MOVE, not
	FT_MEM_COPY, for copying the private dict.

	* src/type1/t1load.c (parse_subrs): Assign number of subrs only
	in first run.
	(parse_charstrings): Parse /CharStrings in second run without
	assigning values.
	(parse_dict): Skip all /CharStrings arrays but the first.  We need
	this for non-standard fonts like `Optima' which have different
	outlines depending on the resolution.  Note that there is no
	guarantee that we get fitting /Subrs and /CharStrings arrays; this
	can only be done by a real PS interpreter.

2004-01-29  Antoine Leca  <<EMAIL>>

	* builds/win32/visualc/index.html: New file, giving detailed
	explanations about forcing CR+LF line endings for the VC++ project
	files.

2004-01-22  Garrick Meeker  <<EMAIL>>

	* src/cff/cffload.c (cff_subfont_load): Initialize `dict'.

2004-01-22  Werner Lemberg  <<EMAIL>>

	Add support for the hexadecimal representation of binary data
	started with `StartData' in CID-keyed Type 1 fonts.

	* include/freetype/internal/t1types.h (CID_FaceRec): Add new
	members `binary_data' and `cid_stream'.

	* src/cid/cidload.c (cid_read_subrs): Use `face->cid_stream'.
	(cid_hex_to_binary): New auxiliary function.
	(cid_face_open): Add new argument `face_index' to return quickly
	if less than zero.  Updated all callers.
	Call `cid_hex_to_binary', then open and assign memory stream to
	`face->cid_stream' if `parser->binary_length' is non-zero.
	* src/cid/cidload.h: Updated.

	* src/cid/cidobjs.c (cid_face_done): Free `binary_data' and
	`cid_stream'.

	* src/cid/cidparse.c (cid_parser_new): Check arguments to
	`StartData' and set parser->binary_length accordingly.
	* src/cid/cidparse.h (CID_Parser): New member `binary_length'.

	* src/cid/cidgload.c (cid_load_glyph): Use `face->cid_stream'.

	* docs/CHANGES: Updated.

2004-01-21  Werner Lemberg  <<EMAIL>>

	include/freetype/config/ftstdlib.h (ft_atoi): Replaced with...
	(ft_atol): This.
	* src/base/ftdbgmem.c: s/atol/ft_atol/.
	* src/type42/t42drivr.c: s/ft_atoi/ft_atol/.

2004-01-20  Masatake YAMATO  <<EMAIL>>

	* include/freetype/ftcache.h: Delete duplicated definition of
	FTC_FaceID.

	* src/cff/cffdrivr.c (cff_get_cmap_info): Call sfnt module's TT CMap
	Info service function if the cmap comes from sfnt.  Return 0 if the
	cmap is synthesized in cff module.

2004-01-20  David Turner  <<EMAIL>>

	* src/cache/ftcmanag.c (ftc_size_node_compare): Call
	FT_Activate_Size.

2004-01-20  Werner Lemberg  <<EMAIL>>

	* src/type1/t1parse.c (T1_Get_Private_Dict): Skip exactly one
	CR, LF, or CR/LF after `eexec'.

2004-01-18  David Turner  <<EMAIL>>

	* src/sfnt/ttsbit.c (tt_face_set_sbit_strike): Remove compiler
	warning.

	* src/tools/docmaker/*: Updating beautifier tool.

2004-01-15  David Turner  <<EMAIL>>

	* src/base/ftoutln.c (ft_orientation_extremum_compute): Fix
	infinite loop bug.

	* include/freetype/ftstroke.h: Include FT_GLYPH_H.
	(FT_Stroker_Rewind, FT_Glyph_Stroke, FT_Glyph_StrokeBorder): New
	declarations.

	* src/base/ftstroke.c: Include FT_INTERNAL_OBJECTS_H.
	(FT_Outline_GetOutsideBorder): Inverse result.
	(FT_Stroker_Rewind, FT_Glyph_Stroke, FT_Glyph_StrokeBorder): New
	functions.
	(FT_Stroker_EndSubPath): Close path if needed.
	(FT_Stroker_Set, FT_Stroker_ParseOutline): Use FT_Stroker_Rewind.

	* include/freetype/cache/ftcmanag.h (FTC_ScalerRec,
	FTC_Manager_LookupSize): Moved to...
	* include/freetype/ftcache.h (FTC_ScalerRec,
	FTC_Manager_LookupSize): Here.

	* src/tools/docmaker/docbeauty.py: New file to beautify the
	documentation comments (e.g., to convert them to single block border
	mode).
	* src/tools/docmaker/docmaker.py (file_exists, make_file_list):
	Moved to...
	* src/tools/docmaker/utils.py (file_exists, make_file_list): Here.

2004-01-14  David Turner  <<EMAIL>>

	* include/freetype/internal/ftmemory.h (FT_ARRAY_COPY,
	FT_ARRAY_MOVE): New macros to make copying arrays easier.
	Updated all relevant code to use them.

2004-01-14  Werner Lemberg  <<EMAIL>>

	* src/cff/cffload.c (cff_font_load): Load charstrings_index earlier.
	Use number of charstrings as argument to CFF_Load_FD_Select (as
	documented in the CFF specs).

2004-01-13  Graham Asher  <<EMAIL>>

	* src/pshinter/pshalgo.c (psh_glyph_init): Move assignment of
	`glyph->memory' up to free arrays properly in case of failure.

2004-01-10  Masatake YAMATO  <<EMAIL>>

	Make `FT_Get_CMap_Language_ID' work with CFF.  Bug reported by
	Steve Hartwell <<EMAIL>>.

	* src/cff/cffdrivr.c: Include FT_SERVICE_TT_CMAP_H.
	(cff_services): Added an entry for FT_SERVICE_ID_TT_CMAP.
	(cff_get_cmap_info): New function.
	(cff_service_get_cmap_info) New entry for cff_services.

	* src/sfnt/ttcmap0.c: Exit loop after a format match has been found.
	Suggested by Steve Hartwell <<EMAIL>>.

2004-01-03  Masatake YAMATO  <<EMAIL>>

	* src/base/ftobjs.c (destroy_charmaps): New function.
	(destroy_face, open_face): Use `destroy_charmaps'.

2004-01-01  Werner Lemberg  <<EMAIL>>

	* docs/CHANGES: Updated.

2004-01-01  Michael Jansson  <<EMAIL>>

	* src/winfonts/winfnt.c (FNT_Size_Set_Pixels): Fix sign of
	size->metrics.descender.

2003-12-31  Wolfgang Domröse  <<EMAIL>>

	* src/cff/cffgload.c (cff_decoder_parse_charstrings)
	[FT_DEBUG_LEVEL_TRACE]: Use `%ld' in FT_TRACE4.
	<cff_op_flex1>: Change type of dx and dy to FT_Pos and remove
	cast for accessing arguments.

2003-12-31  Werner Lemberg  <<EMAIL>>

	* src/truetype/ttgload.c (TT_Load_Simple_Glyph): Revert previous
	change.  It's not necessary.

2003-12-29  Smith Charles  <<EMAIL>>

	* src/truetype/ttgload.c (TT_Load_Simple_Glyph): Handle `repeated
	flags set' correctly.

2003-12-29  Werner Lemberg  <<EMAIL>>

	* src/cff/cffobjs.c (cff_face_init): Fix memory leak by deallocating
	`full' and `weight' properly.
	* src/cff/cffgload.c (cff_decoder_parse_charstrings)
	<cff_op_hintmask> [FT_DEBUG_LEVEL_TRACE]: Use `0x' as prefix for
	tracing output.

2003-12-26  Werner Lemberg  <<EMAIL>>

	* include/freetype/internal/sfnt.h (TT_Set_SBit_Strike_Func):
	Use FT_UInt for ppem values.
	* src/sfnt/ttsbit.c (tt_face_set_sbit_strike): Use FT_UInt for
	ppem values.
	* src/sfnt/ttsbit.h: Updated.

	* src/base/ftobjs.c (FT_Set_Pixel_Sizes): Don't allow ppem values
	larger than -0FFFF.

2003-12-25  Werner Lemberg  <<EMAIL>>

	* src/base/fttrigon.c, src/base/ftgloadr.c: Include
	FT_INTERNAL_OBJECTS_H.

	* src/base/ftstroke.c (FT_Outline_GetInsideBorder,
	FT_Outline_GetOutsideBorder): s/or/o/ to make it compile with
	C++ compilers.

	* src/cache/ftcmru.c, include/freetype/cache/ftcmru.h:
	s/select/selection/ to avoid compiler warning.
	* src/cff/cffload.h: s/select/ftselect/ to avoid potential
	compiler warning.

2003-12-24  Werner Lemberg  <<EMAIL>>

	* src/cache/ftcsbits.c (FTC_SNode_Weight):
	s/FTC_SBIT_ITEM_PER_NODE/FTC_SBIT_ITEMS_PER_NODE/.

2003-12-24  David Turner  <<EMAIL>>

	* Fixed compilation problems in the cache sub-system.

	* Partial updates to src/autofit.

	* Jamfile (FT2_COMPONENTS): Add autofit module.

2003-12-23  Werner Lemberg  <<EMAIL>>

	* src/cff/cffgload.c (cff_lookup_glyph_by_stdcharcode): Handle
	CID-keyed fonts.

2003-12-23  David Turner  <<EMAIL>>

	* include/freetype/internal/ftobjs.h (FT_PAD_FLOOR, FT_PAD_ROUND,
	FT_PAD_CEIL, FT_PIX_FLOOR, FT_PIX_ROUND, FT_PIX_CEIL): New macros.
	They are used to avoid compiler warnings with very pedantic compilers.
	Note that `(x) & -64' causes a warning if (x) is not signed.  Use
	`(x) & ~63' instead!
	Updated all related code.

	Add support for extraction of `inside' and `outside' borders.

	* src/base/ftstroke.c (FT_StrokerBorder): New enumeration.
	(FT_Outline_GetInsideBorder, FT_Outline_GetOutsideBorder,
	FT_Stroker_GetBorderCounts, FT_Stroker_ExportBorder): New functions.
	(FT_StrokeBorderRec): New boolean member `valid'.
	(ft_stroke_border_get_counts): Updated.
	* include/freetype/ftstroke.h: Updated.

2003-12-22  Werner Lemberg  <<EMAIL>>

	* include/freetype/ftwinfnt.h (FT_WinFNT_ID_*): New definitions
	to describe the `charset' field in FT_WinFNT_HeaderRec.
	* src/winfonts/winfnt.c (FNT_Face_Init): Set encoding to
	FT_ENCODING_NONE except for FT_WinFNT_ID_MAC.

	* include/freetype/freetype.h (FT_Encoding): Improve comment,
	based on work by Detlef Würkner <<EMAIL>>.

	* docs/CHANGES: Updated.

2003-12-22  David Turner  <<EMAIL>>

	* include/freetype/ftcache.h,
	include/freetype/cache/ftcmanag.h,
	include/freetype/cache/ftccache.h,
	include/freetype/cache/ftcmanag.h,
	include/freetype/cache/ftcmru.h (added),
	include/freetype/cache/ftlru.h (removed),
	include/freetype/cache/ftcsbits.h,
	include/freetype/cache/ftcimage.h,
	include/freetype/cache/ftcglyph.h,
	src/cache/ftcmru.c,
	src/cache/ftcmanag.c,
	src/cache/ftccache.c,
	src/cache/ftcglyph.c,
	src/cache/ftcimage.c,
	src/cache/ftcsbits.c,
	src/cache/ftccmap.c,
	src/cache/ftcbasic.c (added),
	src/cache/ftlru.c (removed):

	  *Complete* rewrite of the cache sub-system to `solve' the
	  following points:

	    - all public APIs have been moved to FT_CACHE_H, everything
	      under `include/freetype/cache' is only needed by client
	      applications that want to implement their own caches

	    - a new function named FTC_Manager_RemoveFaceID to deal
	      with the uninstallation of FaceIDs

	    - the image and sbit cache are now abstract classes, that
	      can be extended much more easily by client applications

	    - better performance in certain areas.  Further optimizations
	      to come shortly anyway...

	    - the FTC_CMapCache_Lookup function has changed its signature,
	      charmaps can now only be retrieved by index

	    - FTC_Manager_Lookup_Face => FTC_Manager_LookupFace
	      FTC_Manager_Lookup_Size => FTC_Manager_LookupSize (still in
	      private header for the moment)

2003-12-21  Werner Lemberg  <<EMAIL>>

	* src/type1/t1load.c (parse_dict): Stop parsing if `eexec' keyword
	is encountered.

2003-12-19  Werner Lemberg  <<EMAIL>>

	* src/cff/cfftypes.h (CFF_MAX_CID_FONTS): Increase to 32.  For
	example, the Japanese Hiragino font already contains 15 subfonts.

	* src/cff/cffload.c (cff_font_load): Deallocate `sids' array for
	CID-keyed fonts.

	* devel/ftoption.h: Define FT_DEBUG_MEMORY.

2003-12-18  Werner Lemberg  <<EMAIL>>

	* include/freetype/ttnameid.h (TT_ADOBE_ID_LATIN_1): New macro.
	* src/type1/t1objs.c (T1_Face_Init): Use TT_ADOBE_ID* values.

2003-12-18  Werner Lemberg  <<EMAIL>>

	* src/cff/cfftypes.h (CFF_FontRecDictRec): Change type of
	`cid_count' to `FT_ULong'.

	* src/cff/cffgload.c (cff_slot_load): Take care of empty `cids'
	array.

	* src/cff/cffload.c (cff_charset_done): Free `cids' array.
	(cff_font_load): Create cids array only for CID-keyed fonts which
	are subsetted.

	* src/cff/cffobjs.c (cff_face_init): Check the availability of
	the PSNames modules for non-pure CFFs also.
	Set FT_FACE_FLAG_GLYPH_NAMES for a non-pure CFF also if it isn't
	CID-keyed.

	* src/cff/rules.mk (CFF_DRV_H): Add cfftypes.h.

2003-12-17  Werner Lemberg  <<EMAIL>>

	* src/sfnt/sfobjs.c (sfnt_init_face): Don't set
	FT_FACE_FLAG_GLYPH_NAMES if the font contains a version 3.0 `post'
	table.

	* docs/CHANGES: Updated.

2003-12-17  Masatake YAMATO  <<EMAIL>>

	Add new function FT_Get_CMap_Language_ID to extract the language ID
	for TrueType/sfnt fonts.

	* include/freetype/internal/services/svttcmap.h: New file.
	* include/freetype/internal/ftserv.h (FT_SERVICE_TT_CMAP_H): Add
	svttcmap.h.

	* src/sfnt/sfdriver.c: Include ttcmap0.h.
	(tt_service_get_cmap_info): New service.
	(sfnt_services): Updated.

	* src/sfnt/ttcmap0.c (tt_cmap*_get_info): New functions.
	(tt_cmap*_class_rec): Add tt_cmap*_get_info members.
	(tt_get_cmap_info): New function.
	* src/sfnt/ttcmap0.h: Include FT_SERVICE_TT_CMAP_H.
	(TT_CMap_ClassRec): New field `get_cmap_info'.
	(tt_get_cmap_info): New declaration.

	* src/base/ftobjs.c: Include FT_SERVICE_TT_CMAP_H.
	(FT_Get_CMap_Language_ID): New function implementation.
	* include/freetype/tttables.h (FT_Get_CMap_Language_ID): New
	function declaration.

2003-12-16  Werner Lemberg  <<EMAIL>>

	* src/sfnt/ttcmap.c, src/sfnt/ttcmap.h: Removed.  Obsolete.

	* include/freetype/internal/sfnt.h (SFNT_Interface): Remove
	obsolete fields `load_charmap' and `free_charmap'.
	(TT_CharMap_Load_Func, TT_CharMap_Free_Func): Removed.
	* src/sfnt/sfnt.c: Don't include ttcmap.c.
	* src/sfnt/rules.mk (SFNT_DRV_SRC): Don't include ttcmap.c.
	* src/sfnt/ttload.c: Don't include ttcmap.h.
	* src/sfnt/sfdriver.c: Don't include ttcmap.h.
	(sfnt_interface): Updated.

	* include/freetype/internal/tttypes.h (TT_TableDirRec,
	TT_CMapDirRec, TT_CMapDirEntryRec, TT_CMap0, TT_CMap2SubHeaderRec,
	TT_CMap2Rec, TT_CMap4Segment, TT_CMap4Rec, TT_CMap6,
	TT_CMapGroupRec, TT_CMap8_12Rec, TT_CMap10Rec, TT_CharMap_Func,
	TT_CharNext_Func, TT_CMapTableRec, TT_CharMapRec): Removed.
	Obsolete.
	* src/cff/cffobjs.h (CFF_CharMapRec): Removed.  Obsolete.

2003-12-15  Werner Lemberg  <<EMAIL>>

	* docs/CHANGES: Updated.

2003-12-15  Wolfgang Domröse  <<EMAIL>>

	* builds/atari/*: New directory for building FreeType 2 on Atari
	with the PureC compiler.

2003-12-12  Wolfgang Domröse  <<EMAIL>>

	* src/type1/t1gload.c (T1_Parse_Glyph_And_Get_Char_String): Add
	cast.
	* src/cff/cffdrivr.c (cff_ps_has_glyph_names): Assure that return
	value is either 0 or 1.

2003-12-12  Werner Lemberg  <<EMAIL>>

	* src/cff/cffdrivr.c (cff_get_glyph_name): Improve error message.
	(cff_get_name_index): Return if no PSNames service is available.
	(cff_ps_has_glyph_names): Handle CID-keyed fonts correctly.
	* src/cff/cfftypes.h (CFF_CharsetRec): New field `cids', used for
	CID-keyed fonts.  This is the inverse mapping of `sids'.
	* src/cff/cffload.c (cff_charset_load): New argument `invert'.
	Initialize charset->cids if `invert' is set.
	(cff_font_load): In call to cff_charset_load, set `invert' to true
	for CID-keyed fonts.
	* src/cff/cffgload.c (cff_slot_load): Handle glyph index as CID
	and map it to the real glyph index.

	* docs/CHANGES: Updated.

2003-12-11  Werner Lemberg  <<EMAIL>>

	* src/cff/cffobjs.c (cff_face_init): Don't set
	FT_FACE_FLAG_GLYPH_NAMES for CID-keyed fonts.
	Don't construct a cmap for CID-keyed fonts.

2003-12-10  Werner Lemberg  <<EMAIL>>

	Use implementation specific SID value 0xFFFF to indicate that
	a dictionary element is missing.

	* src/cff/cffload.c (cff_subfont_load): Initialize all fields
	which hold SIDs to 0xFFFF.
	(cff_index_get_sid_string): Handle SID value 0xFFFF.
	Handle case where `psnames' is zero.
	(cff_font_load): Updated.
	Don't load encoding for CID-keyed CFFs.

	* src/cff/cffobjs.c (cff_face_init): Updated.
	Don't check for PSNames module if font is CID-keyed.
	Compute style name properly (using the same algorithm as in the
	CID driver).
	Fix computation of style flags.

	* src/cff/cfftoken.h: Comment out handling of base_font_name.
	Rename `postscript' field to `embedded_postscript'
	* src/cff/cfftypes.h (CFF_FontRecDictRec): Remove `base_font_name'
	and `postscript'.

2003-12-10  Detlef Würkner  <<EMAIL>>

	* src/pcf/pcfdrivr.c (pcf_get_charset_id): New function (a clone
	of the similar BDF function).
	(pcf_service_bdf): Use it.

2003-12-09  Werner Lemberg  <<EMAIL>>

	* src/sfnt/sfobjs.c (sfnt_load_face): Set FT_FACE_FLAG_GLYPH_NAMES
	only if a `post' table is present.

2003-12-09  George Williams  <<EMAIL>>

	* src/base/ftobjs.c (load_mac_face): Recent versions of Linux
	support Mac's HFS+ file system, thus enable code to read /rsrc on
	non-Macintosh platforms also.

2003-12-08  Werner Lemberg  <<EMAIL>>

	* include/freetype/internal/psaux.h (PS_TableRec): Change type
	of `lengths' to FT_PtrDist.
	(T1_DecoderRec): Change type of `subrs_len' to FT_PtrDist.
	* include/freetype/internal/t1types.h (T1_FontRec): Change type
	of `subrs_len' and `charstrings_len' to FT_PtrDist.

	* src/base/ftobjs.c (Mac_Read_POST_Resource): Replace `junk'
	variable with better solution.
	(IsMacResource): Remove unused variable `map_len'.
	Replace `junk' variable with better solution.
	(FT_Open_Face) [!FT_MACINTOSH]: Add conditional
	FT_CONFIG_OPTION_MAC_FONTS.

2003-12-08  Wolfgang Domröse  <<EMAIL>>

	* src/autohint/ahhint.c (ah_hinter_hint_edges,
	ah_hinter_align_strong_points): Add some casts.

	* src/base/ftoutln.c (FT_OrientationExtremumRec): Change type
	of `pos' to FT_Long.

	* src/base/ftobjs.c (Mac_Read_POST_Resource,
	Mac_Read_sfnt_Resource): Change type of `len' to FT_Long.

	* src/type42/t42parse.c (t42_parse_dict): Add cast for `n_keywords'.

2003-12-07  Werner Lemberg  <<EMAIL>>

	* docs/raster.txt: New file, taken from FreeType 1 and completely
	revised.

2003-12-04  Masatake YAMATO  <<EMAIL>>

	* src/type1/t1driver.c (Get_Interface): Remove FT_UNUSED for
	t1_interface.  t1_interface is used.

2003-11-27  David Turner  <<EMAIL>>

	* src/pfr/pfrdrivr.c (pfr_get_metrics): Revert incorrect change of
	2003-11-23: For PFR fonts, metrics->x_scale and metrics->y_scale are
	the scaling values for outline units, not for metric units.

2003-11-25  Werner Lemberg  <<EMAIL>>

	* src/base/ftcalc.c, include/freetype/internal/ftcalc.h
	(FT_MulDiv_No_Round): Surround code with `#ifdef
	TT_CONFIG_OPTION_BYTECODE_INTERPRETER ... #endif'.

2003-11-23  Werner Lemberg  <<EMAIL>>

	* src/base/ftcalc.c (FT_MulDiv_No_Round): New function (32 and
	64 bit version).
	* include/freetype/internal/ftcalc.h: Updated.

	* src/truetype/ttinterp.c (TT_MULDIV_NO_ROUND): New macro.
	(TT_INT64): Removed.
	(DO_DIV): Use TT_MULDIV_NO_ROUND.

	* src/pfr/pfrdrivr.c (pfr_get_metrics): Directly use
	metrics->x_scale and metrics->y_scale.

2003-11-22  Rogier van Dalen  <<EMAIL>>

	* src/truetype/ttinterp.c (CUR_Func_move_orig): New macro.
	(Direct_Move_Orig, Direct_Move_Orig_X, Direct_Move_Orig_Y): New
	functions.  Similar to Direct_Move, Direct_Move_X, and
	Direct_Move_Y but without touching.
	(Compute_Funcs): Use new functions.

	(Round_None, Round_To_Grid, Round_To_Half_Grid, Round_Down_To_Grid,
	Round_Up_To_Grid, Round_To_Double_Grid, Round_Super,
	Round_Super_45): Fix rounding of value zero.

	(DO_DIV): Don't use TT_MULDIV.

	(Ins_SHC): This instruction actually touches the points.
	(Ins_MSIRP): Fix undocumented behaviour.

	* src/truetype/ttinterp.h (TT_ExecContextRec): Updated.

2003-11-22  Werner Lemberg  <<EMAIL>>

	* docs/VERSION.DLL, docs/CHANGES: Updated.

	* src/base/ftobjs.c (FT_Set_Char_Size): Make metrics->x_scale and
	metrics->y_scale really precise.

	(FT_Load_Glyph): Update computation of linearHoriAdvance and
	linearVertAdvance.

	* src/truetype/ttinterp.c (Update_Max): Use FT_REALLOC.

2003-11-22  David Turner  <<EMAIL>>

	* src/autofit/*: More updates.

	* include/freetype/freetype.h (FREETYPE_PATCH): Set to 8.
	* builds/unix/configure.ac (version_info): Set to 9:6:3.
	* README: Updated.

2003-11-13  John A. Boyd Jr.  <<EMAIL>>

	* src/bdf/bdfdrivr.c (bdf_interpret_style), src/pcf/pcfread.c
	(pcf_interpret_style): Replace spaces with dashes in properties
	SETWIDTH_NAME and ADD_STYLE_NAME to simplify parsing.

2003-11-11  Werner Lemberg  <<EMAIL>>

	* docs/CHANGES: Updated.

2003-11-11  John A. Boyd Jr.  <<EMAIL>>

	Handle SETWIDTH_NAME and ADD_STYLE_NAME properties for BDF and PCF
	fonts.

	* src/bdf/bdfdrivr.c (bdf_interpret_style): New auxiliary function.
	(BDF_Face_Init): Don't handle style properties but call
	bdf_interpret_style.

	* src/pcf/pcfread.c (pcf_interpret_style): New auxiliary function.
	(pcf_load_font): Don't handle style properties but call
	pcf_interpret_style.

2003-11-07  Werner Lemberg  <<EMAIL>>


	* Version 2.1.7 released.
	=========================


	* include/freetype/freetype.h (FREETYPE_PATCH): Set to 7.

	* builds/unix/ft2unix.h: Fix comments.

	* builds/unix/ftconfig.in: Synchronized with ANSI version.
	Use `#undef' in templates as recommended in the autoconf
	documentation.
	Since real `#undef' lines don't survive during configuration, use
	`/undef' instead; the postprocessing facility of the
	AC_CONFIG_HEADERS autoconf macro converts them to `#undef'.

	* builds/unix/install.mk (install): Install Unix version of
	`ftconfig.h'.

	* builds/unix/unix-cc.in (CFLAGS): Set FT_CONFIG_CONFIG_H macro
	to include the correct `ftconfig.h' file.

	* builds/unix/ft-munmap.m4 (FT_MUNMAP_DECL): Removed.
	(FT_MUNMAP_PARAM): Updated syntax to autoconf 2.59.

	* builds/unix/freetype2.m4: Updated syntax to autoconf 2.59.

	* builds/unix/configure.ac: Use AC_CONFIG_HEADERS instead of
	AC_CONFIG_HEADER to create ftconfig.h, and use second argument
	to replace `/undef' with `#undef'.
	Don't use FT_MUNMAP_DECL but AC_CHECK_DECLS to check for munmap.
	Use AS_HELP_STRING in AC_ARG_WITH.
	Update syntax to autoconf 2.59.

	* builds/unix/ltmain.sh: Regenerated with `libtoolize --force
	--copy' from libtool 1.5.
	* builds/unix/aclocal.m4: Regenerated with `aclocal -I .' from
	automake 1.7.8.
	* builds/unix/configure: Regenerated with autoconf 2.59.
	* builds/unix/config.guess, builds/unix/config.sub: Updated from
	`config' CVS module at subversions.gnu.org
	* builds/unix/install-sh, builds/unix/mkinstalldirs: Updated from
	`texinfo' CVS module at subversions.gnu.org.

	* builds/vms/ftconfig.h: Synchronized with ANSI version.

	* docs/CUSTOMIZE: Fix documentation error.
	* docs/CHANGES, docs/VERSION.DLL, docs/release: Updated.

	* builds/freetype.mk (refdoc): Updated --title.

2003-11-07  David Turner  <<EMAIL>>


	* Version 2.1.6 released.
	=========================


	* install: Removed.  Obsolete.

2003-11-04  Werner Lemberg  <<EMAIL>>

	* src/sfnt/sfdriver.c: Include FT_SERVICE_SFNT_H.
	(sfnt_service_sfnt_table): New service.
	(sfnt_services): Updated.

	* docs/license.txt: Reworded.

2003-11-03  Werner Lemberg  <<EMAIL>>

	* include/freetype/*: Add a guard to all public header files which
	load FT_FREETYPE_H to reject freetype.h from FreeType 1.

2003-11-02  Patrick Welche  <<EMAIL>>

	* builds/unix/freetype2.m4, builds/unix/ft-munmap.m4: Protect
	first argument of AC_DEFUN with brackets to avoid possible
	expansion.

2003-11-02  Werner Lemberg  <<EMAIL>>

	* include/freetype/cache/ftcglyph.h: Don't include stddef.h.

	* include/freetype/freetype.h: Fix check for ft2build.h.

2003-11-01  Werner Lemberg  <<EMAIL>>

	* include/freetype/freetype.h: Check that ft2build.h has been
	loaded first.

	* src/base/fttype1.c (FT_Get_PS_Font_Info): Fix incorrectly applied
	patch.

2003-10-31  Detlef Würkner  <<EMAIL>>

	* src/base/fttype1.c (FT_Get_PS_Font_Info, FT_Has_PS_Glyph_Names):
	Fix parameter order in calls to FT_FACE_FIND_SERVICE.

2003-10-31  Werner Lemberg  <<EMAIL>>

	* include/freetype/internal/ftserv.h
	(FT_SERVICE_POSTSCRIPT_NAMES_H): Removed.  Unused.

	* src/type42/t42drivr.c (t42_services): Updated.

2003-10-29  David Turner  <<EMAIL>>

	* include/freetype/internal/bdftypes.h: Removed.  Obsolete.
	* src/base/ftbdf.c: Updated.

	* include/freetype/internal/cfftypes.h: Moved to...
	* src/cff/cfftypes.h: This place since no other module needs to
	know about those types.

	* include/freetype/internal/t42types.h: Moved to...
	* src/type42/t42types.h: This place since no other module needs to
	know about those types.

	* include/freetype/internal/services/svbdf.h: Include FT_BDF_H.

	* include/freetype/internal/services/svpsname.h: Renamed to...
	* include/freetype/internal/services/svpscmap.h: This.
	Updated `FT_Service_PsNames' -> `FT_Service_PsCMaps' and
	`POSTSCRIPT_NAMES' -> `POSTSCRIPT_CMAPS' everywhere.

	* include/freetype/internal/services/svpsinfo.h: New file, providing
	PostScript info service.

	* include/freetype/internal/ftserv.h (FT_SERVICE_POSTSCRIPT_CMAPS_H,
	FT_SERVICE_POSTSCRIPT_INFO_H): New macros for svpscmap.h and
	svpsinfo.h.
	* include/freetype/internal/internal.h (FT_INTERNAL_TYPE42_TYPES_H,
	FT_INTERNAL_CFF_TYPES_H, FT_INTERNAL_BDF_TYPES_H): Removed.

	* src/base/fttype1.c: Don't include FT_INTERNAL_TYPE1_TYPES_H and
	FT_INTERNAL_TYPE42_TYPES_H but FT_INTERNAL_SERVICE_H and
	FT_SERVICE_POSTSCRIPT_INFO_H.
	(FT_Get_PS_Font_Info, FT_Has_PS_Glyph_Names): Use new
	POSTSCRIPT_INFO service.

	* src/cff/cffdrivr.c: Include FT_SERVICE_POSTSCRIPT_INFO_H.
	(cff_ps_has_glyph_names): New function.
	(cff_service_ps_info): New service.
	(cff_services): Updated.

	* src/cff/cffload.h, src/cff/cffobjs.h, src/cff/cffparse.h: Don't
	include FT_INTERNAL_CFF_TYPES_H but cfftypes.h directly.

	* src/cid/cidriver.c: Include FT_SERVICE_POSTSCRIPT_INFO_H.
	(cid_ps_get_font_info): New function.
	(cid_service_ps_info): New service.
	(cid_services): Updated.

	* src/type1/t1driver.c: Include FT_SERVICE_POSTSCRIPT_INFO_H.
	(t1_ps_get_font_info, t1_ps_has_glyph_names): New functions.
	(t1_service_ps_info): New service.
	(t1_services): Updated.

	* src/type42/t42drivr.c: Include FT_SERVICE_POSTSCRIPT_INFO_H.
	(t42_ps_get_font_info, t42_ps_has_glyph_names): New functions.
	(t42_service_ps_info): New service.

	* src/type42/t42objs.h: Don't include FT_INTERNAL_TYPE42_TYPES_H
	but t42types.h directly.

	* src/psnames/psmodule.c (psnames_interface, psnames_services):
	Renamed to...
	(pscmaps_interface, pscmaps_services): This.
	Updated all users.


	* src/gzip/infblock.c (inflate_blocks): Remove compiler warning.

2003-10-22  Werner Lemberg  <<EMAIL>>

	* src/type1/t1load.c (parse_encoding): Handle `/Encoding [ ... ]'.

	* src/type1/t1parse.c (T1_Get_Private_Dict): Test whether `eexec'
	is real.

	* src/type42/t42parse.c (t42_parse_encoding): Improve boundary
	checking while parsing.

	* docs/CHANGES: Updated.

2003-10-21  Josselin Mouette  <<EMAIL>>

	* include/freetype/internal/t1types.h (T1_FontRec): `paint_type'
	and `stroke_width' aren't pointers.

	* src/type42/t42objs.c (T42_Face_Done), src/type1/t1objs.c
	(T1_Face_Done): Don't free `paint_type' and `stroke_width'.

2003-10-20  Graham Asher  <<EMAIL>>

	* src/winfonts/winfnt.c (fnt_cmap_class): Fix position of `const'.

2003-10-19  Werner Lemberg  <<EMAIL>>

	* src/autohint/ahhint.c (ah_hinter_load_glyph): Patch from
	2003-08-18 introduced a severe bug (FT_Render_Glyph was called
	twice under some circumstances, causing strange results).  This
	is fixed now by clearing the FT_LOAD_RENDER bit of `load_flags'.

	* src/base/ftpfr.c (FT_Get_PFR_Metrics): Initialize `error'.
	* src/psaux/psobjs.c (ps_tobytes): Initialize `n'.
	* src/type42/t42parse.c (t42_parse_sfnts): Initialize `string_size'.

2003-10-16  Werner Lemberg  <<EMAIL>>

	Completely revised Type 42 parser.  It now handles both fonts
	produced with ttftot42 (tested version 0.3.1) and
	TrueTypeToType42.ps (tested version May 2001; it is necessary to
	fix the broken header comment to be `%!PS-TrueTypeFont...').

	* src/type42/t42objs.c (T42_GlyphSlot_Load): Change fourth
	parameter to `FT_UInt'.
	* src/type42/t42objs.h: Updated.

	* src/type42/t42parse.h (T42_ParserRec): Change type of `in_memory'
	to FT_Bool.
	(T42_Loader): Change type of `num_chars' and `num_glyphs' to
	FT_UInt.
	Add `swap_table' element.
	* src/type42/t42parse.c (T42_KEYWORD_COUNT, T1_ToFixed,
	T1_ToCoordArray, T1_ToTokenArray): Removed.
	(T1_ToBytes): New macro.
	(t42_is_alpha, t42_hexval): Removed.
	(t42_is_space): Handle `\0'.
	(t42_parse_encoding): Updated to use new PostScript parser routines
	from psaux.
	Handle `/Encoding [ ... ]' also.
	(T42_Load_Status): New enumeration.
	(t42_parse_sfnts): Updated to use new PostScript parser routines
	from psaux.
	(t42_parse_charstrings): Updated to use new PostScript parser
	routines from psaux.
	Handle `/CharStrings << ... >>' also.
	Don't expect that /.notdef is the first element in dictionary.  Copy
	code from type1 module to handle this.
	(t42_parse_dict): Updated to use new PostScript parser routines
	from psaux.
	Remove code for synthetic fonts (which can't occur in Type 42
	fonts).
	(t42_loader_done): Release `swap_table'.

	* src/psaux/psobjs.c (skip_string): Increase `cur' properly.

	* src/type1/t1load.c (parse_charstrings): Make test for `.notdef'
	faster.

2003-10-15  Graham Asher  <<EMAIL>>

	* src/autohint/ahglobal.c (blue_chars), src/winfonts/winfnt.c
	(fnt_cmap_class_rec, fnt_cmap_class), src/bdf/bdflib.c (empty,
	_num_bdf_properties), src/gzip/infutil.c (inflate_mask),
	src/gzip/inffixed.h (fixed_bl, fixed_bd, fixed_tl, fixed_td),
	src/gzip/inftrees.h (inflate_trees_fixed), src/gzip/inftrees.c
	(inflate_trees_fixed): Decorate with more `const' to avoid
	writable global variables which are disallowed on ARM.

2003-10-08  Werner Lemberg  <<EMAIL>>

	* src/type1/t1load.c (parse_font_matrix, parse_charstrings): Remove
	code specially for synthetic fonts; this is handled elsewhere.
	(parse_encoding): Remove code specially for synthetic fonts; this is
	handled elsewhere.
	Improve boundary checking while parsing.
	(parse_dict): Improve boundary checking while parsing.
	Use ft_memcmp to simplify code.

2003-10-07  Werner Lemberg  <<EMAIL>>

	* src/type1/t1load.c (parse_subrs, parse_dict): Handle synthetic
	fonts properly.
	(parse_charstrings): Copy correct number of characters into
	`name_table'.

2003-10-06  Werner Lemberg  <<EMAIL>>

	Heavy modification of the PS parser to handle comments and strings
	correctly.  This doesn't slow down the loading of PS fonts
	significantly since charstrings aren't affected.

	* include/freetype/config/ftstdlib.h (ft_xdigit): Renamed to...
	(ft_isxdigit): This.  Updated all callers.
	(ft_isdigit): New alias to `isdigit'.

	* include/freetype/internal/psaux.h (PS_Parser_FuncsRec): Renamed
	`skip_alpha' to `skip_PS_token'.
	Add parameter to `to_bytes' and change some argument types.

	* src/psaux/psauxmod.c (ps_parser_funcs): Updated.
	* src/psaux/psobjs.c (ft_char_table): New array to map character
	codes (ASCII and EBCDIC) of digits to numbers.
	(OP): New auxiliary macro holding either `>=' or `<' depending on
	the character encoding.
	(skip_comment): New function.
	(skip_spaces): Use it.
	(skip_alpha): Removed.
	(skip_literal_string, skip_string): New functions.
	(ps_parser_skip_PS_token): New function.  This is a better
	replacement of...
	(ps_parser_skip_alpha): Removed.
	(ps_parser_to_token, ps_parser_to_token_array): Updated.
	(T1Radix): Rewritten, using `ft_char_table'.
	(t1_toint): Renamed to...
	(ps_toint): This.  Update all callers.
	Use `ft_char_table'.
	(ps_tobytes): Add parameter to handle delimiters and change some
	argument types.
	Use `ft_char_table'.
	(t1_tofixed): Renamed to...
	(ps_tofixed): This.  Update all callers.
	Use `ft_char_table'.
	(t1_tocoordarray): Renamed and updated to...
	(ps_tocoordarray): This.  Update all callers.
	(t1_tofixedarray): Renamed and updated to...
	(ps_tofixedarray): This.  Update all callers.
	(t1_tobool): Renamed to...
	(ps_tobool): This.  Update all callers.
	(ps_parser_load_field): Updated.
	(ps_parser_load_field_table): Use `T1_MAX_TABLE_ELEMENTS'
	everywhere.
	(ps_parser_to_int, ps_parser_to_fixed, ps_parser_to_coord_array,
	ps_parser_to_fixed_array): Skip spaces.  Updated.
	(ps_parser_to_bytes): Add parameter to handle delimiters and change
	some argument types.  Updated.
	* src/psaux/psobjs.h: Updated.

	* src/cid/cidload.c (cid_parse_dict): Updated.
	* src/cid/cidparse.c (cid_parser_new): Check whether the `StartData'
	token was really found.
	* src/cid/cidparse.h (cid_parser_skip_alpha): Updated and renamed
	to...
	(cid_parser_skip_PS_token): This.

	* src/type1/t1parse.h (T1_ParserRec): Use `FT_Bool' for boolean
	fields.
	(T1_Skip_Alpha): Replaced with...
	(T1_Skip_PS_Token): This new macro.
	* src/type1/t1parse.c (hexa_value): Removed.
	(T1_Get_Private_Dict): Use `ft_isxdigit' and
	`psaux->ps_parser_funcs_to_bytes' for handling ASCII hexadecimal
	encoding.
	After decrypting, replace the four random bytes at the beginning
	with whitespace.
	* src/type1/t1load.c (t1_allocate_blend): Use proper error values.
	(parser_blend_design_positions, parse_blend_design_map,
	parse_weight_vector): Updated.
	(is_space): Handle `\f' also.
	(is_name_char): Removed.
	(read_binary_data): Updated.
	(parse_encoding): Use `ft_isdigit'.
	Updated.
	(parse_subrs): Updated.
	(TABLE_EXTEND): New macro.
	(parse_charstrings): Updated.
	Provide a workaround for buggy fonts which have more entries in the
	/CharStrings dictionary then expected; the function now adds some
	slots and skips entries which still exceed the new limit.
	(parse_dict): Updated.
	Terminate on the token `closefile'.

	* src/type42/t42parse.c (T1_Skip_Alpha): Replaced with...
	(T1_Skip_PS_Token): This new macro.  Updated all callers.
	(t42_parse_encoding): Use `ft_isdigit'.


	* src/base/ftmm.c (ft_face_get_mm_service): Return FT_Err_Ok if
	success.

2003-10-05  Werner Lemberg  <<EMAIL>>

	* include/freetype/ftmodule.h: Renamed to...
	* include/freetype/ftmodapi.h: This to avoid duplicate file names.
	* include/freetype/config/ftheader.h (FT_MODULE_H): Updated.

2003-10-04  Werner Lemberg  <<EMAIL>>

	* src/base/ftoutln.c (FT_OrientationExtremumRec,
	FT_Outline_Get_Orientation): Trivial typo fixes to make it compile.

2003-10-02  Markus F.X.J. Oberhumer  <<EMAIL>>

	* src/winfonts/winfnt.c (FT_WinFNT_HeaderRec): `color_table_offset'
	has four bytes, not two.
	Fix all users.
	(fnt_font_load, FNT_Load_Glyph): Add more font validity tests.

2003-10-01  David Turner  <<EMAIL>>

	* src/autofit/*: Adding first source files of the new multi-script
	`auto-fitter'.

	* include/freetype/ftoutln.h (FT_Orientation): New enumeration.
	(FT_Outline_Get_Orientation): New declaration.

	* src/base/ftoutln.c (FT_OrientationExtremumRec): New structure.
	(ft_orientation_extremum_compute): New auxiliary function.
	(FT_Outline_Get_Orientation): New function to compute the fill
	orientation of a given glyph outline.

	* include/freetype/internal/ftserv.h (FT_FACE_LOOKUP_SERVICE): Fixed
	trivial bug which could crash the font engine when a cached service
	pointer was retrieved.

2003-09-30  Werner Lemberg  <<EMAIL>>

	* src/cid/cidload.c (cid_parse_dict): Skip token if no keyword is
	found.

	* src/type1/t1parse.c (IS_T1_WHITESPACE, IS_T1_LINESPACE,
	IS_T1_SPACE): Removed.
	(PFB_Tag): Removed.
	(read_pfb_tag): Don't use PFB_Tag.

	* src/type42/t42parse.c (t42_is_space): Handle `\f' also.
	(t42_parse_encoding): Handle synthetic fonts.

2003-09-29  Werner Lemberg  <<EMAIL>>

	* include/freetype/internal/t1types.h: Don't include
	FT_INTERNAL_OBJECTS_H but FT_INTERNAL_SERVICE_H.
	* src/truetype/ttobjs.c: Don't include
	FT_SERVICE_POSTSCRIPT_NAMES_H.

2003-09-29  David Turner  <<EMAIL>>

	Added new service to handle glyph name dictionaries, replacing the
	old internal header named `psnames.h' by `services/svpsname.h'.
	Note that this is different from `services/svpostnm.h' which only
	handles the retrieval of PostScript font names for a given face.
	(Should we merge these two services into a single header?)

	* include/freetype/internal/psnames.h: Removed.  Most of its
	contents is moved to...
	* include/freetype/internal/services/svpsname.h: New file.

	* include/freetype/internal/services/svpostnm.h
	(FT_SERVICE_ID_POSTSCRIPT_NAME): Replaced with...
	(FT_SERVICE_ID_POSTSCRIPT_FONT_NAME): New macro.
	(PsName): Service named changed to...
	(PsFontName): This.
	Updated `FT_Service_PsName' -> `FT_Service_PsFontName' and
	`POSTSCRIPT_NAME' -> `POSTSCRIPT_FONT_NAME' everywhere.

	* include/freetype/internal/internal.h
	(FT_INTERNAL_POSTSCRIPT_NAMES_H): Removed.
	* include/freetype/internal/psaux.h: Include
	FT_SERVICE_POSTSCRIPT_NAMES_H.
	(T1_DecoderRec): Updated type of `psnames'.
	* include/freetype/internal/t1types.h: Don't include
	FT_INTERNAL_POSTSCRIPT_NAMES_H but FT_SERVICE_POSTSCRIPT_NAMES_H.
	Include FT_INTERNAL_OBJECTS_H.
	* include/freetype/internal/t42types.h: Don't include
	FT_INTERNAL_POSTSCRIPT_NAMES_H.
	* include/freetype/internal/tttypes.h (TT_FaceRec): Updated.

	* include/freetype/internal/ftserv.h (FT_FACE_FIND_SERVICE): Changed
	order of parameters.  All callers updated.
	(FT_FACE_FIND_GLOBAL_SERVICE): New macro to look up a service
	globally, checking all modules.
	(FT_ServiceCacheRec): Updated.
	(FT_SERVICE_POSTSCRIPT_NAMES_H): New macro for accessing
	`svpsname.h'.

	* include/freetype/internal/ftobjs.h, src/base/ftobjs.c
	(ft_module_get_service): New function.

	* src/cff/cffdrivr.c: Don't include FT_INTERNAL_POSTSCRIPT_NAMES_H
	but FT_SERVICE_POSTSCRIPT_NAMES_H.
	(cff_get_glyph_name, cff_get_name_index): Use new POSTSCRIPT_NAMES
	service.
	* src/cff/cffcmap.c (cff_cmap_unicode_init): Updated.
	* src/cff/cffload.c, src/cff/cffload.h:  Don't include
	FT_INTERNAL_POSTSCRIPT_NAMES_H but FT_SERVICE_POSTSCRIPT_NAMES_H.
	(cff_index_get_sid_string): Updated.
	* src/cff/cffobjs.c:  Don't include FT_INTERNAL_POSTSCRIPT_NAMES_H
	but FT_SERVICE_POSTSCRIPT_NAMES_H.
	(cff_face_init): Use new POSTSCRIPT_NAMES service.
	* src/cff/cffobjs.h:  Don't include FT_INTERNAL_POSTSCRIPT_NAMES_H
	but FT_SERVICE_POSTSCRIPT_NAMES_H.

	* src/cid/cidobjs.c:  Don't include FT_INTERNAL_POSTSCRIPT_NAMES_H
	but FT_SERVICE_POSTSCRIPT_NAMES_H.
	(cid_face_init): Use new POSTSCRIPT_NAMES service.
	* src/cid/cidriver.c: Don't include FT_INTERNAL_POSTSCRIPT_NAMES_H.

	* src/psaux/t1cmap.c (t1_cmap_std_init, t1_cmap_unicode_init): Use
	new POSTSCRIPT_NAMES service.
	* src/psaux/t1decode.h (t1_lookup_glyph_by_stdcharcode,
	t1_decode_init): Use new POSTSCRIPT_NAMES service.
	* src/psaux/t1cmap.h, src/psaux/t1decode.h: Don't include
	FT_INTERNAL_POSTSCRIPT_NAMES_H.

	* src/psnames/psmodule.c:  Don't include
	FT_INTERNAL_POSTSCRIPT_NAMES_H but FT_SERVICE_POSTSCRIPT_NAMES_H.
	(ps_build_unicode_table): Renamed to...
	(ps_unicodes_init): This.
	(ps_lookup_unicode): Renamed to...
	(ps_unicodes_char_index): This.
	(ps_next_unicode): Renamed to...
	(ps_unicodes_char_next): This.
	(psnames_interface): Updated.
	(psnames_services): New services list.
	(psnames_get_service): New function.
	(psnames_module_class): Updated.

	* src/sfnt/sfobjs.c: Don't include FT_INTERNAL_POSTSCRIPT_NAMES_H
	but FT_SERVICE_POSTSCRIPT_NAMES_H.
	(sfnt_init_face): Use new POSTSCRIPT_NAMES service.
	* src/sfnt/ttpost.c: Don't include FT_INTERNAL_POSTSCRIPT_NAMES_H
	but FT_SERVICE_POSTSCRIPT_NAMES_H.
	(tt_face_get_ps_name): Updated.

	* src/truetype/ttobjs.c:  Don't include
	FT_INTERNAL_POSTSCRIPT_NAMES_H but FT_SERVICE_POSTSCRIPT_NAMES_H.

	* src/type1/t1driver.c:  Don't include
	FT_INTERNAL_POSTSCRIPT_NAMES_H but FT_SERVICE_POSTSCRIPT_NAMES_H.
	* src/type1/t1objs.c:  Don't include
	FT_INTERNAL_POSTSCRIPT_NAMES_H but FT_SERVICE_POSTSCRIPT_NAMES_H.
	(T1_Face_Init): Use new POSTSCRIPT_NAMES service.

	* src/type42/t42drivr.c (t42_get_ps_name): Renamed to...
	(t42_get_ps_font_name): This.
	(t42_service_ps_name): Renamed to...
	(t42_service_ps_font_name): This.
	(t42_services): Updated.
	* src/type42/t42objs.c (T42_Face_Init): Use new POSTSCRIPT_NAMES
	service.
	* src/type42/t42objs.h:  Don't include
	FT_INTERNAL_POSTSCRIPT_NAMES_H but FT_SERVICE_POSTSCRIPT_NAMES_H.


	* src/base/ftglyph.c (FT_Get_Glyph): Don't access `slot' before
	testing its validity.  Reported by Henry Maddocks
	<<EMAIL>>.

2003-09-21  Werner Lemberg  <<EMAIL>>

	* include/freetype/internal/ftserv.h (FT_FACE_FIND_SERVICE):
	Fix compilation warning (s/pptr/Pptr/).

	* include/freetype/internal/internal.h (FT_INTERNAL_PFR_H,
	FT_INTERNAL_FNT_TYPES_H): Removed.

2003-09-21  David Turner  <<EMAIL>>

	Migrating the PFR and WINFNT drivers to the new service-based
	internal API.

	* include/freetype/internal/fnttypes.h: Removed.  Most of its data
	are moved to winfnt.h and...
	* include/freetype/internal/services/svwinfnt.h: New file.

	* include/freetype/internal/pfr.h: Removed.  Most of its data are
	moved to...
	* include/freetype/internal/services/svpfr.h: New file.

	* include/freetype/internal/ftserv.h (FT_FACE_FIND_SERVICE,
	FT_FACE_LOOKUP_SERVICE): Simplify fix of 2003-09-16 by removing
	pointer type argument.
	Updated all callers.
	Update macro names of services header files.

	* src/base/ftobjs.c (FT_Get_Name_Index): Simplified code.

	* src/base/ftpfr.c: Include FT_SERVICE_PFR_H instead of
	FT_INTERNAL_PFR_H.
	(ft_pfr_check, FT_Get_PFR_Metrics, FT_Get_PFR_Kerning,
	FT_Get_PFR_Advance): Use services provided in `PFR_METRICS'.

	* src/base/ftwinfnt.c: Include FT_SERVICE_WINFNT_H instead of
	FT_INTERNAL_FNT_TYPES_H.
	(FT_Get_WinFNT_Header): Use service provided in `WINFNT'.

	* src/pfr/pfrdrivr.c: Include FT_SERVICE_PFR_H and
	FT_SERVICE_XFREE86_NAME_H instead of FT_INTERNAL_PFR_H.
	(pfr_service_bdf): Updated.
	(pfr_services): New services list.
	(pfr_get_service): New function.
	(pfr_driver_class): Updated.

	* src/winfonts/winfnt.c: Include FT_SERVICE_WINFNT_H and
	FT_SERVICE_XFREE86_NAME_H instead of FT_INTERNAL_FNT_TYPES_H.
	(winfnt_get_header, winfnt_get_service): New functions.
	(winfnt_service_rec): New structure providing WINFNT services.
	(winfnt_services): New services list.
	(winfnt_driver_class): Updated.
	* src/winfonts/winfnt.h: Add most of the removed fnttypes.h data.

	* src/sfnt/sfdriver.c (sfnt_service_ps_name): Fix typo.

	* src/type1/t1driver.c (t1_service_ps_name): Fix typo.

	* src/cff/cffobjs.c, src/cid/cidobjs.c, src/pfr/pfrsbit.c,
	src/psaux/psobjs.c, src/sfnt/sfobjs.c, src/truetype/ttobjs.c,
	src/type1/t1objs.c, src/type42/t42objs.c: Removing various compiler
	warnings.

2003-09-19  David Bevan  <<EMAIL>>

	* src/type1/t1parse.c (pfb_tag_fields): Removed.
	(read_pfb_tag): Fix code so that it doesn't fail on end-of-file
	indicator (0x8003).
	* docs/CHANGES: Updated.

2003-09-16  Werner Lemberg  <<EMAIL>>

	* include/freetype/internal/ftserv.h (FT_FACE_FIND_SERVICE,
	FT_FACE_LOOKUP_SERVICE): Add parameter to pass pointer type.
	Ugly, I know, but this is needed for compilation with C++ --
	maybe someone knows a better solution?
	Updated all callers.

	* src/base/ftobjs.c (FT_Get_Name_Index, FT_Get_Glyph_Name): Remove
	C++ compiler warnings.

	* src/base/ftbdf.c (FT_Get_BDF_Charset_ID, FT_Get_BDF_Property):
	Fix order of arguments passed to FT_FACE_FIND_SERVICE.

2003-09-15  Werner Lemberg  <<EMAIL>>

	Avoid header files with identical names.

	* include/freetype/internal/services/bdf.h: Renamed to...
	* include/freetype/internal/services/svbdf.h: This.
	Add copyright notice.
	* include/freetype/internal/services/glyfdict.h: Renamed to...
	* include/freetype/internal/services/svgldict.h: This.
	Add copyright notice.
	* include/freetype/internal/services/multmast.h: Renamed to...
	* include/freetype/internal/services/svmm.h: This.
	Add copyright notice.
	Add FT_BEGIN_HEADER and FT_END_HEADER.
	* include/freetype/internal/services/sfnt.h: Renamed to...
	* include/freetype/internal/services/svsfnt.h: This.
	Add copyright notice.
	* include/freetype/internal/services/postname.h: Renamed to...
	* include/freetype/internal/services/svpostnm.h: This.
	Add copyright notice.
	* include/freetype/internal/services/xf86name.h: Renamed to...
	* include/freetype/internal/services/svxf86nm.h: This.
	Add copyright notice.

	* include/freetype/internal/ftserv.h: Add FT_BEGIN_HEADER and
	FT_END_HEADER.
	Add copyright notice.
	Update macro names of services header files.

	* builds/freetype.mk (SERVICES_DIR): New variable.
	(BASE_H): Add services header files.

2003-09-11  Werner Lemberg  <<EMAIL>>

	* builds/toplevel.mk (distclean): Remove `builds/unix/freetype2.pc'.

	* src/cff/cffdrivr.c: Don't load headers twice.

	* include/freetype/internal/ftserv.h (FT_SERVICE_SFNT_H): New macro.
	* src/base/ftobjs.c: Include FT_SERVICE_SFNT_H.

	* src/cff/cffcmap.c: Include `cfferrs.h'.
	* src/pfr/pfrdrivr.c: Include `pfrerror.h'.
	* src/sfnt/sfdriver.c: Include `sferrors.h'.
	* src/psaux/psobjs.h: Add declaration for `ps_parser_to_bytes'.

2003-09-11  David Turner  <<EMAIL>>

	Introducing the concept of `module services'.  This is the first
	step towards a massive simplification of the engine's internals, in
	order to get rid of various numbers of hacks.

	Note that these changes will break source & binary compatibility for
	authors of external font drivers.

	* include/freetype/config/ftconfig.h (FT_BEGIN_STMNT, FT_END_STMNT,
	FT_DUMMY_STMNT): New macros.

	* include/freetype/internal/ftserv.h: New file, containing the new
	structures and macros to provide `services'.

	* include/freetype/internal/internal.h (FT_INTERNAL_EXTENSION_H,
	FT_INTERNAL_EXTEND_H, FT_INTERNAL_HASH_H, FT_INTERNAL_OBJECT_H):
	Removed, obsolete.
	(FT_INTERNAL_SERVICE_H): New macro for `ftserv.h'.

	* include/freetype/internal/services/bdf.h,
	include/freetype/internal/services/glyfdict.h,
	include/freetype/internal/services/postname.h,
	include/freetype/internal/services/xf86name.h: New files.

	* include/freetype/ftmm.h (FT_Get_MM_Func, FT_Set_MM_Design_Func,
	FT_Set_MM_Blend_Func): Function pointers moved (in modified form)
	to...
	* include/freetype/internal/services/multmast.h: New file.

	* include/freetype/internal/sfnt.h (SFNT_Interface): `get_interface'
	is now of type `FT_Module_Requester'.
	(SFNT_Get_Interface_Func, SFNT_Load_Table_Func): Function pointers
	moved (in modified form) to...
	* include/freetype/internal/services/sfnt.h: New file.

	* include/freetype/tttables.h (FT_Get_Sfnt_Table_Func): Function
	pointer moved (in modified form) to `services/sfnt.h'.

	* include/freetype/ftmodule.h (FT_Module_Interface): Make it a
	a typedef to `FT_Pointer'.

	* include/freetype/internal/tttypes.h (TT_FaceRec): Add
	`postscript_name'.
	* include/freetype/internal/ftobjs.h (FT_Face_InternalRec): Remove
	`postscript_name'.
	Add `services' element.
	(FT_LibraryRec): Remove `meta_class'.

	* src/base/ftbdf.c: Include FT_SERVICE_BDF_H.
	(test_font_type): Removed.
	(FT_Get_BDF_Charset_ID, FT_Get_BDF_Property): Use services
	provided in `FT_SERVICE_ID_BDF'.

	* src/base/ftmm.c: Include FT_SERVICE_MULTIPLE_MASTERS_H.
	(ft_face_get_mm_service): New auxiliary function to get services
	from `FT_SERVICE_ID_MULTI_MASTERS'.
	(FT_Get_Multi_Master, FT_Set_MM_Design_Coordinates,
	FT_Set_MM_Blend_Coordinates): Use `ft_face_get_mm_service'.

	* src/base/ftobjs.c: Include FT_SERVICE_POSTSCRIPT_NAME_H and
	FT_SERVICE_GLYPH_DICT_H.
	(ft_service_list_lookup): New function to get a specific service.
	(destroy_face): Updated.
	(Mac_Read_POST_Resource): Simplify some code.
	(IsMacResource): Fix warnings.
	(FT_Get_Name_Index, FT_Get_Glyph_Name): Use services provided in
	`FT_SERVICE_ID_GLYPH_DICT'.
	(FT_Get_Postscript_Name): Use service provided in
	`FT_SERVICE_ID_POSTSCRIPT_NAME'.
	(FT_Get_Sfnt_Table, FT_Load_Sfnt_Table): Use services provided in
	`FT_SERVICE_ID_SFNT_TABLE'.

	* src/base/ftxf86.c: Include FT_SERVICE_XFREE86_NAME_H.
	(FT_Get_X11_Font_Format): Use service provided in
	`FT_SERVICE_ID_XF86_NAME'.

	* src/bdf/bdfdrivr.c: Include FT_SERVICE_BDF_H and
	FT_SERVICE_XFREE86_NAME_H.
	(bdf_get_charset_id): New function.
	(bdf_service_bdf): New structure providing BDF services.
	(bdf_services): New services list.
	(bdf_driver_requester): Use `ft_service_list_lookup'.

	* src/cff/cffdrivr.c: Include FT_SERVICE_XFREE86_NAME_H and
	FT_SERVICE_GLYPH_DICT_H.
	(cff_service_glyph_dict): New structure providing CFF services.
	(cff_services): New services list.
	(cff_get_interface): Use `ft_service_list_lookup'.

	* src/cid/cidriver.c: Include FT_SERVICE_POSTSCRIPT_NAME_H and
	FT_SERVICE_XFREE86_NAME_H.
	(cid_service_ps_name): New structure providing CID services.
	(cid_services): New services list.
	(cid_get_interface): Use `ft_service_list_lookup'.

	* src/pcf/pcfdrivr.c: Include FT_SERVICE_BDF_H and
	FT_SERVICE_XFREE86_NAME_H.
	(pcf_service_bdf): New structure providing PCF services.
	(pcf_services): New services list.
	(pcf_driver_requester): Use `ft_service_list_lookup'.

	* src/sfnt/sfdriver.c: Include FT_SERVICE_GLYPH_DICT_H and
	FT_SERVICE_POSTSCRIPT_NAME_H.
	(get_sfnt_glyph_name): Renamed to...
	(sfnt_get_glyph_name): This.
	(get_sfnt_postscript_name): Renamed to...
	(sfnt_get_ps_name): This.
	Updated.
	(sfnt_service_glyph_dict, sfnt_service_ps_name): New structures
	providing services.
	(sfnt_services): New services list.
	(sfnt_get_interface): Use `ft_service_list_lookup'.

	* src/truetype/ttdriver.c: Include FT_SERVICE_XFREE86_NAME_H.
	(tt_services): New services list.
	(tt_get_interface): Use `ft_service_list_lookup'.

	* src/type1/t1driver.c: Include FT_SERVICE_MULTIPLE_MASTERS_H,
	FT_SERVICE_GLYPH_DICT_H, FT_SERVICE_XFREE86_NAME_H, and
	FT_SERVICE_POSTSCRIPT_NAME_H.
	(t1_service_glyph_dict, t1_service_ps_name,
	t1_service_multi_masters): New structures providing Type 1 services.
	(t1_services): New services list.
	(Get_Interface): Use `ft_service_list_lookup'.

	* src/type42/t42drivr.c: Include FT_SERVICE_XFREE86_NAME_H,
	FT_SERVICE_GLYPH_DICT_H, and FT_SERVICE_POSTSCRIPT_NAME_H.
	(t42_service_glyph_dict, t42_service_ps_name): New structures
	providing Type 42 services.
	(t42_services): New services list.
	(T42_Get_Interface): Use `ft_service_list_lookup'.


	* README, docs/CHANGES: Updating version numbers for 2.1.6, and
	removing obsolete warnings in the documentation.
	* include/freetype/freetype.h (FREETYPE_PATCH): Set to 6.
	* builds/unix/configure.ac (version_info): Set to 9:5:3.
	* builds/unix/configure: Regenerated.

	* include/freetype/internal/ftcore.h,
	include/freetype/internal/ftexcept.h,
	include/freetype/internal/fthash.h,
	include/freetype/internal/ftobject.h: Removed.  Obsolete.

2003-09-09  David Turner  <<EMAIL>>

	Fixing PFR kerning support.  The tables within the font file contain
	(charcode,charcode) kerning pairs, we need to convert them to
	(gindex,gindex).

	* src/base/ftpfr.c (ft_pfr_check): Fix serious typo.
	* src/pfr/pfrload.c: Remove dead code.
	(pfr_get_gindex, pfr_compare_kern_pairs, pfr_sort_kerning_pairs):
	New functions.
	(pfr_phy_font_done): Free `kern_pairs'.
	(pfr_phy_font_load): Call `pfr_sort_kerning_pairs'.
	* src/pfr/pfrobjs.c (pfr_face_get_kerning): Fix kerning extraction.
	* src/pfr/pfrtypes.h (PFR_KERN_PAIR_INDEX): New macro.
	(PFR_KernPairRec): Make `kerning' an FT_Int.
	(PFR_PhyFontRec): New element `kern_pairs'.
	(PFR_KernFlags): Values of PFR_KERN_2BYTE_CHAR and
	PFR_KERN_2BYTE_ADJ were erroneously reversed.

	* include/freetype/ftoption.h: Commenting out the macro
	TT_CONFIG_OPTION_BYTECODE_INTERPRETER.

2003-09-02  David Turner  <<EMAIL>>


	* Version 2.1.5 released.
	=========================


2003-08-31  Manish Singh  <<EMAIL>>

	* src/bdf/bdflib.c (_bdf_readstream): Don't use FT_MEM_COPY but
	FT_MEM_MOVE.

2003-08-30  Werner Lemberg  <<EMAIL>>

	* include/freetype/freetype.h (FT_ENCODING_SJIS, FT_ENCODING_GB2312,
	FT_ENCODING_BIG5, FT_ENCODING_WANSUNG, FT_ENCODING_JOHAB): New
	enumerations of FT_Encoding.  The FT_ENCODING_MS_* variants except
	FT_ENCODING_MS_SYMBOL are now deprecated.
	Updated all users.
	* docs/CHANGES: Document it.

2003-08-27  Werner Lemberg  <<EMAIL>>

	* src/bdf/bdfdrivr.c (BDF_Face_Init): Accept lowercase characters
	for spacing.

2003-08-27  Mike FABIAN  <<EMAIL>>

	* src/pcf/pcfread.c (pcf_load_font), src/bdf/bdfdrivr.c
	(BDF_Face_Init): Accept lowercase characters for slant and weight.

2003-08-18  David Turner  <<EMAIL>>

	* include/freetype/config/ftoption.h: Disabling TrueType bytecode
	interpreter until the UNPATENTED_HINTING works as advertised.

	* src/autohint/ahhint.c (ah_hinter_load_glyph): Use `|' for
	setting `load_flags'.

	* Jamfile: Adding the `refdoc' target to the Jamfile in order to
	build the API Reference in `docs/reference' automatically.

	* include/freetype/t1tables.h (PS_FontInfoRec), src/cid/cidtoken.h,
	src/type1/t1tokens.h, src/type42/t42parse.c: Resetting the types of
	`italic_angle', `underline_position', and `underline_thickness' to
	their previous values (i.e., long, short, and ushort) in order to
	avoid breaking binary compatibility.

	* include/freetype/ttunpat.h: Fixing documentation comment.

	* include/freetype/config/ftoption.h, devel/ftoption.h
	(TT_CONFIG_OPTION_COMPILE_UNPATENTED_HINTING): Replaced with...
	(TT_CONFIG_OPTION_UNPATENTED_HINTING): This.  Updated all users.
	(TT_CONFIG_OPTION_FORCE_UNPATENTED_HINTING): Removed.

	* include/freetype/internal/ftobjs.h (FT_DEBUG_HOOK_TYPE1): Removed.
	(FT_DEBUG_HOOK_UNPATENTED_HINTING): New macro.  Use this with
	`FT_Set_Debug_Hook' to get the same effect as the removed
	TT_CONFIG_OPTION_FORCE_UNPATENTED_HINTING.

	* src/truetype/ttobjs.c (tt_face_init): Use
	`FT_DEBUG_HOOK_UNPATENTED_HINTING'.

2003-08-06  Werner Lemberg  <<EMAIL>>

	* src/type1/t1gload.c (T1_Load_Glyph), src/cff/cffgload.c
	(cff_slot_load), src/cid/cidgload.c (cid_slot_load_glyph): Fix
	previous change.

2003-08-05  Werner Lemberg  <<EMAIL>>

	* src/type1/t1gload.c (T1_Load_Glyph), src/cff/cffgload.c
	(cff_slot_load), src/cid/cidgload.c (cid_slot_load_glyph): Apply
	font matrix to advance width also.
	* docs/CHANGES: Updated.

2003-07-26  Werner Lemberg  <<EMAIL>>

	* builds/unix/configure.ac (version_info): Set to 9:4:3.
	* builds/unix/configure: Updated.
	* docs/CHANGES, docs/VERSION.DLL: Updated.

	* include/freetype/freetype.h (FT_GlyphSlot): Change 2003-06-16
	also breaks binary compatibility.  Reintroduce an unsigned integer
	at the old position of `flags' called `reserved'.

2003-07-25  Werner Lemberg  <<EMAIL>>

	Make API reference valid HTML 4.01 transitional.

	* src/tools/docmaker/tohtml.py (html_header_1): Add doctype
	and charset.
	(html_header_2): Fix style elements and add some more.
	Fix syntax.
	(block_header, block_footer, description_header, description_footer,
	marker_header, marker_footer, source_header, source_footer,
	chapter_header): Don't use <center>...</center> but `align=center'
	table attribute.
	(chapter_inter, chapter_footer): Add <li> and use special <ul>
	class.
	Use double quotes around table widths given in percent.
	(keyword_prefix, keyword_suffix): Don't change font colour directly
	but use a new <span> class.
	(section_synopsis_header, section_synopsis_footer): Don't change
	colour.
	(code_header, code_footer): Don't change font colour directly but
	use a special <pre> class.
	(print_html_field): <tr> gets the `valign' attribute, not <table>.
	(print_html_field_list): Ditto.
	(index_exit): Don't use <center>...</center> but `align=center'
	table attribute.
	(section_enter): Ditto.
	(toc_exit): Don't emit </table>.
	(block_enter): Use <h4><a>, not <a><h4>.
	(__init__): Fix tag order in self.html_footer.

2003-07-25  David Turner  <<EMAIL>>

	This change reimplements fix from 2003-05-30 without breaking
	binary compatibility.

	* include/freetype/t1tables.h (PS_FontInfoRec): `italic_angle',
	`is_fixed_pitch', `underline_position', `underline_thickness' are
	reverted to be normal values.

	* include/freetype/internal/psaux.h (T1_FieldType): Remove
	`T1_FIELD_TYPE_BOOL_P', `T1_FIELD_TYPE_INTEGER_P',
	`T1_FIELD_TYPE_FIXED_P', `T1_FIELD_TYPE_FIXED_1000_P'.
	(T1_FIELD_TYPE_BOOL_P, T1_FIELD_NUM_P, T1_FIELD_FIXED_P,
	T1_FIELD_FIXED_1000_P): Removed.
	(T1_FIELD_TYPE_BOOL): Renamed to...
	(T1_FIELD_BOOL): New macro.  Updated all callers.

	* src/type42/t42parse.c: `italic_angle', `is_fixed_pitch',
	`underline_position', `underline_thickness', `paint_type',
	`stroke_width' are reverted to be normal values.
	(T42_KEYWORD_COUNT): New macro.
	(t42_parse_dict): New array `keyword_flags' to mark that a value has
	already been assigned to a dictionary entry.
	* src/type42/t42objs.c (T42_Face_Init, T42_Face_Done): Updated.

	* src/cid/cidtoken.h: `italic_angle', `is_fixed_pitch',
	`underline_position', `underline_thickness' are reverted to be
	normal values.
	* src/cid/cidobjs.c (cid_face_done, cid_face_init): Updated.

	* src/psaux/psobjs.c (ps_parser_load_field): Updated.

	* src/type1/t1tokens.h: `italic_angle', `is_fixed_pitch',
	`underline_position', `underline_thickness', `paint_type',
	`stroke_width' are reverted to be normal values.
	* src/type1/t1objs.c (T1_Face_Done, T1_Face_Init): Updated.
	* src/type1/t1load.c (T1_FIELD_COUNT): New macro.
	(parse_dict): Add parameter for keyword flags.
	Record only first instance of a field.
	(T1_Open_Face): New array `keyword_flags'.

2003-07-24  Werner Lemberg  <<EMAIL>>

	* include/freetype/freetype.h (FREETYPE_PATCH): Set to 5.
	* builds/unix/configure.ac (version_info): Set to 10:0:3.
	* builds/unix/configure: Updated.
	* builds/freetype.mk (refdoc): Fix --title.

	* docs/CHANGES, docs/VERSION.DLL, README: Updated.

	* src/tools/docmaker/sources.py (re_crossref): Fix regular
	expression to handle trailing punctuation characters.
	* src/tools/docmaker/tohtml.py (make_html_word): Updated.

	* docs/release: New file.

2003-07-23  YAMANO-UCHI Hidetoshi  <<EMAIL>>

	* include/freetype/internal/psaux.h (PS_Parser_FuncsRec): New
	member function `to_bytes'.

	* src/psaux/psauxmod.c (ps_parser_funcs): New member
	`ps_parser_to_bytes'.
	(psaux_module_class): Increase version to 0x20000L.

	* src/psaux/psobjs.c (IS_T1_LINESPACE): Add \f.
	(IS_T1_NULLSPACE): New macro.
	(IS_T1_SPACE): Add it.
	(skip_spaces, skip_alpha): New functions.
	(ps_parser_skip_spaces, ps_parser_skip_alpha): Use them.
	(ps_tobytes, ps_parser_to_bytes): New functions.

2003-07-07  Werner Lemberg  <<EMAIL>>

	* builds/freetype.mk (DOC_DIR): New variable.
	(refdoc): Use *_DIR variables.
	(distclean): Remove documentation files.

	* builds/detect.mk (std_setup, dos_setup): Mention `make refdoc'.

	* configure: Set DOC_DIR variable.

2003-07-07  Patrik Hägglund  <<EMAIL>>

	* builds/freetype.mk (refdoc): New target to build the
	documentation.
	(.PHONY): Updated.

	* include/freetype/freetype.h: Improve documentation of FT_CharMap.
	* include/freetype/ftimage,h: Fix documentation of FT_OUTLINE_FLAGS.
	* include/freetype/tttables.h: Document FT_Sfnt_Tag.

2003-07-06  Werner Lemberg  <<EMAIL>>

	* src/bdf/bdfdrivr.c (BDF_Face_Init), src/pcf/pcfread.c
	(pcf_load_font): Fix computation of height if PIXEL_SIZE property is
	missing.

2003-07-01  Werner Lemberg  <<EMAIL>>

	* src/cache/ftcsbits.c (ftc_sbit_node_compare): Only add `size' if
	there is no error.  Reported by Knut St. Osmundsen
	<<EMAIL>>.

2003-06-30  Werner Lemberg  <<EMAIL>>

	A new try to synchronize bitmap font access.

	* include/freetype/freetype.h (FT_Bitmap_Size): `height' is now
	defined to return the baseline-to-baseline distance.  This was
	already the value returned by the BDF and PCF drivers.

	The `width' field now gives the average width.  I wasn't able to
	find something better.  It should be taken as informative only.

	New fields `size', `x_ppem', and `y_ppem'.

	* src/pcf/pcfread.c (pcf_load_font): Updated to properly fill
	FT_Bitmap_Size.
	Do proper rounding and conversion from 72.27 to 72 points.

	* src/bdf/bdfdrivr.c (BDF_Face_Init): Updated to properly fill
	FT_Bitmap_Size.
	Do proper rounding and conversion from 72.27 to 72 points.

	* src/sfnt/sfobjs.c (sfnt_load_face): Updated to properly fill
	FT_Bitmap_Size.

	* src/winfonts/winfnt.c (FNT_Face_Init): Updated to properly fill
	FT_Bitmap_Size.

2003-06-29  Werner Lemberg  <<EMAIL>>

	Redesigning the FNT driver to return multiple faces, not multiple
	strikes.  At least one font (app850.fon from WinME) contains
	different FNT charmaps for its subfonts.  Consequently, the previous
	design of having multiple bitmap strikes in a single font face fails
	since we have only one charmap per face.

	* include/freetype/internal/fnttypes.h (FNT_Size_Rec): Removed.
	(FNT_FaceRec): Remove `num_fonts' field and replace `fonts' with
	`font'.

	* src/base/ftwinfnt.c (FT_Get_WinFNT_Header): Updated.

	* src/winfonts/winfnt.c (fnt_font_load): Don't set pixel_width equal
	to pixel_height.
	(fnt_face_done_fonts): Removed.
	(fnt_face_get_dll_fonts): Renamed to...
	(fnt_face_get_dll_font): This.  Add second function argument to
	select face index.
	Updated to load just one subfont.
	(fnt_font_done, FNT_Face_Done): Updated.
	(FNT_Face_Init): Handle `face_index'.
	Updated.
	(FNT_Size_Set_Pixels): Simplified; similar to BDF and PCF, the
	bitmap width is now ignored.
	(FNT_Load_Glyph): Updated.
	Fix glyph index computation.
	(winfnt_driver_class): Updated.

2003-06-25  Owen Taylor  <<EMAIL>>

	* src/sfnt/ttload.c (tt_face_load_hdmx): Don't assign
	num_records until we actually decide to load the table,
	otherwise, we'll segfault in tt_face_free_hdmx.

2003-06-24  Werner Lemberg  <<EMAIL>>

	* src/cff/cffdrivr.c (cff_get_glyph_name): Protect against zero
	glyph name pointer.  Reported by Mikey Anbary <<EMAIL>>.

2003-06-23  Werner Lemberg  <<EMAIL>>

	* src/tools/glnames.py: Updated to AGL 2.0.
	* src/psnames/pstables.h: Regenerated.

2003-06-22  Werner Lemberg  <<EMAIL>>

	* include/freetype/cache/ftcglyph.h, include/freetype/ttnameid.h,
	src/base/ftcalc.c, src/base/fttrigon.c, src/cff/cffgload.c,
	src/otlayout/otlgsub.c, src/pshinter/pshrec.c,
	src/psnames/psmodule.c, src/sfnt/sfobjs.c, src/truetype/ttdriver.c:
	Decorate constants with `U' and `L' if appropriate.

	* include/freetype/ftmoderr.h: Updated to include recent module
	additions.

	* src/pshinter/pshnterr.h (FT_ERR_BASE): Define as
	`FT_Mod_Err_PShinter'.
	* src/type42/t42error.h (FT_ERR_BASE): Define as
	`FT_Mod_Err_Type42'.

	* src/pshinter/pshrec.h (PS_HINTS_MAGIC): Removed.  Not used.

	* include/freetype/config/ftconfig.h [__MWERKS__]: Define FT_LONG64
	and FT_INT64.

2003-06-21  Werner Lemberg  <<EMAIL>>

	* src/winfonts/winfnt.c (FNT_Load_Glyph): Use first_char in
	computation of glyph_index.
	(FNT_Size_Set_Pixels): To find a strike, first check pixel_height
	only, then try to find a better hit by comparing pixel_width also.
	Without this fix it isn't possible to access all strikes.
	Also compute metrics.max_advance to be in sync with other bitmap
	drivers.

	* src/base/ftobjs.c (FT_Set_Char_Size): Remove redundant code.
	(FT_Set_Pixel_Sizes): Assign value to `metrics' after validation of
	arguments.

2003-06-20  Werner Lemberg  <<EMAIL>>

	Synchronize computation of height and width for bitmap strikes.  The
	`width' field in the FT_Bitmap_Size structure is now only useful to
	enumerate different strikes.  The `max_advance' field of the
	FT_Size_Metrics structure should be used to get the (maximum) width
	of a strike.

	* src/bdf/bdfdrivr.c (BDF_Face_Init): Don't use AVERAGE_WIDTH for
	computing `available_sizes->width' but make it always equal to
	`available_sizes->height'.

	* src/pcf/pcfread.c (pcf_load_font): Don't use RESOLUTION_X for
	computing `available_sizes->width' but make it always equal to
	`available_sizes->height'.

	* src/truetype/ttdriver.c (Set_Pixel_Sizes): Pass only single
	argument to function.

	* src/psnames/psmodule.c (ps_unicode_value): Handle `.' after
	`uniXXXX' and `uXXXX[X[X]]'.

2003-06-19  Werner Lemberg  <<EMAIL>>

	* src/bdf/bdfdrivr.c: s/FT_Err_/BDF_Err/.
	* src/cache/ftccache.c, src/cache/ftcsbits.c, src/cache/ftlru.c:
	s/FT_Err_/FTC_Err_/.
	* src/cff/cffcmap.c: s/FT_Err_/CFF_Err_/.
	* src/pcf/pcfdrivr.c: s/FT_Err_/PCF_Err_/.
	* src/psaux/t1cmap.c: Include psauxerr.h.
	s/FT_Err_/PSaux_Err_/.
	* src/pshinter/pshnterr.h: New file.
	* src/pshinter/rules.mk: Updated.
	* src/pshinter/pshalgo.c, src/pshinter/pshrec.c: Include pshnterr.h.
	s/FT_Err_/PSH_Err_/.
	* src/pfr/pfrdrivr.c, src/pfr/pfrobjs.c, src/pfr/pfrsbit.c:
	s/FT_Err_/PFR_Err_/.
	* src/sfnt/sfdriver.c, src/sfnt/sfobjs.c, src/sfnt/ttcmap0.c,
	src/sfnt/ttload.c: s/FT_Err_/SFNT_Err_/.
	* src/truetype/ttgload.c: s/FT_Err_/TT_Err_/.
	* src/gzip/ftgzip.c: Load FT_MODULE_ERRORS_H and define
	FT_ERR_PREFIX and FT_ERR_BASE.
	s/FT_Err_/Gzip_Err_/.

2003-06-19  Dirck Blaskey  <<EMAIL>>

	* src/cff/cffload (cff_encoding_load): `nleft' must be FT_UInt,
	otherwise adding 1 might wrap the result.

2003-06-18  Werner Lemberg  <<EMAIL>>

	* src/psnames/psmodule.c (ps_unicode_value): Add support to
	recognize `uXXXX[X[X]]' glyph names.
	Don't handle glyph names starting with `uni' which have more than
	four digits.

2003-06-16  Werner Lemberg  <<EMAIL>>

	* include/freetype/freetype.h (FT_Open_Flags): Replaced with
	#defines for the constants.
	(FT_Open_Args): Change type of `flags' to FT_UInt.
	(FT_GlyphSlot): Move `flags' to FT_Slot_Internal.

	* include/freetype/ftimage.h (FT_Outline_Flags, FT_Raster_Flag):
	Replaced with #defines for the constants.

	* include/freetype/internal/ftobjs.h (FT_Slot_Internal): New
	field `flags' (from FT_GlyphSlot).
	Updated all affected source files.
	(FT_GLYPH_OWN_BITMAP): New macro (from ftgloadr.h).

	* include/freetype/internal/ftgloadr.h (FT_GLYPH_OWN_BITMAP): Moved
	to ftobjs.h.

	* src/base/ftglyph.c (FT_Glyph_To_Bitmap): Use dummy
	FT_GlyphSlot_Internal object.

2003-06-15  Werner Lemberg  <<EMAIL>>

	* builds/compiler/gcc.mk, builds/compiler/gcc-dev.mk (CFLAGS):
	Add -fno-strict-aliasing to get rid of zillion warnings from gcc
	version 3.3.

2003-06-14  Werner Lemberg  <<EMAIL>>

	* include/freetype/ftglyph.h (ft_glyph_bbox_unscaled,
	ft_glyph_bbox_subpixels, ft_glyph_bbox_gridfit,
	ft_glyph_bbox_truncate, ft_glyph_bbox_pixels): Replaced with
	FT_GLYPH_BBOX_UNSCALED, FT_GLYPH_BBOX_SUBPIXELS,
	FT_GLYPH_BBOX_GRIDFIT, FT_GLYPH_BBOX_TRUNCATE, FT_GLYPH_BBOX_PIXELS.
	The lowercase variants are now (deprecated aliases) to the uppercase
	versions.
	Updated all other files.

	* include/freetype/ftmodule.h (ft_module_font_driver,
	ft_module_renderer, ft_module_hinter, ft_module_styler,
	ft_module_driver_scalable, ft_module_driver_no_outlines,
	ft_module_driver_has_hinter): Replaced with FT_MODULE_FONT_DRIVER,
	FT_MODULE_RENDERER, FT_MODULE_HINTER, FT_MODULE_STYLER,
	FT_MODULE_DRIVER_SCALABLE, FT_MODULE_DRIVER_NO_OUTLINES,
	FT_MODULE_DRIVER_HAS_HINTER.
	The lowercase variants are now (deprecated aliases) to the uppercase
	versions.
	Updated all other files.

	* src/base/ftglyph.c (FT_Glyph_Get_CBox): Handle bbox_mode better
	as enumeration.

	* src/pcf/pcfdrivr.c (pcf_driver_class), src/winfonts/winfnt.c
	(winfnt_driver_class), src/bdf/bdfdrivr.c (bdf_driver_class): Add
	the FT_MODULE_DRIVER_NO_OUTLINES flag.

2003-06-13  Detlef Würkner  <<EMAIL>>

	* src/pfr/pfrobjs.c (pfr_slot_load): Apply font matrix.

2003-06-13  Werner Lemberg  <<EMAIL>>

	* builds/dos/detect.mk: Test not only for `Dos' but for `DOS' also.

	* builds/dos/dos-emx.mk, builds/compiler/emx.mk: New files for
	EMX gcc compiler.
	* builds/dos/detect.mk: Add target `emx'.

	* builds/compiler/watcom.mk (LINK_LIBRARY): GNU Make for DOS doesn't
	like a trailing semicolon; add a dummy command.

	* src/cid/cidload.c: Remove parse_font_bbox code (already enclosed
	with #if 0 ... #endif).

	* src/type1/t1tokens.h: Handle /FontName.
	* src/type1/t1load.c (parse_font_name): Removed.
	Remove parse_font_bbox code (already enclosed with #if 0 ...
	#endif).

	* src/type42/t42parse.c (t42_parse_font_name): Removed.
	Remove t42_parse_font_bbox code (already enclosed with #if 0 ...
	#endif).
	(t42_keywords): Handle /FontName with T1_FIELD_KEY.

2003-06-12  Werner Lemberg  <<EMAIL>>

	* include/freetype/internal/psaux.h (T1_FieldType): Add
	T1_FIELD_TYPE_KEY.
	(T1_FIELD_KEY): New macro.
	* src/psaux/psobjs.c (ps_parser_load_field): Handle
	T1_FIELD_TYPE_KEY.

	* src/cid/cidtoken.h: Use T1_FIELD_KEY for /CIDFontName.

2003-06-11  Alexander Malmberg  <<EMAIL>>

	* src/cache/ftlru.c (FT_LruList_Remove_Selection): Decrease
	number of nodes.
	(FT_LruList_Lookup): Fix assertion for out-of-memory case.

2003-06-11  Werner Lemberg  <<EMAIL>>

	* src/cid/cidload.c (cid_decrypt): Removed.
	(cid_read_subrs): Use t1_decrypt from psaux module.
	* src/cid/cidload.h: Updated.
	* src/cid/cidgload.c (cid_load_glyph): Use t1_decrypt from psaux
	module.

2003-06-10  Werner Lemberg  <<EMAIL>>

	* src/cid/cidobjs.c: Apply change 2003-05-31 from <<EMAIL>>.
	Compute style flags.
	Fix computation of root->height.
	* src/cid/cidtoken.h: Handle FontBBox.
	* src/cid/cidload.c (cid_load_keyword): Handle
	T1_FIELD_LOCATION_BBOX.
	(parse_font_bbox): Commented out.
	(cid_field_records): Comment out element for parsing FontBBox.

	* src/type42/t42parse.c (t42_parse_font_bbox): Commented out.
	(t42_keywords): Handle FontBBox with T1_FIELD_BBOX, not with
	T1_FIELD_CALLBACK.
	(t42_parse_font_bbox): Commented out.
	(t42_load_keyword): Handle T1_FIELD_LOCATION_BBOX.
	* src/type42/t42objs.c (T42_Face_Init): Apply change 2003-05-31
	from <<EMAIL>>.

2003-06-09  George Williams  <<EMAIL>>

	* src/truetype/ttinterp.c (SetSuperRound) <0x30>: Follow Apple's
	TrueType specification.
	(Ins_MDRP, Ins_MIRP): Fix single width cut-in test.

2003-06-09  Detlef Würkner  <<EMAIL>>

	* src/gzip/ftgzip.c: (inflate_mask): Replaced with...
	(NO_INFLATE_MASK): This.
	* src/gzip/infutil.h: Declare `inflate_mask' conditionally by
	NO_INFLATE_MASK.

2003-06-09  Alexis S. L. Carvalho  <<EMAIL>>

	* src/gzip/ftgzip.c (ft_gzip_file_fill_output): Handle Z_STREAM_END
	correctly.

2003-06-09  Wolfgang Domröse  <<EMAIL>>

	* src/pshinter/pshglob.c (psh_globals_new): Change calculation of
	dim->stdw.count to avoid compiler problem.

	* src/truetype/ttgload.c (TT_Load_Simple_Glyph): Move the block
	variables to the beginning of the function to avoid compiler
	problems.
	Add casts necessary for 16bit compilers.

2003-06-09  Werner Lemberg  <<EMAIL>>

	* src/pfr/rules.mk (PFR_DRV_SRC): Add pfrsbit.c.
	(PFR_DRV_H): Add pfrtypes.h.

	* include/freetype/config/ftconfig.h: s/__MWKS__/__MWERKS__/.

2003-06-08  Karl Schultz  <<EMAIL>>

	* src/pfr/pfrsbit.c (pfr_bitwriter_init): Change type of third
	argument to FT_Bool.
	(pfr_lookup_bitmap_data): Change type of third and fourth argument
	to FT_UInt.  Updated caller.
	(pfr_load_bitmap_bits): Change type of fourth argument to FT_Bool.

2003-06-08  Werner Lemberg  <<EMAIL>>

	Completely revised FreeType's make management.

	. In all makefiles `/' is used as the path separator.  The
	  conversion to the real path separators is done as late as
	  possible using $(subst ...).

	. $(HOSTSEP) no longer exists.  Now, $(SEP) gives the path separator
	  for the operating system, and the new $(COMPILER_SEP) the path
	  separator for the compiler tools.

	. $(BUILD) has been renamed to $(BUILD_DIR).  In general, all
	  directory variables end with `_DIR'.  The variants ending in `_'
	  (like `BASE_' have been removed).

	The following ChangeLog entries only describe changes which are
	not related to the redesign.

	* builds/beos/beos-def.mk (BUILD_DIR): Fix typo.
	* builds/compiler/watcom.mk (LINK_LIBRARY): Fix linker call to avoid
	overlong arguments as suggested by J. Ali Harlow
	<<EMAIL>>.
	* builds/dos/dos-wat.mk: New file.
	* builds/freetype.mk (FREETYPE_H): Include header files from the
	`devel' subdirectory.

	* builds/os2/os2-dev.mk, builds/unix/unixddef.mk,
	builds/unix/unixddef.mk, builds/win32/w32-bccd.mk,
	builds/win32/w32-dev.mk (BUILD_DIR): Fix path.

	* builds/unix/configure.ac, builds/unix/configure: Updated.
	* builds/unix/unix-def.in (DISTCLEAN): Add `freetype2.pc'.

2003-06-07  Werner Lemberg  <<EMAIL>>

	* src/base/ftmac.c (FT_New_Face_From_SFNT): s/rlen/sfnt_size/ to
	make it compile.

	* devel/ftoption.h: Updated.

2003-06-07  Detlef Würkner  <<EMAIL>>

	* include/freetype/internal/psaux.h, src/truetype/ttgload.h:
	s/index/idx/ to fix compiler warnings.

	* src/sfnt/ttcmap0.c (tt_face_build_cmaps): Use more `volatile' to
	fix compiler warning.

	* src/gzip/ftgzip.c (BUILDFIXED): Removed.
	* src/gzip/inftrees.c (inflate_trees_fixed) [!BUILDFIXED]: Use
	FT_UNUSED to remove compiler warning.

2003-06-06  Werner Lemberg  <<EMAIL>>

	* include/freetype/ftstroker.h: Renamed to...
	* include/freetype/ftstroke.h: This.

	* src/base/ftstroker.c: Renamed to...
	* src/base/ftstroke.c: This.

	* include/freetype/config/ftheader.h (FT_STROKER_H): Updated.

	* src/base/descrip.mms, src/base/Jamfile, src/base/rules.mk:
	Updated.

	* src/pcf/pcfdriver.c: Renamed to...
	* src/pcf/pcfdrivr.c: This.
	* src/pcf/pcfdriver.h: Renamed to...
	* src/pcf/pcfdrivr.h: This.

	* src/pcf/Jamfile, src/pcf/rules.mk: Updated.

2003-06-05  Wenlin Institute (Tom Bishop)  <<EMAIL>>

	* src/base/ftmac.c (file_spec_from_path) [TARGET_API_MAC_CARBON]:
	Add `#if !defined(__MWERKS__)'.

2003-06-05  Werner Lemberg  <<EMAIL>>

	* include/freetype/internal/psaux.h (T1_FieldType): Add
	T1_FIELD_TYPE_FIXED_1000 and T1_FIELD_TYPE_FIXED_1000_P.
	(T1_FIELD_FIXED_1000, T1_FIELD_FIXED_1000_P): New macros.
	* src/psaux/psobjs.c (ps_parser_load_field): Handle
	T1_FIELD_TYPE_FIXED_1000 and T1_FIELD_TYPE_FIXED_1000_P.

	* src/cff/cffparse.c (cff_kind_fixed_thousand): New enumeration.
	(CFF_FIELD_FIXED_1000): New macro.
	(cff_parser_run): Handle cff_kind_fixed_thousand.
	* src/cff/cfftoken.h: Use CFF_FIELD_FIXED_1000 for blue_scale.
	* src/cff/cffload (cff_subfont_load): Fix default values of
	expansion_factor and blue_scale.

	* src/cid/cidtoken.h, src/type1/t1tokens.h: Use T1_FIELD_FIXED_1000
	for blue_scale.

	* src/pshinter/pshglob.c (psh_globals_new): Fix default value of
	blue_scale.

2003-06-04  Wolfgang Domröse  <<EMAIL>>

	* include/freetype/internal/ftdriver.h,
	include/freetype/internal/ftobjs.h,
	include/freetype/internal/psaux.h, src/cid/cidgload.c,
	src/psaux/psobjs.c, src/psaux/t1decode.c, src/psaux/psobjs.h,
	src/pshinter/pshrec.c, src/pshinter/pshalgo.c,
	src/psnames/psmodule.c, src/raster/ftraster.c, src/sfnt/sfobjs.c,
	src/smooth/ftgrays.c, src/smooth/ftsmooth.c, src/truetype/ttobjs.c,
	src/truetype/ttdriver.c, src/truetype/ttgload.c, src/type1/t1afm.c,
	src/type1/t1gload.c, src/type1/t1gload.h, src/type1/t1load.c,
	src/type1/t1objs.c, src/type42/t42parse.c, src/type42/t42parse.h:
	Many casts and slight argument type changes to make it work with
	a 16bit compiler.

2003-06-04  Werner Lemberg  <<EMAIL>>

	* include/freetype/config/ftoption.h: Defining
	TT_CONFIG_OPTION_FORCE_UNPATENTED_HINTING by default is a bad idea
	since some fonts (e.g. Arial) produce worse results than without
	hinting.  Reverted.

2003-06-04  Werner Lemberg  <<EMAIL>>

	* src/truetype/ttgload.c (load_truetype_glyph)
	[TT_CONFIG_OPTION_BYTECODE_INTERPRETER]: Call
	FT_GlyphLoader_CheckPoints before adding phantom points.  This fixes
	a segfault bug with fonts (e.g. htst3.ttf) which have nested
	subglyphs more than one level deep.  Reported by Anthony Fok.

	* include/freetype/config/ftoption.h: Define
	TT_CONFIG_OPTION_BYTECODE_INTERPRETER,
	TT_CONFIG_OPTION_COMPILE_UNPATENTED_HINTING, and
	TT_CONFIG_OPTION_FORCE_UNPATENTED_HINTING to make it the new
	default.

2003-06-03  Werner Lemberg  <<EMAIL>>

	* src/autohint/ahhint.c (ah_hinter_hint_edges): Removed.  Just a
	wrapper for ah_hint_edges.
	(ah_hint_edges): Renamed to...
	(ah_hinter_hint_edges): This.

	* src/base/ftobjs.c (FT_Set_Hint_Flags): Removed.  Unused.

	* include/freetype/internal/ftobjs.h (FT_Face_InternalRec),
	include/freetype/internal/psaux.h (T1_DecoderRec),
	src/cff/cffgload.h (CFF_Builder): Remove `hint_flags' field.
	Unused.

	* src/cff/cffgload.c (cff_builder_init): Updated.
	(cff_decoder_parse_charstrings) <cff_op_endchar>: Call hinter->apply
	with decoder->hint_mode instead of builder->hint_flags.
	* src/psaux/t1decode.c (t1_decoder_init): Updated.

	* src/base/ftstroker.c (ft_stroke_border_export): s/index/idx/.

	* src/sfnt/sfobjs.c (sfnt_load_face): Commented out code which
	increased root->height by 15% if the line gap was zero.  There exist
	fonts (containing e.g. form drawing characters) which intentionally
	have a zero line gap value.

	* src/truetype/ttinterp.c (Free_Project, CUR_Func_freeProj):
	Removed.  Unused.
	Updated all callers.

2003-06-02  Werner Lemberg  <<EMAIL>>

	* src/cff/cffobjs.c (cff_face_init): Use symbolic names for
	Adobe specific encoding IDs (there was a wrong EID value for custom
	encoding).

	* src/cff/cffcmap.h (CFF_CMapStdRec): Remove `count'.
	* src/cff/cffcmap.c (cff_cmap_encoding_init,
	cff_cmap_encoding_done): Updated.
	(cff_cmap_encoding_char_index, cff_cmap_encoding_char_next): Use
	256 as limit for character code.

2003-06-01  Werner Lemberg  <<EMAIL>>

	* src/winfonts/winfnt.c (FNT_Load_Glyph): Revert change from
	2003-03-20.

2003-05-31  Werner Lemberg  <<EMAIL>>

	* include/freetype/fttrigon.h (FT_Vector_Normalize): Removed.

2003-05-31    <<EMAIL>>

	* src/type1/t1objs.c (T1_Face_Init): Improve algorithm for guessing
	the font style by ignoring spaces and hyphens.

	* builds/unix/freetype2.in: Fix `Version' field.

2003-05-30  Werner Lemberg  <<EMAIL>>

	Avoid overwriting of numeric font dictionary entries for synthetic
	fonts.  Additionally, some entries were handled as `integer' instead
	of `number'.

	* include/freetype/internal/psaux.h (T1_FieldType): Add
	T1_FIELD_TYPE_BOOL_P, T1_FIELD_TYPE_INTEGER_P, and
	T1_FIELD_TYPE_FIXED_P.
	(T1_FIELD_BOOL_P, T1_FIELD_NUM_P, T1_FIELD_FIXED_P): New macros.
	* src/psaux/psobjs.c (ps_parser_load_field): Handle new field types.

	* include/freetype/internal/cfftypes.h (CFF_FontRecDict),
	src/cff/cfftoken.h: Change type of underline_position and
	underline_thickness to FT_Fixed.
	* src/cff/cffload.c (cff_subfont_load): Fix default values of
	underline_position and underline_thickness.
	* src/cff/cffobjs.c (cff_face_init): Set underline_position
	and underline_thickness in `root'.

	* include/freetype/internal/t1types.h (T1_Font): Change point_type
	and stroke_width to pointers.
	* include/freetype/t1tables.h (PS_FontInfo): Change italic_angle,
	is_fixed_pitch, underline_position, and underline_thickness to
	pointers.
	* src/type1/t1tokens.h: Change italic_angle, is_fixed_pitch,
	underline_position, and underline_thickness to pointers.  Change
	the type of the latter two to `fixed'.
	Change type of stroke_width to `fixed' and make it a pointer.
	Change paint_type to pointer.
	* src/type1/t1objs.c (T1_Face_Done): Updated.
	(T1_Face_Init): Updated.
	Fix assignment of underline_position and underline_thickness.

	* src/cid/cidtoken.h: Change italic_angle, is_fixed_pitch,
	underline_position, and underline_thickness to pointers.  Change
	the type of the latter two to `fixed'.
	Change type of stroke_width to `fixed'.
	* src/cid/cidobjs.c (cid_face_done): Updated.
	(cid_face_init): Updated.
	Fix assignment of underline_position and underline_thickness.

	* src/type42/t42parse.c: Change italic_angle, is_fixed_pitch,
	underline_position, and underline_thickness to pointers.  Change the
	type of the latter two to `fixed'.
	Change type of stroke_width to `fixed' and make it a pointer.
	Change paint_type to pointer.
	* src/type42/t42objs.c (T42_Face_Init): Updated.
	Fix assignment of underline_position and underline_thickness.
	(T42_Face_Done): Updated.

	* src/base/ftobjs.c (open_face_from_buffer): Fix compiler warning.
	* src/pshinter/pshglob.c, src/pshinter/pshglob.h
	(psh_globals_set_scale): Make it a local function.

	* test/gview.c: Fix renaming ps3->ps typo.
	Formatting.

2003-05-29  Werner Lemberg  <<EMAIL>>

	* src/pshinter/pshalgo1.[ch], src/pshinter/pshalgo2.[ch]: Removed.
	* src/pshinter/pshalgo.h: Removed.

	* src/pshinter/pshalgo3.[ch]: Renamed to...
	* src/pshinter/pshalgo.[ch]: New files.
	s/PSH3/PSH/.
	s/psh3/psh/.
	s/ps3/ps/.

	* src/pshinter/pshrec.c, src/pshinter/pshinter.c: Updated.
	* src/pshinter/rules.mk, src/pshinter/Jamfile: Updated.

	* src/pshinter/pshglob.[ch] (psh_dimension_snap_width): Commented
	out.

	* tests/gview.c: Remove code for pshalgo1 and pshalgo2.
	Updated.

2003-05-28  Martin Zinser  <<EMAIL>>

	* vms_make.com: Reworked support for shareable images on VMS.  The
	first version was kind of a hack; the current implementation of the
	procedure to extract the required symbols is much cleaner.

	Reworked creation of MMS files, avoiding a number of temporary files
	which were created in the previous version.

	Further work on creating descrip.mms files on the fly.

	* builds/vms/descrip.mms, src/autohint/descrip.mms,
	src/type1/descrip.mms: Removed.

2003-05-28  Werner Lemberg  <<EMAIL>>

	* src/pshinter/pshalgo3.c (psh3_glyph_compute_extrema): Skip
	contours with only a single point to avoid segfault.

	* src/base/ftglyph.c (FT_Glyph_To_Bitmap): Activate code for
	handling `origin'.

2003-05-24  Werner Lemberg  <<EMAIL>>

	* src/autohint/ahtypes.h (AH_OPTION_NO_STRONG_INTERPOLATION):
	Removed since unused.

2003-05-21  Werner Lemberg  <<EMAIL>>

	* include/freetype/config/ftstdlib.h (ft_strcat): New wrapper macro
	for strcat.

	* src/base/ftmac.c (create_lwfn_name): s/isupper/ft_isupper/.
	(parse_font): s/memcpy/ft_memcpy/.
	(is_dfont) [TARGET_API_MAC_CARBON]: s/memcmp/ft_memcmp/.
	* src/base/ftobjs.c (load_mac_face) [FT_MACINTOSH]:
	s/strlen/ft_strlen/.
	s/strcat/ft_strcat/.
	s/strcpy/ft_strcpy/.
	* src/gzip/zutil.h: s/memset/ft_memset/.
	s/memcmp/ft_memcmp/.

	* src/bdf/bdfdrivr.c (BDF_Face_Init), src/pcf/pcfdriver.c
	(PCF_Face_Init): Test for charset registry case-insensitively.

	* src/gzip/ftgzip.c (ft_gzip_file_io): Revert change from yesterday;
	it has already been fixed differently.

	* src/truetype/ttinterp.c (DO_SFVTL): Add missing braces around
	if-clause.

2003-05-21  Martin Zinser  <<EMAIL>>

	* t1load.c (parse_blend_axis_types): Fix compiler warning.

	* descrip.mms: Removed.  Now created by...

	* vms_make.com: New file.

2003-05-21  Weiqi Gao  <<EMAIL>>

	* src/gzip/ftgzip.c (ft_gzip_file_io): Avoid zero value of `delta'
	to prevent infinite loop.

2003-05-21  Lars Clausen  <<EMAIL>>

	* docs/VERSION.DLL: Provide better autoconf snippet to check
	FreeType version.

2003-05-21  Werner Lemberg  <<EMAIL>>

	* src/base/ftobjs.c (open_face): Free `internal' not
	`face->internal' in case of error to avoid possible segfault.

	* src/pshinter/pshalgo3.c (ps3_hints_apply): Check whether we
	actually have an outline.

2003-05-20  David Chester  <<EMAIL>>

	* src/pshinter/pshalgo3.c (ps3_hints_apply): Try to optimize
	y_scale so that the top of non-capital letters is aligned on a pixel
	boundary whenever possible.

	* src/autohint/ahhint.c (ah_hint_edges): Make sure that lowercase
	m's maintain their symmetry.

2003-05-20  Werner Lemberg  <<EMAIL>>

	* src/autohint/ahhint.c (ah_hinter_load_glyph): Oops!  David's
	patch from yesterday has been resolved already in a different
	way.  Reverted.

2003-05-19  David Chester  <<EMAIL>>

	* src/autohint/ahhint.c (ah_hinter_load_glyph): Don't scale
	y_scale locally but face->size->metrics.y_scale.

2003-05-19  David Turner  <<EMAIL>>

	* src/sfnt/ttcmap0.c (tt_cmap4_char_next): Select proper start
	value for `hi' to avoid infinite loop.

2003-05-18  Yong Sun  <<EMAIL>>

	* src/raster/ftraster.c (Insert_Y_Turn): Fix overflow test.

2003-05-18  Werner Lemberg  <<EMAIL>>

	* include/freetype/config/ftoption.h [FT_CONFIG_OPTION_MAC_FONTS]:
	New macro.
	* src/base/ftobjs.c: Use it to control mac font support on non-mac
	platforms.

2003-05-17  George Williams  <<EMAIL>>

	Implement partial support of Mac fonts on non-Mac platforms.

	* src/base/ftobjs.c (memory_stream_close, new_memory_stream,
	open_face_from_buffer, Mac_Read_POST_Resource,
	Mac_Read_sfnt_Resource, IsMacResource, IsMacBinary, load_mac_face)
	[!FT_MACINTOSH]: New functions.
	(FT_Open_Face) [!FT_MACINTOSH]: Use load_mac_face.

2003-05-17  Werner Lemberg  <<EMAIL>>

	* src/base/ftobjs.c (FT_Load_Glyph): Scale linear advance width only
	if FT_FACE_FLAG_SCALABLE is set (otherwise we have a division by
	zero since FNT and friends don't define `face->units_per_EM').

2003-05-15  David Turner  <<EMAIL>>

	* src/base/fttrigon.c (FT_Vector_Rotate): Avoid rounding errors
	for small values.

2003-05-15  Werner Lemberg  <<EMAIL>>

	* src/autohint/ahtypes.h (AH_PointRec): Remove unused `in_angle'
	and `out_angle' fields.

2003-05-14  George Williams  <<EMAIL>>

	* src/base/ftmac.c (FT_New_Face_From_SFNT): Handle CFF files also.

2003-05-14  Werner Lemberg  <<EMAIL>>

	* include/freetype/freetype.h: Fix typo in comment
	(FT_HAS_FIXED_SIZES).

2003-05-10  Dan Williams  <<EMAIL>>

	* builds/unix/aclocal.m4: Comment out definition of
	`allow_undefined_flag' for Darwin 1.3.
	* builds/unix/configure.ac: Add option --with-old-mac-fonts.
	* builds/unix/ltmain.sh: Fix version numbering for Darwin 1.3.
	* builds/unix/configure: Regenerated.

	* include/freetype/config/ftconfig.h: Fix conditions for defining
	`FT_MACINTOSH'.
	* src/base/ftbase.c: Include `ftmac.c' conditionally.
	* src/base/ftmac.c: Handle __GNUC__.

2003-05-07  YAMANO-UCHI Hidetoshi  <<EMAIL>>

	* src/cid/cidload.c (is_alpha): Removed.
	(cid_parse_dict): Use `cid_parser_skip_alpha' instead of `is_alpha'.

2003-05-07  Werner Lemberg  <<EMAIL>>

	* src/autohint/ahoptim.c, src/autohint/ahoptim.h: Obsolete, removed.

2003-05-07  David Turner  <<EMAIL>>

	* src/autohint/ahglyph.c (ah_setup_uv): Exchange `for' loop and
	`switch' statement to make it run faster.
	(ah_outline_compute_segments): Reset `segment->score' and
	`segment->link'.
	(ah_outline_link_segments): Provide alternative code which does
	the same but runs much faster.
	Handle major direction also.
	(ah_outline_compute_edges): Scale `edge_distance_threshold' down
	after rounding instead of scaling comparison value in loop.

	* src/autohint/ahhint.c (ah_hinter_align_strong_points): Provide
	alternative code which runs faster.
	Handle `before->scale == 0'.

	* src/autohint/ahtypes.h (AH_SegmentRec): Move some fields down.
	(AH_EdgeRec): Move some fields in structure.
	New field `scale'.

	* src/sfnt/ttcmap0.c (tt_cmap4_char_next): Use binary search.

2003-05-02  Werner Lemberg  <<EMAIL>>

	* src/autohint/ahoptim.c (LOG): Renamed to...
	(AH_OPTIM_LOG): This.
	(AH_Dump_Springs): Fix log message format.

	* src/autohint/ahhint.c (ah_hint_edges_3): Renamed to...
	(ah_hint_edges): This.

2002-05-02  Keith Packard  <<EMAIL>>

	* src/bdf/bdfdrivr.c (BDF_Set_Pixel_Size): Initialize `max_advance'.

2003-05-01  Werner Lemberg  <<EMAIL>>

	* src/autohint/ahglyph.c (ah_test_extrema): Renamed to...
	(ah_test_extremum): This.

2003-04-28  Werner Lemberg  <<EMAIL>>

	* builds/unix/configure.ac: Generate `freetype.pc' from
	`freetype.in'.
	* builds/unix/configure: Regenerated.
	* builds/unix/install.mk (install, uninstall): Handle `freetype.pc'.

2003-04-28  Gustavo J. A. M. Carneiro  <<EMAIL>>

	* builds/unix/freetype2.in: New file.  Contains building information
	for the `pkg-config' package.

2003-04-28  David Turner  <<EMAIL>>

	* src/base/ftobjs.c (FT_Load_Glyph): Fix boundary check for
	`glyph_index'.

2003-04-25:  Graham Asher  <<EMAIL>>

	Added the optional unpatented hinting system for TrueType.  It
	allows typefaces which need hinting to produce correct glyph forms
	(e.g., Chinese typefaces from Dynalab) to work acceptably without
	infringing Apple patents.  This system is compiled only if
	TT_CONFIG_OPTION_COMPILE_UNPATENTED_HINTING is defined in
	ftoption.h.

	* include/freetype/ttunpat.h: New file.  Defines
	FT_PARAM_TAG_UNPATENTED_HINTING.

	* include/freetype/config/ftheader.h (FT_TRUETYPE_UNPATENTED_H): New
	macro to use when including ttunpat.h.

	* include/freetype/config/ftoption.h
	(TT_CONFIG_OPTION_COMPILE_UNPATENTED_HINTING,
	TT_CONFIG_OPTION_FORCE_UNPATENTED_HINTING): New configuration macros
	(not defined, but in comments) for the unpatented hinting system.

	* include/freetype/internal/tttypes.h (TT_FaceRec)
	[TT_CONFIG_OPTION_COMPILE_UNPATENTED_HINTING]: New element `FT_Bool
	unpatented_hinting'.

	* src/truetype/ttinterp.c (NO_APPLE_PATENT, APPLE_THRESHOLD):
	Removed.
	(GUESS_VECTOR): New macro.
	(TT_Run_Context) [TT_CONFIG_OPTION_COMPILE_UNPATENTED_HINTING]:
	Set `both_x_axis'.
	(tt_default_graphics_state)
	[TT_CONFIG_OPTION_COMPILE_UNPATENTED_HINTING]: Updated.
	(Current_Ratio) [TT_CONFIG_OPTION_COMPILE_UNPATENTED_HINTING]:
	Handle `unpatented_hinting'.
	(Direct_Move) [NO_APPLE_PATENT]: Removed.
	[TT_CONFIG_OPTION_COMPILE_UNPATENTED_HINTING]: Insert assertion.
	(Project, FreeProject)
	[TT_CONFIG_OPTION_COMPILE_UNPATENTED_HINTING]: Insert assertion.
	(Compute_Funcs) [TT_CONFIG_OPTION_COMPILE_UNPATENTED_HINTING]:
	Implement unpatented hinting.
	(DO_SPVTCA, DO_SFVTCA, DO_SPVTL, DO_SFVTL, DO_SPVFS, DO_SFVFS,
	Ins_SDPVTL): Call `GUESS_VECTOR'.
	(DO_GPV, DO_GFV) [TT_CONFIG_OPTION_COMPILE_UNPATENTED_HINTING]:
	Handle `unpatented_hinting'.
	(Compute_Point_Displacement) [NO_APPLE_PATENT]: Removed.
	[TT_CONFIG_OPTION_COMPILE_UNPATENTED_HINTING]: Implement unpatented
	hinting.
	(Move_Zp2_Point, Ins_SHPIX, Ins_DELTAP, Ins_DELTAC)
	[TT_CONFIG_OPTION_COMPILE_UNPATENTED_HINTING]: Implement unpatented
	hinting.
	(TT_RunIns): Updated.

	* src/truetype/ttobjs.c
	[TT_CONFIG_OPTION_COMPILE_UNPATENTED_HINTING]: Include
	FT_TRUETYPE_UNPATENTED_H.
	(tt_face_init) [TT_CONFIG_OPTION_COMPILE_UNPATENTED_HINTING,
	TT_CONFIG_OPTION_FORCE_UNPATENTED_HINTING]: Check
	FT_PARAM_TAG_UNPATENTED_HINTING.

	* src/truetype/ttobjs.h (TT_GraphicsState)
	[TT_CONFIG_OPTION_COMPILE_UNPATENTED_HINTING]: Add `both_x_axis'.

2003-04-25  Werner Lemberg  <<EMAIL>>

	* src/bdf/bdflib.c (hash_bucket, hash_lookup): Use `const' for first
	argument.
	(bdf_get_font_property): Use `const' for third argument.
	Updated all callers.
	* src/bdf/bdfdrivr.c (BDF_Face_Init): Set pixel width and height
	similar to the PCF driver.
	* src/bdf/bdf.h (_hashnode): Use `const' for `key'.
	Updated.

	* src/gzip/ftgzip.c: C++ doesn't like that the array `inflate_mask'
	is declared twice.  It is perhaps better to modify the zlib source
	files directly instead of this hack.
	(zcalloc, zfree, ft_gzip_stream_close, ft_gzip_stream_io): Add casts
	to make build with g++ successful.

2003-04-24  Manish Singh  <<EMAIL>>

	* src/cid/cidobjs.c (cid_face_init), src/type1/t1objs.c
	(T1_Face_Init), src/type42/t42objs.c (T42_Face_Init): Split on `-'
	also for searching the style name.

2003-04-24  David Turner  <<EMAIL>>

	* src/pcf/pcfread.c (pcf_load_font): Fixed the computation of
	face->num_glyphs.  We must increase the value by 1 to respect the
	convention that glyph index 0 always corresponds to the `missing
	glyph'.

2003-04-24  Werner Lemberg  <<EMAIL>>

	* builds/unix/unix-cc.in (CFLAGS): Add @CPPFLAGS@.

2003-04-24  Dieter Baron  <<EMAIL>>

	* builds/unix/freetype-config.in (cflags): Emit FreeType 2's include
	files first.  Otherwise there are conflicts with FreeType 1
	installed simultaneously.

2003-04-23  Werner Lemberg  <<EMAIL>>

	Fixing bugs reported by Nelson Beebe.

	* src/base/ftstroker.c (FT_Stroker_ParseOutline): Remove unused
	variable `in_path'.

	* src/base/ftobjs (ft_glyphslot_set_bitmap): Change type of
	second argument to `FT_Byte*'.
	* include/freetype/internal/ftobjs.h: Updated.

	* src/bdf/bdflib.c (_bdf_readstream): Remove unused variable `res'.
	(_bdf_parse_glyphs): Remove unused variable `next'.
	Mark `call_data' as unused.

	* src/cache/ftlru.c (FT_LruList_Lookup): Remove unused variable
	`plast'.

	* src/pcf/pcfread.c (pcf_seek_to_table_type): Slight recoding to
	actually use `error'.
	(pcf_load_font): Remove unused variable `avgw'.

	* src/pfr/pfrobjs.c (pfr_face_get_kerning): Change return type
	to `void'.
	Mark `error' as unused.
	* src/pfr/pfrobjs.h: Updated.
	* src/pfr/pfrdrivr.c (pfr_get_kerning): Updated.

	* src/sfnt/ttload.c (sfnt_dir_check): Remove unused variable
	`format_tag'.

	* src/sfnt/ttcmap0.c (tt_cmap6_validate, tt_cmap10_validate): Remove
	unused variable `start'.
	(tt_cmap10_char_next): Remove unused variable `result'

	* src/sfnt/sfobjs.c (tt_face_get_name): Mark `error' as unused.

	* src/sfnt/sfdriver.c (get_sfnt_postscript_name): Mark `error' as
	unused.

	* src/type1/t1objs.c (T1_Face_Init): Remove unused variable
	`pshinter'.

	* src/type1/t1gload.c (T1_Load_Glyph): Use `glyph_data_loaded'
	only for FT_CONFIG_OPTION_INCREMENTAL.

2003-04-23  Akito Hirai  <<EMAIL>>

	* src/sfnt/ttcmap0.c (tt_cmap4_validate): Provide a weak variant
	of the glyph ID bounding check if FT_VALIDATE_TIGHT is not active.
	Without this change, many CJK fonts from Dynalab are rejected.

2003-04-23  Joe Marcus Clarke  <<EMAIL>>

	* src/base/ftbdf.c (FT_Get_BDF_Property): Check for valid
	`get_interface'.

2003-04-23  Paul Miller  <<EMAIL>>

	* src/base/ftmac.c (parse_fond): Fix handling of style names.

2003-04-23  Werner Lemberg  <<EMAIL>>

	* src/pfr/pfrload.c (pfr_extra_item_load_font_id): Use FT_PtrDist
	instead of FT_UInt for `len'.

2003-04-22  Werner Lemberg  <<EMAIL>>

	* src/gzip/ftgzip.c (zcalloc) [!FT_CONFIG_OPTION_SYSTEM_ZLIB]:
	Convert K&R format to modern C usage.
	(FT_Stream_OpenGzip): Use long constant.

2003-04-21  Werner Lemberg  <<EMAIL>>

	* src/cache/ftccache.c (ftc_cache_lookup): Remove shadow declaration
	of `manager'.

2003-04-20  Werner Lemberg  <<EMAIL>>

	* doc/INSTALL.UNX: Cleaned up.

2003-04-09  Torrey Lyons  <<EMAIL>>

	* src/base/ftmac.c (open_face_from_buffer): Removed a double-free
	bug that had nasty consequences when trying to open an `invalid'
	font on a Mac.

2003-04-09  Mike Fabian  <<EMAIL>>

	* src/bdf/bdfdrivr.h (BDF_encoding_el), src/pcf/pcf.h
	(PCF_EncodingRec): Changed FT_Short to FT_UShort in order to be able
	to access more than 32768 glyphs in fonts.

2003-04-08  David Turner  <<EMAIL>>


	* Version 2.1.4 released.
	=========================


2003-04-03  Martin Muskens  <<EMAIL>>

	* src/type1/t1load.c (T1_Open_Face): Fixed the code to make it
	handle special cases where a font only contains a `.notdef' glyph
	(happens in PDF-embedded fonts).  Otherwise, FT_Panic was called.

2003-03-27  David Turner  <<EMAIL>>

	* README: Updated.

	* README.UNX: Removed (now replaced by docs/INSTALL.UNX).

	* src/pshinter/pshalgo3.c: The hinter now performs as in 2.1.3 and
	will ignore stem quantization only when FT_LOAD_TARGET_SMOOTH is
	used.
	(psh3_dimension_quantize_len): Enabled.
	(psh3_hint_align): Enable commented code.
	(psh3_hint_align_light): Commented out.

	* src/base/ftobjs.c (FT_Set_Char_Size): Changed the default
	computations to include rounding in all cases; this is required to
	provide accurate kerning data when native TrueType hinting is
	enabled.

	* src/type1/t1load.c (is_name_char): The Type 1 loader now accepts
	more general names according to the PostScript specification (the
	previous one was too restrictive).
	(parse_font_name, parse_encoding, parse_charstrings, parse_dict):
	Use `is_name_char'.
	(parse_subrs): Handle empty arrays.

2003-03-20  David Turner  <<EMAIL>>

	Serious rewriting of the documentation.

	* docs/BUGS, docs/BUILD: Removed.
	* docs/DEBUG.TXT: Renamed to...
	* docs/DEBUG: This.
	* docs/CUSTOMIZE, docs/TRUETYPE, docs/UPGRADE.UNX: New files.
	* docs/INSTALL.ANY, docs/INSTALL.UNX, docs/INSTALL.GNU New files,
	containing platform specific information previously in INSTALL.
	* docs/readme.vms: Renamed to...
	* docs/INSTALL.VMS: This.

	* docs/*: Updated.

	Introduced three new functions to deal with glyph bitmaps within
	FT_GlyphSlot objects:

	  ft_glyphslot_free_bitmap
	  ft_glyphslot_alloc_bitmap
	  ft_glyphslot_set_bitmap

	These functions are much more convenient to use than managing the
	FT_GLYPH_OWN_BITMAP flag manually.

	* include/freetype/internal/ftobjs.h (ft_glyphslot_free_bitmap,
	ft_glyphslot_alloc_bitmap, ft_glyphslot_set_bitmap): New functions.
	* src/base/ftobjs.c: Implement them.
	(ft_glyphslot_done): Use ft_glyphslot_free_bitmap.

	* src/bdf/bdfdrivr.c (BDF_Glyph_Load), src/pcf/pcfdriver.c
	(PCF_Glyph_Load): Remove unused variable `memory'.
	Use `ft_glyphslot_*' functions.
	Don't set `FT_GLYPH_OWN_BITMAP'.

	* src/pfr/pfrsbit.c (pfr_slot_load_bitmap): Use
	`ft_glyphslot_alloc_bitmap'.

	* src/sfnt/ttsbit.c (Load_SBit_Image): Change 5th argument to type
	`FT_GlyphSlot'.
	Adding argument `depth' to handle recursive calls.
	Use `ft_glyphslot_alloc_bitmap'.
	(tt_face_load_sbit_image): Remove unused variable `memory'.
	Don't handle `FT_GLYPH_OWN_BITMAP'.
	Update call to Load_SBit_Image.

	* src/type42/t42objs.c (ft_glyphslot_clear): Renamed to...
	(t42_glyphslot_clear): This.  Updated caller.
	Call `ft_glyphslot_free_bitmap'.

	* src/winfonts/winfnt.c (FNT_Load_Glyph): Use
	`ft_glyphslot_set_bitmap'.
	Don't handle `FT_GLYPH_OWN_BITMAP'.

	* src/cache/ftlru.c (FT_LruList_Lookup): Fixed an invalid assertion
	check.

	* src/autohint/ahglyph.c (ah_outline_load): Add two scaling
	arguments.
	* src/autohint/ahglyph.h: Updated.
	* src/autohint/ahhint.c (ah_hinter_load): Updated.
	* src/autohint/ahglobal.c (ah_hinter_compute_widths): Updated.

	* src/cache/ftccache.c (ftc_family_done): Fixed small bug that could
	crash the cache in rare circumstances (mostly with broken fonts).

2003-03-15  David Turner  <<EMAIL>>

	* src/truetype/ttdriver.c (Set_Char_Sizes): Fixed a small rounding
	bug.  Actually, it seems that previous versions of FreeType didn't
	perform TrueType rounding exactly as appropriate.

2003-03-14  David Turner  <<EMAIL>>

	* src/truetype/ttdriver.c (Set_Char_Sizes): Fixing the small
	TrueType native rendering glitches; they came from a small rounding
	error.

2003-03-13  David Turner  <<EMAIL>>

	Added new environment variables to control memory debugging with
	FreeType.  See the description of `FT2_DEBUG_MEMORY',
	`FT2_ALLOC_TOTAL_MAX' and `FT2_ALLOC_COUNT_MAX' in DEBUG.TXT.

	* src/base/ftdbgmem.c (FT_MemTableRec): Add `alloc_count',
	`bound_total', `alloc_total_max', `bound_count', `alloc_count_max'.
	(ft_mem_debug_alloc): Handle new variables.
	(ft_mem_debug_init): s/FT_DEBUG_MEMORY/FT2_DEBUG_MEMORY/.
	Handle new environment variables.
	* docs/DEBUG.TXT: Updated.

	Fixed the cache sub-system to correctly deal with out-of-memory
	conditions.

	* src/cache/ftccache.c (ftc_node_destroy): Comment out generic
	check.
	(ftc_cache_lookup): Implement loop.
	* src/cache/ftccmap.c: Define FT_COMPONENT.
	* src/cache/ftcsbits.c (ftc_sbit_node_load): Handle
	FT_Err_Out_Of_Memory.
	* src/cache/ftlru.c: Include FT_INTERNAL_DEBUG_H.
	(FT_LruList_Lookup): Implement loop.

	* src/pfr/pfrobjs.c (pfr_face_done): Fix memory leak.
	(pfr_face_init): Fixing compiler warnings.

	* src/psaux/psobjs.c (reallocate_t1_table): Fixed a bug (memory
	leak) that only happened when a try to resize an array would end in
	an out-of-memory condition.

	* src/smooth/ftgrays.c (gray_convert_glyph): Removed compiler
	warnings / volatile bug.

	* src/truetype/ttobjs.c (tt_glyphzone_done): Removed segmentation
	fault that happened in tight memory environments.

2003-02-28  Pixel  <<EMAIL>>

	* src/gzip/ftgzip.c (ft_gzip_file_done): Fixed memory leak: The ZLib
	stream was not properly finalized.

2003-02-25  Anthony Fok  <<EMAIL>>

	* src/cache/ftccmap.c: Include FT_TRUETYPE_IDS_H.
	(ftc_cmap_family_init): The cmap cache now
	supports UCS-4 charmaps when available in Asian fonts.

	* src/sfnt/ttload.c, src/base/ftobjs.c: Changed `asian' to `Asian'
	in comments.

2003-02-25  David Turner  <<EMAIL>>

	* src/gzip/ftgzip.c (ft_gzip_file_fill_output): Fixed a bug that
	caused FreeType to loop endlessly when trying to read certain
	compressed gzip files.  The following test reveals the bug:

	  touch 0123456789 ; gzip 0123456789 ; ftdump 0123456789.gz

	Several fixes to the PFR font driver:

	- The list of available embedded bitmaps was not correctly set in
	  the root FT_FaceRec structure describing the face.

	- The glyph loader always tried to load the outlines when
	  FT_LOAD_SBITS_ONLY was specified.

	- The table loaded now scans for *undocumented* elements of a
	  physical font's auxiliary data record.  This is necessary to
	  retrieve the `real' family and style names.

	NOTE THAT THESE CHANGES THE FAMILY NAME OF MANY PFR FONTS!

	* src/pfr/pfrload.c (pfr_aux_name_load): New function.
	(pfr_phy_font_done): Free `family_name' and `style_name' also.
	Remove unused variables.
	(pfr_phy_font_load): Extract useful information from the auxiliary
	bytes.

	* src/pfr/pfrobjs.c (pfr_face_done): Set pointers to NULL.
	(pfr_face_init): Provide fallback values for `family_name' and
	`style_name'.
	Handle strikes.
	(pfr_slot_load): Handle FT_LOAD_SBITS_ONLY.
	* src/pfr/pfrtypes.h (PFR_PhyFontRec): Add fields `ascent',
	`descent', `leading', `family_name', and `style_name'.

	* src/truetype/ttdriver.c (Set_Char_Sizes): Fixed a rounding bug
	when computing the scale factors for a given character size in
	points with resolution.

	* devel/ft2build.h, devel/ftoption.h: New files (in a new directory)
	which are special development versions of include/ft2build.h and
	include/freetype/config/ftoption.h, respectively.

2003-02-18  David Turner  <<EMAIL>>

	Fixing the slight distortion problem that occurred due to the latest
	auto-hinter changes.

	* src/base/ftobjs.c (ft_recompute_scaled_metrics): Fix rounding.

	* src/truetype/ttdriver.c (Set_Char_Sizes): New variable `metrics2'.
	[!TT_CONFIG_OPTION_BYTECODE_INTERPRETER]: Removed.

	* src/truetype/ttobjs.h (TT_SizeRec): New field `metrics'.
	* src/truetype/ttobjs.c (Reset_Outline_Size): Fix initialization of
	`metrics'.
	[FT_CONFIG_CHESTER_ASCENDER]: Code removed.
	(Reset_SBit_Size): Fix initialization of `metrics'.

	* src/truetype/ttinterp.c (TT_Load_Context): Fix initialization of
	`exec->metrics'.

	* src/autohint/ahhint.c (ah_hinter_load): Disabled the advance width
	`correction' which seemed to provide more trouble than benefits.

2003-02-13  Graham Asher  <<EMAIL>>

	Changed the incremental loading interface in a way that makes it
	simpler and allows glyph metrics to be changed (e.g., by adding a
	constant, as required by CFF fonts) rather than just overridden.
	This was required to make the GhostScript-to-FreeType bridge work.

	* src/cff/cffgload.c (cff_slot_load) [FT_CONFIG_OPTION_INCREMENTAL]:
	Allow metrics to be overridden.
	* src/cid/cidgload.c (cid_load_glyph) [FT_CONFIG_OPTION_INCREMENTAL]:
	Ditto.

	* src/truetype/ttgload.c (load_truetype_glyph)
	[FT_CONFIG_OPTION_INCREMENTAL]: Simplify.
	(compute_glyph_metrics) [FT_CONFIG_OPTION_INCREMENTAL]: Code block
	moved down.

	* src/type1/t1gload.c (T1_Parse_Glyph_And_Get_Char_String)
	[FT_CONFIG_OPTION_INCREMENTAL]: Updated.

	* include/freetype/ftincrem.h: Updated.

2003-01-31  David Turner  <<EMAIL>>

	* docs/CHANGES, docs/VERSION.DLL, docs/TODO: Updating documentation
	for the 2.1.4 release.

	* builds/win32/visualc/freetype.dsp,
	builds/win32/visualc/index.html: Updating the project file for
	2.1.4.

	* src/gzip/adler32.c, src/gzip/ftgzip.c, src/gzip/infblock.c,
	src/gzip/infcodes.c, src/gzip/inflate.c, src/gzip/inftrees.c,
	src/gzip/infutil.c: Removed old-style (K&R)function definitions.
	This avoids warnings with Visual C++ at its most pedantic mode.

	* src/pfr/pfrsbit.c: Removed compiler warnings.

	* src/cache/ftccmap.c (ftc_cmap_family_init): Changed an FT_ERROR
	into an FT_TRACE1 since it caused `ftview' and others to dump too
	much junk when trying to display a waterfall with a font without a
	Unicode charmap (e.g.  SYMBOL.TTF).

	Implemented FT_CONFIG_CHESTER_BLUE_SCALE, corresponding to the last
	patch from David Chester, but with a much simpler (and saner)
	implementation.

	* src/autohint/ahhint.c (ah_hinter_load_glyph)
	[FT_CONFIG_CHESTER_BLUE_SCALE]: Try to optimize the y_scale so that
	the top of non-capital letters is aligned on a pixel boundary
	whenever possible.

	* src/base/ftobjs.c (FT_Set_Char_Size)
	[FT_CONFIG_CHESTER_BLUE_SCALE]: Round differently.
	* src/truetype/ttdriver.c (Set_Char_Sizes)
	[TT_CONFIG_OPTION_BYTECODE_INTERPRETER]: Do some rounding only
	if this macro is defined.

	* src/truetype/ttobjs.c (Reset_Outline_Size)
	[FT_CONFIG_CHESTER_ASCENDER]: Round differently.

	* src/pshinter/pshalgo3.c: Improved the Postscript hinter.  Getting
	rid of stem snapping seems to work well here (though the stems are
	still slightly moved to increase contrast).
	(psh3_dimension_quantize_len): Commented out.
	(psh3_hint_align_light): New function.
	(psh3_hint_align): Comment out some code.

	THIS IMPROVES ANTI-ALIASED RENDERING, BUT MONOCHROME AND LCD MODES
	STILL SUCK.

2003-01-22  David Chester  <<EMAIL>>

	* src/autohint/ahhint.c (ah_compute_stem_width): Small fix to the
	stem width optimization.

2003-01-22  David Turner  <<EMAIL>>

	Adding a new API `FT_Get_BDF_Property' to retrieve the BDF
	properties of a given PCF or BDF font.

	* include/freetype/ftbdf.h (BDF_PropertyType): New enumeration.
	(BDF_Property, BDF_PropertyRec): New structure.
	FT_Get_BDF_Property): New function.
	* include/freetype/internal/bdftypes.h: Include FT_BDF_H.
	(BDF_GetPropertyFunc): New function pointer.

	* src/base/ftbdf.c (test_font_type): New helper function.
	(FT_Get_BDF_Charset_ID): Use `test_font_type'.
	(FT_Get_BDF_Property): New function.

	* src/bdf/bdfdrivr.c: Include FT_BDF_H.
	(bdf_get_bdf_property, bdf_driver_requester): New functions.
	(bdf_driver_class): Use `bdf_driver_requester'.

	* src/pcf/pcfdrivr.c: Include FT_BDF_H.
	(pcf_get_bdf_property, pcf_driver_requester): New functions
	(pcf_driver_class): Use `pcf_driver_requester'.

	* src/pcf/pcfread.c: Include `pcfread.h'.
	(pcf_find_property): Decorate it with FT_LOCAL_DEF.
	* src/pcf/pcfread.h: New file, providing `pcf_find_property'.

	* src/sfnt/ttload.c (sfnt_dir_check): Relaxed the `head' table size
	verification to accept a few broken fonts who pad the size
	incorrectly (the table should be padded, but its `size' field
	shouldn't according to the specification).

2003-01-18  Werner Lemberg  <<EMAIL>>

	* builds/unix/ltmain.sh: Regenerated with `libtoolize --force
	--copy' from libtool 1.4.3.
	* builds/unix/aclocal.m4: Regenerated with `aclocal -I .' from
	automake 1.7.1.
	* builds/unix/configure: Regenerated with autoconf 2.54.
	* builds/unix/config.guess, builds/unix/config.sub: Updated from
	`config' CVS module at subversions.gnu.org.
	* builds/unix/install-sh, builds/unix/mkinstalldirs: Updated from
	`automake' CVS module at subversions.gnu.org.

2003-01-15  David Turner  <<EMAIL>>

	* include/freetype/freetype.h: Fixed documentation for
	FT_Size_Metrics.

2003-01-15  James Su  <<EMAIL>>

	* src/gzip/ftgzip.c (ft_gzip_check_header): Bugfix: couldn't read
	certain gzip-ed font files (typo: `&&' -> `&').

2003-01-15  Huw D M Davies  <<EMAIL>>

	Added a Windows .FNT specific API (mostly for Wine).  Also fixed a
	nasty bug in the header loader which would cause invalid memory
	overwrites.

	* include/freetype/config/ftheader.h (FT_WINFONTS_H): New macro
	for ftwinfnt.h.
	* include/freetype/internal/fnttypes.h: Include FT_WINFONTS_H.
	(FNT_FontRec): Updated.
	Move Windows FNT definition to...
	* include/freetype/ftwinfnt.h: This new file.
	(FT_WinFNT_HeaderRec): Rename `reserved2' to `reserved1'.
	* src/base/ftwinfnt.c: New file, providing `FT_Get_WinFNT_Header'.
	* src/winfonts/winfnt.c (winfnt_header_fields): Updated.
	Rename `reserved2' to `reserved1'.
	(fnt_font_load): Updated.

	* src/base/Jamfile, src/base/descrip.mms, src/base/rules.mk:
	Updated.

2003-01-14  Graham Asher  <<EMAIL>>

	* include/freetype/ftglyph.h, src/base/ftglyph.c: Added `const' to
	the type of the first argument to FT_Matrix_Multiply, which isn't
	changed -- this adds documentation and convenience.

2003-01-13  Graham Asher  <<EMAIL>>

	* src/sfnt/ttload.c (tt_face_load_metrics)
	[FT_CONFIG_OPTION_INCREMENTAL]: TrueType typefaces without
	horizontal metrics (without the `hmtx' table) are now tolerated if
	an incremental interface has been specified that has a
	get_glyph_metrics function, implying that metrics will be supplied
	from outside.  This happens for certain Type 42 fonts passed from
	GhostScript.

2003-01-11  David Chester  <<EMAIL>>

	Patches to the auto-hinter in order to slightly improve the output.
	Note that everything is controlled through the new
	FT_CONFIG_OPTION_CHESTER_HINTS defined in `ftoption.h'.  There are
	also individual FT_CONFIG_CHESTER_XXX macros to control individual
	`features'.

	Note that all improvements are enabled by default, but can be
	tweaked for optimization and testing purposes.  The configuration
	macros will most likely disappear in the short future.

	* include/freetype/config/ftoption.h
	(FT_CONFIG_OPTION_CHESTER_HINTS): New macro.
	(FT_CONFIG_CHESTER_{SMALL_F,ASCENDER,SERIF,STEM,BLUE_SCALE})
	[FT_CONFIG_OPTION_CHESTER_HINTS]: New macros to control individual
	features.

	* src/autohint/ahglobal.c (blue_chars) [FT_CONFIG_CHESTER_SMALL_F]:
	Add blue zone for `fijkdbh'.
	* src/autohint/ahglobal.h (AH_IS_TOP_BLUE)
	[FT_CONFIG_CHESTER_SMALL_F]: Use `AH_BLUE_SMALL_F_TOP'.
	* src/autohint/ahglyph.c (ah_outline_compute_edges)
	[FT_CONFIG_CHESTER_SERIF]: Use `AH_EDGE_SERIF'.
	(ah_outline_compute_blue_edges) [FT_CONFIG_CHESTER_SMALL_F]:
	Increase threshold for `best_dist'.
	* src/autohint/ahhint.c (ah_compute_stem_width)
	[FT_CONFIG_CHESTER_SERIF]: Provide new version for improved serif
	handling.
	(ah_align_linked_edge) [FT_CONFIG_CHESTER_SERIF]: Use special
	version of `ah_compute_stem_width'.
	(ah_hint_edges_3) [FT_CONFIG_CHESTER_STEM]: A new algorithm for stem
	alignment when stem widths are less than 1.5 pixels wide centers the
	stem slightly off-center of the center of a pixel (this increases
	sharpness and consistency).
	[FT_CONFIG_CHESTER_SERIF]: Use special version of
	`ah_compute_stem_width'.
	* src/autohint/ahtypes.h [FT_CONFIG_CHESTER_SMALL_F]: Add
	`AH_BLUE_SMALL_F_TOP'.

2003-01-11  David Turner  <<EMAIL>>

	* include/freetype/internal/fnttypes.h (WinFNT_HeaderRec): Increase
	size of `reserved2' to avoid memory overwrites.

2003-01-08  Huw Davies  <<EMAIL>>

	* src/winfonts/winfnt.c (winfnt_header_fields): Read 16 bytes into
	`reserved2', not `reserved'.

	* src/base/ftobjs.c (find_unicode_charmap): Fixed the error code
	returned when the font doesn't contain a Unicode charmap.  This
	allows FT2 to load `symbol.ttf' and a few others correctly since the
	last release.
	(open_face): Fix return value.

2003-01-08  Owen Taylor  <<EMAIL>>

	Implemented the FT_RENDER_MODE_LIGHT hinting mode in the auto and
	postscript hinters.

	* src/autohint/ahtypes.h (AH_HinterRec): Add `do_stem_adjust'.
	* src/autohint/ahhint.c (ah_compute_stem_width): Handle
	hinter->do_stem_adjust.
	(ah_hinter_load_glyph): Set hinter->do_stem_adjust.

	* src/pshinter/pshalgo3.h (PSH3_GlyphRec): Add `do_stem_adjust'.
	* src/pshinter/pshalgo3.c (psh3_hint_align): Use `do_stem_adjust'.
	(ps3_hints_apply): Handle FT_RENDER_MODE_LIGHT.

	* include/freetype/freetype.h (FT_Render_Mode): Add
	FT_RENDER_MODE_LIGHT.

	* src/truetype/ttgload.c: Fixing the TrueType loader to handle
	invalid composites correctly by limiting the recursion depth.
	(TT_MAX_COMPOSITE_RECURSE): New macro.
	(load_truetype_glyph): Add argument `recurse_count'.
	Load a composite only if the numbers of contours is -1, emit error
	otherwise.
	(TT_Load_Glyph): Updated.

2003-01-08  David Turner  <<EMAIL>>

	* Jamrules, Jamfile, Jamfile.in, src/*/Jamfile: Small changes to
	support the compilation of FreeType 2 as part of larger projects
	with their own configuration options (only with Jam).

2003-01-07  David Turner  <<EMAIL>>

	* src/base/ftstroker.c: Probably the last bug-fixes to the stroker;
	the API is likely to change, however.
	(ft_stroke_border_close): Don't record empty paths.
	(ft_stroke_border_get_counts): Increase `num_points' also in for loop.
	(ft_stroke_border_export): Don't increase `write' twice in for loops.
	(ft_stroker_outside): Handle `phi' together with `theta'.
	(FT_Stroker_ParseOutline): New function.

	* src/base/fttrigon.c (FT_Angle_Diff): Fixing function: It returned
	invalid values for large negative angle differences (resulting in
	incorrect stroker computations, among other things).

	* src/cache/ftccache.c (ftc_node_hash_unlink): Removing incorrect
	assertion, and changing code to avoid hash table size contraction.

	* src/base/Jamfile, src/base/rules.mk, src/base/descrip.mms: Adding
	`ftstroker' to default build, as optional component.

2002-12-26  David Turner  <<EMAIL>>

	* src/gzip/adler32.c, src/gzip/infblock.c, src/gzip/inflate.c,
	src/gzip/inftrees.c, src/gzip/zconf.h, src/gzip/zlib.h,
	src/gzip/zutil.h: Updates to allow compilation without compiler
	warnings with LCC-Win32.

	* include/freetype/freetype.h (FREETYPE_PATCH): Set to 4.
	* builds/unix/configure.ac (version_info): Increased to 9:3:3.
	* builds/unix/configure: Regenerated.
	* docs/VERSION.DLL: Updated.

2002-12-23  Anthony Fok  <<EMAIL>>

	* builds/unix/configure.ac, builds/unix/unix-cc.in (LINK_LIBRARY),
	builds/unix/unix-def.in (SYSTEM_ZLIB): Small fix to configure
	sub-system on Unix to allow other programs to correctly link with
	zlib when needed.

2002-12-19  David Turner  <<EMAIL>>

	* include/freetype/internal/sfnt.h (SFNT_Load_Table_Func): New
	function pointer.

	* include/freetype/tttables.h (FT_Load_Sfnt_Table): New function.
	* src/base/ftobjs.c: Implement it.

	* src/sfnt/sfdriver.c (sfnt_get_interface): Handle `load_sfnt'
	module request.

2002-12-17  David Turner  <<EMAIL>>

	* src/base/ftobjs.c (find_unicode_charmap): Added some comments to
	better explain what's happening there.
	(open_face): Included Graham Asher's fix to prevent faces without
	Unicode charmaps from loading.

	* src/winfonts/winfnt.c: Included George Williams's fix to support
	version 2 fonts correctly.
	(winfnt_header_fields): Updated.
	(fnt_font_load): Handle version 2 fonts.
	(FNT_Load_Glyph): Updated.

2002-12-16  David Turner  <<EMAIL>>

	* docs/VERSION.DLL: Updating document to better explain the
	differences between the three version numbers being used on Unix, as
	well as providing an autoconf fragment provided by Lars Clausen.

	* src/smooth/ftgrays.c (gray_render_conic): Fixed small bug that
	prevented Bézier arcs with negative vertical coordinates to be
	rendered appropriately.

2002-12-02  Antoine Leca  <<EMAIL>>

	* src/base/ftobjs.c: Modified the logic to get Unicode charmaps.
	Now it loads UCS-4 charmaps when there is one.
	(find_unicode_charmap): New function.
	(open_face): Refer to the above one.
	(FT_Select_Charmap): Idem.

2002-11-29  Antoine Leca  <<EMAIL>>

	* include/freetype/ftgzip.h: Correct the name of the controlling
	macro (was __FTXF86_H__ ...).

2002-11-27  Vincent Caron  <<EMAIL>>

	* builds/unix/unix-def.in, builds/unix/freetype-config.in,
	builds/unix/configure.ac, src/gzip/rules.mk, src/gzip/ftgzip.c
	[FT_CONFIG_OPTION_SYSTEM_ZLIB]: Adding support for system zlib
	installations if available on the target platform (Unix only).

2002-11-23  David Turner  <<EMAIL>>

	* src/cff/cffload.c (cff_charset_load, cff_encoding_load): Modified
	charset loader to accept pre-defined charsets, even when the font
	contains fewer glyphs.  Also enforced more checks to ensure that we
	never overflow the character codes array in the encoding.

2002-11-22  Antoine Leca  <<EMAIL>>

	* include/freetype/ttnameid.h: Updated to latest OpenType
	specification.

2002-11-18  David Turner  <<EMAIL>>


	* Version 2.1.3 released.
	=========================


2002-11-07  David Turner  <<EMAIL>>

	* src/cache/ftcsbits.c (ftc_sbit_node_load): Fixed a small bug that
	caused problems with embedded bitmaps.

	* src/otlayout/otlayout.h, src/otlayout/otlconf.h,
	src/otlayout/otlgsub.c, src/otlayout/otlgsub.h,
	src/otlayout/otlparse.c, src/otlayout/otlparse.h,
	src/otlayout/otlutils.h: Updating the OpenType Layout code, adding
	support for the first GSUB lookups.  Nothing that really compiles
	for now though.

	* src/autohint/ahhint.c (ah_align_serif_edge): Disabled serif stem
	width quantization.  It produces slightly better shapes though this
	is not distinguishable with many fonts.
	Remove other dead code.

	* src/Jamfile, src/*/Jamfile: Simplified.
	Use $(FT2_SRC_DIR).

2002-11-06  David Turner  <<EMAIL>>

	* include/freetype/freetype.h (FT_LOAD_TARGET_LIGHT): New macro.
	(FT_LOAD_TARGET, FT_LOAD_TARGET_MODE): Use `& 15' instead of `& 7'.

2002-11-05  David Turner  <<EMAIL>>

	* include/freetype/config/ftoption.h, src/gzip/ftgzip.c: Added
	support for the FT_CONFIG_OPTION_SYSTEM_ZLIB option, used to specify
	the use of system-wide zlib.

	Note that this macro, as well as
	TT_CONFIG_OPTION_BYTECODE_INTERPRETER, is not #undef-ed anymore.
	This allows the build system to define them depending on the
	configuration (typically by adding -D flags at compile time).

	* src/sfnt/ttcmap0.c (tt_face_build_cmaps): Removed compiler
	warnings in optimized mode relative to the `volatile' local
	variables.  This was not a compiler bug after all, but the fact that
	a pointer to a volatile variable is not the same as a volatile
	pointer to a variable :-)

	The fix was to change
	  `volatile FT_Byte* p'
	into
	  `FT_Byte* volatile p'.

	* src/pfr/pfrload.c (pfr_phy_font_load), src/pfr/pfrdrivr.c
	(pfr_get_metrics), src/gzip/inftrees.c: Removed compiler warnings in
	optimized modes.

	* src/gzip/*.[hc]: Modified our zlib copy in order to prevent
	exporting any zlib function names outside of the component.  This
	prevents linking problems on some platforms, when applications want
	to link FreeType _and_ zlib together.

2002-11-05  Juliusz  <<EMAIL>>

	* src/psaux/psobjs.c (ps_table_add): Modified increment loop in
	order to implement exponential behaviour.

2002-11-01  David Turner  <<EMAIL>>

	Added PFR-specific public API.  Fixed the kerning retrieval routine
	(it returned invalid values when the outline and metrics resolution
	differ).

	* include/freetype/ftpfr.h, include/freetype/internal/pfr.h: New
	files.

	* include/freetype/internal/internal.h (FT_INTERNAL_PFR_H): New
	macro for pfr.h.

	* src/base/ftpfr.c: New file.
	* src/base/Jamfile, src/base/descrip.mms: Updated.

	* src/pfr/pfrdrivr.c: Include FT_INTERNAL_PFR_H.
	(pfr_get_kerning, pfr_get_advance, pfr_get_metrics): New functions.
	(pfr_service_rec): New format interface.
	(pfr_driver_class): Use `pfr_service_rec'.
	Replace `pfr_face_get_kerning' with `pfr_get_kerning'.
	* src/pfr/pfrobjs.c: Remove dead code.

	* src/base/ftobjs.c (ft_glyphslot_clear): Small internal fix to
	better support bitmap-based font formats.

	* src/base/ftsynth.c (FT_GlyphSlot_Embolden): Fix handling of
	`scale'.
	Fix arguments to `FT_Vector_From_Polar'.

2002-10-31  David Turner  <<EMAIL>>

	Add support for automatic handling of gzip-compressed PCF files.

	* src/gzip/*: New files, taken from the zlib package (except
	ftgzip.c).

	* include/freetype/ftgzip.h, src/gzip/ftgzip.c: New files.
	* include/freetype/config/ftheader.h (FT_GZIP_H): New macro for
	`ftgzip.h'.

	* src/pcf/pcfdriver.c: Include FT_GZIP_H and FT_ERRORS_H.
	(PCF_Face_Init): If normal open fails, try to open gzip stream.
	(PCF_Face_Done): Close gzip stream.

	* include/freetype/internal/pcftypes.h (PCF_Public_FaceRec),
	src/pcf/pcf.h (PCF_FaceRec): Add `gzip_stream' and `gzip_source'.

	* include/freetype/config/ftoption.h (FT_CONFIG_OPTION_USE_ZLIB):
	New macro.
	(T1_CONFIG_OPTION_DISABLE_HINTER, FT_CONFIG_OPTION_USE_CMAPS
	FT_CONFIG_OPTION_NO_CONVENIENCE_FUNCS,
	FT_CONFIG_OPTION_ALTERNATE_GLYPH_FORMATS): Removed.

	(FT_EXPORT, FT_EXPORT_DEF, FT_DEBUG_LEVEL_ERROR,
	FT_DEBUG_LEVEL_TRACE, FT_DEBUG_MEMORY): Comment out definitions so
	that platform specific configuration file can override.

	* include/freetype/internal/ftstream.h: Include FT_SYSTEM_H.

2002-10-30  David Turner  <<EMAIL>>

	* FreeType 2.1.3rc3 released.

2002-10-25  David Turner  <<EMAIL>>

	* include/freetype/ftcache.h (FT_POINTER_TO_ULONG): New macro.
	(FTC_FACE_ID_HASH): Rewritten, using FT_POINTER_TO_ULONG.

2002-10-22  Giuseppe Ghibò  <<EMAIL>>

	* include/freetype/freetype.h (FT_Encoding): Fix entry for latin-2.

2002-10-07  Werner Lemberg  <<EMAIL>>

	* include/freetype/freetype.h (FT_Open_Face): Use `const' for `args'
	(suggested by Graham).
	* src/base/ftobjs.c (FT_Open_Face): Updated.
	(ft_input_stream_new): Ditto.

2002-10-05  David Turner  <<EMAIL>>

	Adding support for embedded bitmaps to the PFR driver, and rewriting
	its kerning loader/handler to use all kerning pairs in a physical
	font (and not just the first item).

	* src/pfr/pfr.c: Include `pfrsbit.c'.
	* src/pfr/pfrgload.c: Include `pfrsbit.h'.
	* src/pfr/pfrload.c (pfr_extra_item_load_kerning_pairs): Rewritten.
	(pfr_phy_font_done, pfr_phy_font_load): Updated.
	* src/pfr/pfrobjs.c: Include `pfrsbit.h'.
	(pfr_face_init): Handle kerning and embedded bitmaps.
	(pfr_slot_load): Load embedded bitmaps.
	(PFR_KERN_INDEX): Removed.
	(pfr_face_get_kerning): Rewritten.
	* src/pfr/pfrsbit.c, src/pfr/pfrsbit.h: New files.
	* src/pfr/pfrtypes.h (PFR_KernItemRec): New structure.
	(PFR_KERN_INDEX): New macro.
	(PFR_PhyFontRec): Add items for kerning and embedded bitmaps.
	* src/pfr/Jamfile (_sources) [FT2_MULTI]: Add `pfrsbit'.

	* src/base/ftobjs.c (FT_Load_Glyph): Don't load bitmap fonts if
	FT_LOAD_NO_RECURSE is set.
	Load embedded bitmaps only if FT_LOAD_NO_BITMAP isn't set.

	* src/tools/docmaker/content.py, src/tools/docmaker/sources.py,
	src/tools/docmaker/tohtml.py: Fixing a few nasty bugs.

	* src/sfnt/ttcmap0.c (tt_cmap4_validate): The validator for format 4
	sub-tables is now capable of dealing with invalid `length' fields at
	the start of the sub-table.  This allows fonts like `mg______.ttf'
	(i.e.  Marriage) to return accurate charmaps.

	* docs/CHANGES: Updated.

2002-10-05  Werner Lemberg  <<EMAIL>>

	* src/smooth/ftgrays.c (SUBPIXELS): Add cast to `TPos'.
	Update all callers.
	(TRUNC): Add cast to `TCoord'.
	Update all callers.
	(TRaster): Use `TPos' for min_ex, max_ex, min_ey, max_ey, and
	last_ey.
	Update all casts.
	(gray_render_line): Fix casts for `p' and `first'.

2002-10-02  Detlef Würkner  <<EMAIL>>

	* src/bdf/bdflib.c (bdf_load_font): Allocate the _bdf_parse_t
	structure with FT_ALLOC instead of using the stack.

2002-09-27  Werner Lemberg  <<EMAIL>>

	* src/include/freetype/internal/tttypes.h (num_sbit_strikes,
	num_sbit_scales): Use `FT_ULong'.
	* src/sfnt/sfobjs.c (sfnt_load_face): Updated accordingly.
	* src/sfnt/ttsbit.c (tt_face_set_sbit_strike): Ditto.
	(find_sbit_image): Remove cast.
	* src/raster/ftrend1.c (ft_raster1_render): Fix cast.

2002-09-27  Wolfgang Domröse  <<EMAIL>>

	* src/sfnt/ttload.c (tt_face_load_names): Use cast.
	* src/sfnt/ttcmap.c (code_to_next2): Use long constant.
	(code_to_index4): Use cast.
	(code_to_index8_12): Fix cast.
	* src/sfnt/ttcmap0.c (tt_cmap4_char_next, tt_cmap8_char_index,
	tt_cmap12_char_index): Use cast for `result'.
	(tt_face_build_cmaps): Use cast.
	* src/sfnt/sfobjs.c (tt_name_entry_ascii_from_ucs4): Use cast for
	`code'.
	(sfnt_load_face): Use FT_Int32 for `flags'.

	* src/smooth/ftgrays.c (gray_render_scanline, gray_render_line,
	gray_compute_cbox, gray_convert_glyph, gray_raster_reset): Add casts
	to `TCoord' and `int'.
	More 16bit fixes.
	s/FT_Pos/TPos/.
	* src/smooth/ftsmooth.c (ft_smooth_render_generic): Add casts.

2002-09-26  Werner Lemberg  <<EMAIL>>

	* src/sfnt/ttpost.c (load_post_names, tt_face_free_ps_names,
	tt_face_get_ps_name): Replace switch statement with if clauses to
	make it more portable.

	* src/cff/cffobjs.c (cff_face_init): Ditto.

	* include/freetype/ftmodule.h (FT_Module_Class): Use `FT_Long' for
	`module_size'.
	* include/freetype/ftrender.h (FT_Glyph_Class_): Use `FT_Long' for
	`glyph_size'.

	* src/base/ftobjs.c (FT_Render_Glyph): Change second parameter to
	`FT_Render_Mode'.
	(FT_Render_Glyph_Internal): Change third parameter to
	`FT_Render_Mode'.
	* src/base/ftglyph.c (FT_Glyph_To_Bitmap): Change second parameter
	to `FT_Render_Mode'.

	* src/raster/ftrend1.c (ft_raster1_render): Change third parameter
	to `FT_Render_Mode'.
	* src/smooth/ftsmooth.c (ft_smooth_render, ft_smooth_render_lcd,
	ft_smooth_render_lcd_v): Ditto.
	(ft_smooth_render_generic): Change third and fifth parameter to
	`FT_Render_Mode'.

	* include/freetype/freetype.h, include/freetype/internal/ftobjs.h,
	include/freetype/ftglyph.h: Updated.

	* src/cff/cffdrivr.c (Load_Glyph), src/pcf/pcfdriver.c
	(PCF_Glyph_Load), src/pfr/pfrobjs.c (pfr_slot_load),
	src/winfonts/winfnt.c (FNT_Load_Glyph), src/t42/t42objs.c
	(T42_GlyphSlot_Load), src/bdf/bdfdrivr.c (BDF_Glyph_Load): Change
	fourth parameter to `FT_Int32'.

	* src/pfr/pfrobjs.c (pfr_face_init): Add two missing parameters
	and declare them as unused.

	* src/cid/cidparse.h (CID_Parser): Use FT_Long for `postscript_len'.

	* src/psnames/psnames.h (PS_Unicode_Value_Func): Change return
	value to FT_UInt32.
	* src/psnames/psmodule.c (ps_unicode_value, ps_build_unicode_table):
	Updated accordingly.

2002-09-26  Wolfgang Domröse  <<EMAIL>>

	* src/cff/cffdrivr.c (Get_Kerning): Use FT_Long for `middle'.
	(cff_get_glyph_name): Use cast for result of ft_strlen.
	* src/cff/cffparse.c (cff_parse_real): User cast for assigning
	`exp'.
	* src/cff/cffload.c (cff_index_get_pointers): Use FT_ULong for
	some local variables.
	(cff_charset_load, cff_encoding_load): Use casts to FT_UInt for some
	switch statements.
	(cff_font_load): Use cast in call to CFF_Load_FD_Select.
	* src/cff/cffobjs.c (cff_size_init): Use more casts.
	(cff_face_init): Use FT_Int32 for `flags'.
	* src/cff/cffgload.c (cff_operator_seac): Use cast for assigning
	`adx' and `ady'.
	(cff_decoder_parse_charstrings): Use FT_ULong for third parameter.
	Use more casts.
	* src/cff/cffcmap.c (cff_cmap_unicode_init): Use cast for `count'.

	* src/cid/cidload.c (cid_read_subrs): Use FT_ULong for `len'.
	* src/cid/cidgload.c (cid_load_glyph): Add missing cast for
	`cid_get_offset'.

	* src/psaux/t1decode.c (t1_decoder_parse_charstrings) <18>: Use
	cast for `num_points'.
	(t1_decoder_init): Use cast for assigning `decoder->num_glyphs'.

	* src/base/ftdebug.c (ft_debug_init): Use FT_Int.
	* include/freetype/internal/ftdriver.h (FT_Slot_LoadFunc): Use
	`FT_Int32' for fourth parameter.
	* src/base/ftobjs.c (open_face): Use cast for calling
	clazz->init_face.

	* src/raster/ftraster.c (Set_High_Precision): Use `1' instead of
	`1L'.
	(Finalize_Profile_Table, Line_Up, ft_black_init): Use casts.
	* src/raster/ftrend1.c (ft_raster1_render): Ditto.

	* src/sfnt/sfnt_dir_check: Compare `magic' with unsigned long
	constant.

2002-09-26  Detlef Würkner  <<EMAIL>>

	* builds/amiga/include/freetype/config/ftmodule.h: Updated.

2002-09-25  David Turner  <<EMAIL>>

	* src/autohint/ahtypes.h (AH_HINT_METRICS): Disabling metrics
	hinting in the auto-hinter.  This produces much better anti-aliased
	text.

	* docs/CHANGES: Updating the changes documentation.

2002-09-25  Anthony Fok  <<EMAIL>>

	* src/sfnt/ttcmap0.c (tt_cmap4_validate, tt_cmap4_char_index,
	tt_cmap4_char_next): Added support for opens___.ttf (it contains a
	charmap that uses offset=0xFFFFU instead of 0x0000 to indicate a
	missing glyph).

2002-09-21  Wolfgang Domröse  <<EMAIL>>

	* src/truetype/ttdriver.c (Load_Glyph): Fourth parameter must be
	FT_Int32.
	* src/truetype/ttgload.c, src/truetype/ttgload.h (TT_Load_Glyph):
	Ditto.

2002-09-19  Wolfgang Domröse  <<EMAIL>>

	More 16bit fixes.

	* src/autohint/ahglobal.c (sort_values): Use FT_Pos for `swap'.
	(ah_hinter_compute_widths): Use FT_Pos for `dist'.
	Use AH_MAX_WIDTHS.
	* src/autohint/ahglyph.c (ah_outline_scale_blue_edges): Use FT_Pos
	for `delta'.
	(ah_outline_compute_edges): Replace some ints with FT_Int and
	FT_Pos.
	(ah_test_extrema): Clean up code.
	(ah_get_orientation): Use 4 FT_Int variables instead of FT_BBox to
	hold indices.
	* src/autohint/ahtypes.h (AH_SegmentRec): Change type of `score'
	to FT_Pos.

2002-09-19  Werner Lemberg  <<EMAIL>>

	* builds/unix/config.guess, builds/unix/config.sub: Updated to
	recent versions.

2002-09-18  David Turner  <<EMAIL>>

	* src/base/ftobjs.c (FT_Library_Version): Bugfix.

	* FreeType 2.1.3rc2 (release candidate 2) is released!

2002-09-17  David Turner  <<EMAIL>>

	* include/freetype/freetype.h, include/freetype/ftimage.h,
	include/freetype/ftstroker.h, include/freetype/ftsysio.h,
	include/freetype/ftsysmem.h, include/freetype/ttnameid.h: Updating
	the in-source documentation.

	* src/tools/docmaker/tohtml.py: Updating the HTML formatter in the
	DocMaker tool.

	* src/tools/docmaker.py: Removed.

2002-09-17  Werner Lemberg  <<EMAIL>>

	More 16bit fixes.

	* src/psaux/psobjs.c (reallocate_t1_table): Use FT_Long for
	second parameter.

2002-09-16  Werner Lemberg  <<EMAIL>>

	16bit fixes from Wolfgang Domröse.

	* src/type1/t1parse.h (T1_ParserRec): Change type of `base_len'
	and `private_len' to FT_Long.
	* src/type1/t1parse.c (T1_Get_Private_Dict): Remove cast for
	`private_len'.
	* src/type1/t1load.c: Use FT_Int cast for most calls of T1_ToInt.
	Use FT_PtrDist where appropriate.
	(parse_encoding): Use FT_Long for `count' and `n'.
	(read_binary_data): Use FT_Long* for second parameter.
	* src/type1/t1afm.c (afm_atoindex): Use FT_PtrDist.

	* src/cache/ftcsbits.c (ftc_sbit_node_load): Remove unused label.
	* src/pshinter/pshalgo3.c (psh3_hint_align): Remove unused variable.

2002-09-14  Werner Lemberg  <<EMAIL>>

	Making ftgrays.c compile stand-alone again.

	* include/freetype/ftimage.h: Include ft2build.h only if _STANDALONE_
	isn't defined.
	* src/smooth/ftgrays.c [_STANDALONE_]: Define ft_memset,
	FT_BEGIN_HEADER, FT_END_HEADER.
	(FT_MEM_ZERO): Define.
	(TRaster) [GRAYS_USE_GAMMA]: Use `unsigned char' instead of FT_Byte.
	(gray_render_span, gray_init_gamma): Don't use `FT_UInt'.
	Don't cast with `FT_Byte'.
	(grays_init_gamma): Don't use `FT_UInt'.

2002-09-14  Werner Lemberg  <<EMAIL>>

	* src/base/ftinit.c (FT_Add_Default_Modules): Improve error message.
	* src/pcf/pcfdriver.c (PCF_Face_Done): Improve tracing message.
	* include/freetype/config/ftoption.h (FT_MAX_MODULES): Increased
	to 32.

2002-09-10  Werner Lemberg  <<EMAIL>>

	* builds/unix/configure.ac (version_info): Set to 9:2:3.
	* builds/unix/configure: Regenerated.
	* docs/VERSION.DLL: Updated.

2002-09-09  David Turner  <<EMAIL>>

	* src/pshinter/pshalgo2.c (psh2_glyph_find_strong_points),
	src/pshinter/pshalgo3.c (psh3_glyph_find_strong_points): Adding fix
	to prevent segfault when hints are provided in an empty glyph.

	* src/cache/ftccache.i (GEN_CACHE_LOOKUP) [FT_DEBUG_LEVEL_ERROR]:
	Removed conditional code.  This fixes a bug that prevented
	compilation in debug mode of template instantiation.

	* include/freetype/ftimage.h: Removed incorrect `zft_' definitions
	and updated constants documentation comments.

	* src/cff/cffparse.c (cff_parser_run): Fixed the CFF table loader.
	It didn't accept empty arrays, and this prevented the loading of
	certain fonts.

	* include/freetype/freetype.h (FT_FaceRec): Updating documentation
	comment.  The `descender' value is always *negative*, not positive.

2002-09-09  Owen Taylor  <<EMAIL>>

	* src/pcf/pcfdriver.c (PCF_Glyph_Load): Fixing incorrect computation
	of bitmap metrics.

2002-09-08  David Turner  <<EMAIL>>

	Various updates to correctly support subpixel rendering.

	* include/freetype/config/ftmodule.h: Add two renderers for LCD.

	* src/base/ftobjs.c (FT_Load_Glyph): Updated.

	* src/smooth/ftsmooth.c (ft_smooth_render_lcd,
	ft_smooth_render_lcd_v): Set FT_PIXEL_MODE_LCD and
	FT_PIXEL_MODE_LCD_V, respectively.

	* include/freetype/cache/ftcimage.h (FTC_ImageTypeRec): New
	structure.
	Updated all users.
	(FTC_ImageDesc): Removed.
	(FTC_ImageCache_Lookup): Second parameter is now of type
	`FTC_ImageType'.
	Updated all users.
	(FTC_IMAGE_DESC_COMPARE): Updated and renamed to...
	(FTC_IMAGE_TYPE_COMPARE): This.
	(FTC_IMAGE_DESC_HASH): Updated and renamed to...
	(FTC_IMAGE_TYPE_HASH): This.

	* include/freetype/cache/ftcsbits.h (FTC_SBitRec): Field `num_grays'
	replaced with `max_grays'.
	`pitch' is now FT_Short.
	(FTC_SBitCache_Lookup): Second parameter is now of type
	`FTC_ImageType'.
	Updated all users.

	* src/cache/ftcimage.c (FTC_ImageQueryRec, FTC_ImageFamilyRec):
	Updated.
	(ftc_image_node_init): Updated.
	Moved code to convert type flags to load flags to...
	(FTC_Image_Cache_Lookup): This function.
	(ftc_image_family_init): Updated.

	* src/cache/ftcsbits.c (FTC_SBitQueryRec, FTC_SBitFamilyRec):
	Updated.
	(ftc_sbit_node_load): Updated.
	Moved code to convert type flags to load flags to...
	(FTC_SBitCache_Lookup): This function.

	* src/autohint/ahtypes.h (AH_HinterRec): Replace `no_*_hints' with
	`do_*_snapping'.
	Update all users (with negation).
	* src/autohint/ahhint.c (ah_compute_stem_width): Fix threshold for
	`dist' for `delta' < 40.

	* src/pshinter/pshalgo3.h (PSH3_GlyphRec): Replace `no_*_hints' with
	`do_*_snapping'.
	Update all users (with negation).
	* src/pshinter/pshalgo3.c (psh3_dimension_quantize_len): New
	function.
	(psh3_hint_align): Use it.
	Improve hinting code.
	[STRONGER]: Removed.
	(STRONGER): Removed.

	* include/freetype/freetype.h (FT_Set_Hint_Flags, FT_HINT_*):
	Removed.

2002-09-05  Werner Lemberg  <<EMAIL>>

	* src/cid/cidobjs.c (CID_Size_Init): Renamed to...
	(cid_size_init): This.
	* src/psaux/psobjs.c (T1_Builder_Add_Point1): Renamed to...
	(t1_builder_add_point1): This.

	Updated all affected code.

	* src/pshinter/pshalgo3.c (psh3_hint_align): Fix compiler warnings.
	* src/type1/t1gload.c (T1_Compute_Max_Advance): Ditto.

2002-09-04  David Turner  <<EMAIL>>

	* include/freetype/freetype.h: Corrected the definition of
	ft_encoding_symbol to be FT_ENCODING_MS_SYMBOL (instead of
	the erroneous FT_ENCODING_SYMBOL).

	* builds/unix/unix-def.in (datadir): Initialize it (thanks to
	Anthony Fok).

2002-08-29  David Turner  <<EMAIL>>

	Slight modification to the Postscript hinter to slightly increase
	the contrast of smooth hinting.  This is very similar to what the
	auto-hinter does when it comes to stem width computations.  However,
	it produces better results with well-hinted fonts.

	* include/freetype/internal/psaux.h (T1_Decoder_FuncsRec): Add hint
	mode to `init' member function.
	(T1_DecoderRec): Add hint mode.
	* include/freetype/internal/pshints (T1_Hints_ApplyFunc,
	T2_Hints_ApplyFunc): Pass `hint_mode', not `hint_flags'.
	* src/psaux/t1decode.c (t1_decoder_init): Add hint mode argument.
	* src/pshinter/pshalgo1.c (ps1_hints_apply): Pass hint mode, not
	hint flags.
	* src/pshinter/pshalgo2.c (ps2_hints_apply): Ditto.
	* src/pshinter/pshalgo3.c (ps3_hints_apply): Ditto.
	(STRONGER): New macro.
	(psh3_hint_align, psh3_hint_table_align_hints): Pass `glyph' instead
	of `hint_flags'.
	Implement announced changes.
	* src/pshinter/pshalgo3.h (PSH3_GlyphRec): Add flags to control
	vertical and horizontal hints and snapping.

	* README, docs/CHANGES: Updating for the 2.1.3 release.

2002-08-27  David Turner  <<EMAIL>>

	* Massive re-formatting changes to many, many source files.  I don't
	want to list them all here.  The operations performed were all
	logical transformations of the sources:

	- trying to convert all enums and constants to CAPITALIZED_STYLE,
	  #with define definitions like

	    #define my_old_constants   MY_NEW_CONSTANT

	- big, big update of the documentation comments

	* include/freetype/freetype.h, src/base/ftobjs.c,
	src/smooth/ftsmooth.c, include/freetype/ftimage.h: Adding support
	for LCD-optimized rendering though the new constants/enums:

	  FT_RENDER_MODE_LCD, FT_RENDER_MODE_LCD_V
	  FT_PIXEL_MODE_LCD,  FT_PIXEL_MODE_LCD_V

	This is still work in progress, don't expect everything to work
	correctly though most of the features have been implemented.

	* Adding new FT_LOAD_XXX flags, used to specify both hinting and
	rendering targets:

	  FT_LOAD_TARGET_NORMAL :: anti-aliased hinting & rendering
	  FT_LOAD_TARGET_MONO   :: monochrome bitmaps
	  FT_LOAD_TARGET_LCD    :: horizontal RGB/BGR decimated
	                           hinting & rendering
	  FT_LOAD_TARGET_LCD_V  :: vertical RGB/BGR decimated
	                           hinting & rendering

	Note that FT_LOAD_TARGET_NORMAL is 0, which means that the default
	behaviour of the font engine is _unchanged_.

	* include/freetype/ftimage.h
	(FT_Outline_{Move,Line,Conic,Cubic}To_Func): Renamed to...
	(FT_Outline_{Move,Line,Conic,Cubic}ToFunc): This.
	(FT_Raster_Span_Func): Renamed to ...
	(FT_SpanFunc): This.
	(FT_Raster_{New,Done,Reset,Set_Mode,Render}_Func): Renamed to ...
	(FT_Raster_{New,Done,Reset,SetMode,Render}Func}: This.

	Updated all affected code.

	* include/freetype/ftrender.h
	(FT_Glyph_{Init,Done,Transform,BBox,Copy,Prepare}_Func): Renamed
	to ...
	(FT_Glyph_{Init,Done,Transform,GetBBox,Copy,Prepare}Func): This.
	(FTRenderer_{render,transform,getCBox,setMode}): Renamed to ...
	(FT_Renderer_{RenderFunc,TransformFunc,GetCBoxFunc,SetModeFunc}):
	This.

	Updated all affected code.

	* src/autohint/ahtypes.h (AH_Point, AH_Segment, AH_Edge, AH_Globals,
	AH_Face_Globals, AH_Outline, AH_Hinter): These typedefs are now
	pointers to the corresponding `*Rec' structures.  All source files
	have been updated accordingly.

	* src/cff/cffgload.c (cff_decoder_init): Add hint mode as parameter.
	* src/cff/cffgload.h (CFF_Decoder): Add `hint_mode' element.

	* src/cid/cidgload.c (CID_Compute_Max_Advance): Renamed to...
	(cid_face_compute_max_advance): This.
	(CID_Load_Glyph): Renamed to...
	(cid_slot_load_glyph): This.
	* src/cid/cidload.c (CID_Open_Face): Renamed to...
	(cid_face_open): This.
	* src/cid/cidobjs.c (CID_GlyphSlot_{Done,Init}): Renamed to...
	(cid_slot_{done,init}): This.
	(CID_Size_{Get_Globals_Funcs,Done,Reset): Renamed to...
	(cid_size_{get_globals_funcs,done,reset): This.
	(CID_Face_{Done,Init}): Renamed to...
	(cid_face_{done,init}): This.
	(CID_Driver_{Done,Init}: Renamed to...
	(cid_driver_{done,init}: This.
	* src/cid/cidparse.c (CID_{New,Done}_Parser): Renamed to...
	(cid_parser_{new,done}): This.
	* src/cid/cidparse.h (CID_Skip_{Spaces,Alpha}): Renamed to...
	(cid_parser_skip_{spaces,alpha}): This.
	(CID_To{Int,Fixed,CoordArray,FixedArray,Token,TokenArray}): Renamed
	to...
	(cid_parser_to_{int,fixed,coord_array,fixed_array,token,token_array}):
	This.
	(CID_Load_{Field,Field_Table): Renamed to...
	(cid_parser_load_{field,field_table}): This.
	* src/cid/cidriver.c (CID_Get_Interface): Renamed to...
	(cid_get_interface): This.

	Updated all affected code.

	* src/psaux/psobjs.c (PS_Table_*): Renamed to...
	(ps_table_*): This.
	(T1_Builder_*): Renamed to...
	(t1_builder_*): This.
	* src/psaux/t1decode.c (T1_Decoder_*): Renamed to...
	(t1_decoder_*): This.

	* src/psnames/psmodule.c (PS_*): Renamed to...
	(ps_*): This.

	Updated all affected code.

	* src/sfnt/sfdriver (SFNT_Get_Interface): Renamed to...
	(sfnt_get_interface): This.
	* src/sfnt/sfobjs.c (SFNT_*): Renamed to...
	(sfnt_*): This.
	* src/sfnt/ttcmap.c (TT_CharMap_{Load,Free}): Renamed to...
	(tt_face_{load,free}_charmap): This.
	* src/sfnt/ttcmap0.c (TT_Build_CMaps): Renamed to...
	(tt_face_build_cmaps): This.
	* src/sfnt/ttload.c (TT_*): Renamed to...
	(tt_face_*): This.
	* src/sfnt/ttpost.c (TT_Post_Default_Names): Renamed to...
	(tt_post_default_names): This.
	(Load_*): Renamed to...
	(load_*): This.
	(TT_*): Renamed to...
	(tt_face_*): This.
	* src/sfnt/ttsbit.c (TT_*): Renamed to...
	(tt_face_*): This.
	({Find,Load,Crop}_*): Renamed to...
	({find,load,crop}_*): This.

	Updated all affected code.

	* src/smooth/ftsmooth.c (ft_smooth_render): Renamed to...
	(ft_smooth_render_generic): This.
	Make function more generic by adding vertical and horizontal scaling
	factors.
	(ft_smooth_render, ft_smooth_render_lcd, ft_smooth_render_lcd_v):
	New functions.

	(ft_smooth_lcd_renderer_class, ft_smooth_lcdv_renderer_class): New
	classes.

	* src/truetype/ttobjs.c (TT_{Done,New}_GlyphZone): Renamed to...
	(tt_glyphzone_{done,new}): This.
	(TT_{Face,Size,Driver}_*): Renamed to...
	(tt_{face,size,driver}_*): This.
	* src/truetype/ttpload.c (TT_Load_Locations): Renamed to...
	(tt_face_load_loca): This.
	(TT_Load_Programs): Renamed to...
	(tt_face_load_fpgm): This.
	(TT_*): Renamed to...
	(tt_face_*): This.

2002-08-27  Werner Lemberg  <<EMAIL>>

	* docs/VERSION.DLL: New file.

2002-08-23  Graham Asher  <<EMAIL>>

	* src/cff/cffgload.c (cff_operator_seac)
	[FT_CONFIG_OPTION_INCREMENTAL]: Incremental fonts (actually not
	incremental in the case of CFF but just using callbacks to get glyph
	recipes) pass the character code, not the glyph index, to the
	get_glyph_data function; they have no valid charset table.

	* src/cff/cffload.c (cff_font_load): Removed special cases for
	FT_CONFIG_OPTION_INCREMENTAL, which are no longer necessary; CFF
	fonts provided via the incremental interface now have to conform
	more closely to the CFF font format.

	* src/cff/cffload.h (cff_font_load): Removed argument now unneeded.

	* src/cff/cffobjs.c (cff_face_init): Changed call to cff_font_load
	to conform with new signature.

2002-08-22  David Turner  <<EMAIL>>

	* src/base/ftobject.c, src/base/ftsynth.c, src/base/ftstroker.c,
	src/bdf/bdfdrivr.c: Removed compiler warnings.

2002-08-21  Werner Lemberg  <<EMAIL>>

	* src/pshinter/pshalgo3.c (psh3_glyph_compute_inflections,
	psh3_glyph_compute_extrema, psh3_hint_table_find_strong_point): Fix
	compiler warnings and resolve shadowing of local variables.

2002-08-21  David Turner  <<EMAIL>>

	The automatic and Postscript hinter now automatically detect
	inflection points in glyph outlines and treats them specially.  This
	is very useful to prevent nasty effect like the disappearing
	diagonals of `S' and `s' in many, many fonts.

	* src/autohint/ahtypes.h (ah_flag_inflection): New macro.
	* src/autohint/ahangles.c (ah_angle_diff): New function.
	* src/autohint/ahangles.h: Updated.
	* src/autohint/ahglyph.c (ah_outline_compute_inflections): New
	function.
	(ah_outline_detect_features): Use it.
	* src/autohint/ahhint.c (ah_hinter_align_strong_points)
	[!AH_OPTION_NO_WEAK_INTERPOLATION]: Handle inflection.

	* src/tools/docmaker/docmaker.py, src/tools/docmaker/utils.py,
	src/tools/docmaker/tohtml.py: Updating the DocMaker tool.

	* include/freetype/freetype.h: Changing the type of the `load_flags'
	parameter from `FT_Int' to `FT_Int32', this in order to support more
	options.  This should only break binary and/or source compatibility
	on 16-bit platforms (Atari?).
	(FT_LOAD_NO_AUTOHINT): New macro.

	* src/base/ftobjs.c (FT_Load_Glyph): Updated.
	Handle FT_LOAD_NO_AUTOHINT.
	(FT_Load_Char): Updated.

	* src/pshinter/pshalgo3.c, src/base/ftobjs.c, src/base/ftobject.c,
	src/autohint/ahglyph.c, include/freetype/freetype.h: Fixing typos
	and removing compiler warnings.

2002-08-20  Werner Lemberg  <<EMAIL>>

	* src/truetype/ttgload.c (TT_Get_Metrics): Add guard for k = 0.

2002-08-20  David Turner  <<EMAIL>>

	* src/pshinter/pshalgo1.c, src/pshinter/pshalgo2.c,
	src/pshinter/pshglob.c, src/pshinter/pshrec.c,
	src/autohint/ahmodule.c [DEBUG_HINTER]: Removing compiler warnings
	(only used in development builds anyway).

	Improve support of local extrema and stem edge points.

	* src/pshinter/pshalgo3.h (PSH3_Hint_TableRec): Use PSH3_ZoneRec
	for `zones'.
	(PSH3_DIR_UP, PSH3_DIR_DOWN): Exchange values.
	(PSH3_DIR_HORIZONTAL, PSH3_DIR_VERTICAL): New macros.
	(PSH3_DIR_COMPARE, PSH3_DIR_IS_HORIZONTAL, PSH3_IS_VERTICAL): New
	macros.
	(PSH3_POINT_INFLEX): New enum.
	(psh3_point_{is,set}_{off,inflex}): New macros.
	(PSH3_POINT_{EXTREMUM,POSITIVE,NEGATIVE,EDGE_MIN,EDGE_MAX): New
	enum values.
	(psh3_point_{is,set}_{extremum,positive,negative,edge_min,edge_max}):
	New macros.
	(PSH3_PointRec): New members `flags2' and `org_v'.
	(PSH3_POINT_EQUAL_ARG, PSH3_POINT_ANGLE): New macros.

	* src/pshinter/pshalgo3.c [DEBUG_HINTER]: Removing compiler
	warnings.
	(COMPUTE_INFLEXS): New macro.
	(psh3_hint_align): Simplify some basic arithmetic computations.
	(psh3_point_is_extremum): Removed.
	(psh3_glyph_compute_inflections) [COMPUTE_INFLEXS]: New function.
	(psh3_glyph_init) [COMPUTE_INFLEXS]: Use it.
	(psh3_glyph_compute_extrema): New function.
	(PSH3_STRONG_THRESHOLD): Increased to 30.
	(psh3_hint_table_find_strong_point): Improved.
	(psh3_glyph_find_strong_points,
	psh3_glyph_interpolate_strong_points): Updated.
	(psh3_hints_apply): Use psh3_glyph_compute_extrema.

	* test/gview.c (draw_ps3_hint, ps3_draw_control_points): New
	functions.
	Other small updates.

	* Jamfile: Small updates.

2002-08-18  Arkadiusz Miskiewicz  <<EMAIL>>

	* builds/unix/install.mk (install, uninstall): Add $(DESTDIR) to
	make life easier for package maintainers.

2002-08-18  Werner Lemberg  <<EMAIL>>

	* src/pcf/pcfdriver.c (PCF_Glyph_Load): Fix computation of
	horiBearingX.
	* src/bdf/bdfdrivr.c (BDF_Glyph_Load): Fix computation of
	horiBearingY.

2002-08-16  George Williams  <<EMAIL>>

	Add support for Apple composite glyphs.

	* include/freetype/config/ftoption.h
	(TT_CONFIG_OPTION_COMPONENT_OFFSET_SCALED): New macro.

	* src/truetype/ttgload.c (OVERLAP_COMPOUND, SCALED_COMPONENT_OFFSET,
	UNSCALED_COMPONENT_OFFSET): New macros for additional OpenType
	glyph loading flags.
	(load_truetype_glyph): Implement it.

2002-08-16  Werner Lemberg  <<EMAIL>>

	* src/cff/cffgload.c (cff_free_glyph_data),
	src/cff/cffload.c (cff_font_load): Use FT_UNUSED.

2002-08-15  Werner Lemberg  <<EMAIL>>

	* src/base/ftglyph.c (FT_Glyph_To_Bitmap): Initialize `error'.
	* src/sfnt/sfobjs.c (SFNT_Load_Face): Fix compiler warning.

2002-08-15  Graham Asher  <<EMAIL>>

	Implemented the incremental font loading system for the CFF driver.
	Tested using the GhostScript-to-FreeType bridge (under development).

	* src/cff/cffgload.c (cff_get_glyph_data, cff_free_glyph_data): New
	functions.
	(cff_operator_seac, cff_compute_max_advance, cff_slot_load): Use
	them.
	* src/cff/cffload.c (cff_font_load): Add `face' parameter.
	Load charset and encoding only if there are glyphs.
	[FT_CONFIG_OPTION_INCREMENTAL]: Incremental fonts don't need
	character recipes.
	* src/cff/cffload.h, src/cff/cffobjs.c: Updated.

	* src/cid/cidgload.c (cid_load_glyph)
	[FT_CONFIG_OPTION_INCREMENTAL]: Corrected the incremental font
	loading implementation to use the new system introduced on
	2002-08-01.

2002-08-06  Werner Lemberg  <<EMAIL>>

	* src/cff/cffcmap.c: Remove compiler warnings.
	* src/cache/ftccache.c, src/cache/ftccache.i,
	src/pfr/pfrload.c, src/pfr/pfrgload.c: s/index/idx/.
	* src/cff/cffload.c: s/select/fdselect/.
	* src/raster/ftraster.c: s/wait/waiting/.

2002-08-01  Graham Asher  <<EMAIL>>

	* src/type1/t1load.c (T1_Open_Face): Tolerate a face with no
	charstrings if there is an incremental loading interface.  Type 1
	faces supplied by PostScript interpreters like GhostScript will
	typically not provide any charstrings at load time, so this is
	essential if they are to work.

2002-08-01  Graham Asher  <<EMAIL>>

	Modified incremental loading interface to be closer to David's
	preferences.  The header freetype.h is not now affected, the
	interface is specified via an FT_Parameter, the pointer to the
	interface is hidden in an internal part of the face record, and all
	the definitions are in ftincrem.h.

	* include/freetype/freetype.h [FT_CONFIG_OPTION_INCREMENTAL]:
	Removed.
	* include/freetype/internal/ftobjs.h [FT_CONFIG_OPTION_INCREMENTAL]:
	Include FT_INCREMENTAL_H.
	(FT_Face_InternalRec) [FT_CONFIG_OPTION_INCREMENTAL]: Add
	`incremental_interface'.

	* src/base/ftobjs.c (open_face, FT_Open_Face)
	[FT_CONFIG_OPTION_INCREMENTAL]: Updated.
	* src/sfnt/sfobjs.c (SFNT_Load_Face) [FT_CONFIG_OPTION_INCREMENTAL]:
	Updated.

	* src/truetype/ttgload.c (load_truetype_glyph)
	[FT_CONFIG_OPTION_INCREMENTAL]: Updated.
	Free loaded glyph data properly.
	(compute_glyph_metrics, TT_Load_Glyph)
	[FT_CONFIG_OPTION_INCREMENTAL]: Updated.
	* src/truetype/ttobjs.c (TT_Face_Init)
	[FT_CONFIG_OPTION_INCREMENTAL]: Updated.

	* src/type1/t1gload.c (T1_Parse_Glyph_And_Get_Char_String)
	[FT_CONFIG_OPTION_INCREMENTAL]: Updated.
	(T1_Parse_Glyph) [FT_CONFIG_OPTION_INCREMENTAL]: Updated.
	Free loaded glyph data properly.
	(T1_Load_Glyph): Updated.
	[FT_CONFIG_OPTION_INCREMENTAL]: Free loaded glyph data properly.

2002-07-30  David Turner  <<EMAIL>>

	* include/freetype/ftincrem.h: Adding new experimental header file
	to demonstrate a `cleaner' API to support incremental font loading.

	* include/freetype/config/ftheader.h (FT_INCREMENTAL_H): New macro.

	* src/tools/docmaker/*: Adding new (more advanced) version of
	the DocMaker tool, using Python's sophisticated regexps.

2002-07-28  Werner Lemberg  <<EMAIL>>

	s/ft_memset/FT_MEM_SET/.
	s/FT_MEM_SET/FT_MEM_ZERO/ where appropriate.

2002-07-27  Werner Lemberg  <<EMAIL>>

	* src/sfnt/ttload.c (sfnt_dir_check): Make it work with TTCs.

2002-07-26  Werner Lemberg  <<EMAIL>>

	* src/truetype/ttgload.c (load_truetype_glyph)
	[FT_CONFIG_OPTION_INCREMENTAL]: s/memset/ft_memset/.

	* src/autohint/ahhint.c (ah_hint_edges_3): Fix compiler warning.
	* src/cff/cffload.c (cff_encoding_load): Remove `memory' variable.
	* src/cff/cffcmap.c (cff_cmap_encoding_init): Remove `psnames'
	variable.
	* src/truetype/ttgload.c (load_truetype_glyph): Remove statement
	without effect.
	* src/truetype/ttdriver (Get_Char_Index, Get_Next_Char): Removed.

	* src/pshinter/pshalgo3.c (psh3_hint_table_record,
	psh3_hint_table_init, psh3_hint_table_activate_mask): Fix error
	message.

2002-07-24  Graham Asher  <<EMAIL>>

	* src/truetype/ttobjs.c: Fix for bug reported by Sven Neumann
	[<EMAIL>] on the FreeType development forum: `If
	FT_CONFIG_OPTION_INCREMENTAL is undefined (this is the default), the
	TrueType loader crashes in line 852 of src/truetype/ttgload.c when
	it tries to access face->glyph_locations.'

2002-07-18  Graham Asher  <<EMAIL>>

	Added types and structures to support incremental typeface loading.
	The FT_Incremental_Interface structure, defined in freetype.h, is
	designed to be passed to FT_Open_Face to provide callback functions
	to obtain glyph recipes and metrics, for fonts like those passed
	from PostScript that do not necessarily provide all, or any, glyph
	information, when first opened.

	* include/freetype/config/ftoption.h (FT_CONFIG_OPTION_INCREMENTAL):
	New configuration macro to enable incremental face loading.  By
	default it is not defined.

	* include/freetype/freetype.h (FT_Basic_Glyph_Metrics,
	FT_Get_Glyph_Data_Func, FT_Get_Glyph_Metrics_Func,
	FT_Incremental_Interface_Funcs, FT_Incremental_Interface)
	[FT_CONFIG_OPTION_INCREMENTAL]: New.
	(FT_Open_Args, FT_FaceRec) [FT_CONFIG_OPTION_INCREMENTAL]: New field
	`incremental_interface'.
	(FT_Open_Flags) [FT_CONFIG_OPTION_INCREMENTAL]: New enum
	`ft_open_incremental'.

	* include/freetype/fttypes.h: Include FT_CONFIG_CONFIG_H.
	(FT_Data): New structure to represent binary data.

	* src/base/ftobjs.c (open_face) [FT_CONFIG_OPTION_INCREMENTAL]:
	Add parameter for incremental loading.
	(FT_Open_Face) [FT_CONFIG_OPTION_INCREMENTAL]: Use incremental
	interface.

	* src/truetype/ttgload.c (load_truetype_glyph)
	[FT_CONFIG_OPTION_INCREMENTAL]: Added the incremental loading system
	for the TrueType driver.
	(compute_glyph_metrics): Return FT_Error.
	[FT_CONFIG_OPTION_INCREMENTAL]: Check for overriding metrics.
	(TT_Load_Glyph) [FT_CONFIG_OPTION_INCREMENTAL]: Don't look for
	the glyph table while handling an incremental font.
	Get glyph offset.

	* src/truetype/ttobjs.c (TT_Face_Init)
	[FT_CONFIG_OPTION_INCREMENTAL]: Added the incremental loading
	system for the TrueType driver.

	* src/cid/cidgload.c (cid_load_glyph)
	[FT_CONFIG_OPTION_INCREMENTAL]: Added the incremental loading system
	for the CID driver.

	* src/sfnt/sfobjs.c (SFNT_Load_Face) [FT_CONFIG_OPTION_INCREMENTAL]:
	Changes to support incremental Type 42 fonts: Assume a font has
	glyphs if it has an incremental interface object.

	* src/type1/t1gload.c (T1_Parse_Glyph): Renamed to...
	(T1_Parse_Glyph_And_Get_Char_String): This.
	[FT_CONFIG_OPTION_INCREMENTAL]: Added support for incrementally
	loaded Type 1 faces.
	(T1_Parse_Glyph): New function.
	(T1_Load_Glyph): Updated.

2002-07-17  David Turner  <<EMAIL>>

	Cleaning up the cache sub-system code; linear hashing is now the
	default.

	* include/freetype/cache/ftccache.h, src/cache/ftccache.i,
	src/cache/ftccache.c [!FTC_CACHE_USE_LINEAR_HASHING]: Removed.
	(FTC_CACHE_USE_LINEAR_HASHING): Removed also.

	FT_CONFIG_OPTION_USE_CMAPS is now the default.

	* include/freetype/internal/ftdriver.h (FT_Driver_ClassRec): Remove
	`get_char_index' and `get_next_char'.

	* include/freetype/config/ftoption.h,
	include/freetype/internal/tttypes.h, src/base/ftobjs.c,
	src/bdf/bdfdrivr.c, src/cff/cffobjs.c, src/pcf/pcfdrivr.c,
	src/pfr/pfrdrivr.c, src/sfnt/sfobjs.c, src/sfnt/ttcmap0.c,
	src/sfnt/ttcmap0.h, src/sfnt/ttload.c, src/type1/t1objs.c,
	src/type42/t42objs.c, src/winfonts/winfnt.c
	[!FT_CONFIG_OPTION_USE_CMAPS]: Removed.  The new cmap code is now
	the default.

	* src/type42/t42objs.c (T42_CMap_CharIndex, T42_CMap_CharNext):
	Removed.
	* src/type42/t42objs.h: Updated.

	* src/cid/cidriver.c (Cid_Get_Char_Index, Cid_Get_Next_Char):
	Removed.
	(t1cid_driver_class): Updated.
	* src/truetype/ttdriver.c (tt_driver_class): Updated.
	* src/type1/t1driver.c (Get_Char_Index, Get_Next_Char): Removed
	(t1_driver_class): Updated.
	* src/type42/t42drivr.c (t42_driver_class): Updated.

	* src/base/ftobjs.c (open_face): Select Unicode cmap by default.

	* src/sfnt/ttload.c (TT_Load_SFNT_Header): Fixed a recent bug that
	prevented OpenType fonts to be recognized by FreeType.

2002-07-11  David Turner  <<EMAIL>>

	Changing the SFNT loader to check for SFNT-based font files
	differently.  We now ignore the range `helper' fields and check the
	`head' table's magic number instead.

	* include/freetype/internal/tttypes.h (SFNT_HeaderRec): Add `offset'
	field.

	* src/sfnt/ttload.c (sfnt_dir_check): New function.
	(TT_Load_SFNT_HeaderRec): Renamed to...
	(TT_Load_SFNT_Header): This.
	Implement new functionality.
	* src/sfnt/ttload.h: Updated.
	* src/sfnt/sfdriver.c (sfnt_interface): Updated.

	* src/base/ftobject.c, src/base/fthash.c: Updated object sub-system
	and dynamic hash table implementation (still experimental, don't
	use).
	* include/freetype/internal/fthash.h: Updated.
	* include/freetype/internal/ftobjs.h (FT_LibraryRec): New member
	`meta_class'.

	Fixing a bug in the Type 1 loader that prevented valid font bounding
	boxes to be loaded from multiple master fonts.

	* include/freetype/t1tables.h (PS_BlendRec): Add `bboxes' field.

	* include/freetype/internal/psaux.h (T1_FieldType): Add
	`T1_FIELD_TYPE_BBOX'.
	(T1_FieldLocation): Add `T1_FIELD_LOCATION_BBOX'.
	(T1_FIELD_BBOX): New macro.

	* src/psaux/psobjs.c (PS_Parser_LoadField): Handle T1_FIELD_TYPE_BBOX.
	* src/type1/t1load.c (t1_allocate_blend): Create blend->bboxes.
	(T1_Done_Blend): Free blend->bboxes.
	(t1_load_keyword): Handle T1_FIELD_LOCATION_BBOX.
	(parse_font_bbox): Commented out.
	(t1_keywords): Comment out `parse_font_bbox'.
	* src/type1/t1tokens.h: Define `FontBBox' field.

2002-07-10  David Turner  <<EMAIL>>

	* src/cff/cffobjs.c: Small fix to select the Unicode charmap by
	default when needed.
	Small fix to allow OpenType fonts to support Adobe charmaps when
	needed.

	* src/cff/cffcmap.c, src/cff/cffcmap.h: New files to support
	charmaps for CFF fonts.

	* src/cff/cff.c, src/cff/Jamfile, src/cff/rules.mk: Updated.

	* include/freetype/internal/cfftypes.h (CFF_EncodingRec): Use
	fixed-length arrays for `sids' and `codes'.  Add `count' member.
	(CFF_FontRec): Add `psnames' member.

	* src/cff/cffdrivr.c, src/cff/cffload.c, src/cff/cffload.h,
	src/cff/cffobjs.c, src/cff/cffobjs.h, src/cff/cffparse.c,
	src/cffparse.h, src/cff/cffgload.c, src/cff/cffgload.h: Adding
	support for CFF charmaps, reformatting the sources, and removing
	some bugs in the Encoding and Charset loaders.
	Many fonts renamed to use lowercase only:

	  CFF_Builder_Init -> cff_builder_init
	  CFF_Builder_Done -> cff_builder_done
	  CFF_Init_Decoder -> cff_decoder_init
	  CFF_Parse_CharStrings -> cff_decoder_parse_charstrings
	  CFF_Load_Glyph -> cff_slot_load
	  CFF_Init_Decoder -> cff_decoder_init
	  CFF_Prepare_Decoder -> cff_decoder_prepare
	  CFF_Get_Standard_Encoding -> cff_get_standard_encoding
	  CFF_Access_Element -> cff_index_access_element
	  CFF_Forget_Element -> cff_index_forget_element
	  CFF_Get_Name -> cff_index_get_name
	  CFF_Get_String -> cff_index_get_sid_string
	  CFF_Get_FD -> cff_fd_select_get
	  CFF_Done_Charset -> cff_charset_done
	  CFF_Load_Charset -> cff_charset_load
	  CFF_Done_Encoding -> cff_encoding_done
	  CFF_Load_Encoding -> cff_encoding_load
	  CFF_Done_SubFont -> cff_subfont_done
	  CFF_Load_Font -> cff_font_load
	  CFF_Done_Font -> cff_font_done
	  CFF_Size_Get_Globals_Funcs -> cff_size_get_globals_funcs
	  CFF_Size_Done -> cff_size_done
	  CFF_Size_Init -> cff_size_init
	  CFF_Size_Reset -> cff_size_reset
	  CFF_GlyphSlot_Done -> cff_slot_done
	  CFF_GlyphSlot_Init -> cff_slot_init
	  CFF_StrCopy -> cff_strcpy
	  CFF_Face_Init -> cff_face_init
	  CFF_Face_Done -> cff_face_done
	  CFF_Driver_Init -> cff_driver_init
	  CFF_Driver_Done -> cff_driver_done
	  CFF_Parser_Init -> cff_parser_init
	  CFF_Parser_Run -> cff_parser_run

	  add_point -> cff_builder_add_point
	  add_point1 -> cff_builder_add_point1
	  add_contour -> cff_builder_add_contour
	  close_contour -> cff_builder_close_contour
	  cff_explicit_index -> cff_index_get_pointers

2002-07-09  Owen Taylor  <<EMAIL>>

	* src/pshinter/pshglob.c (psh_globals_new): Fixed a bug that
	prevented the hinter from using correct standard width and height
	values, resulting in hinting bugs with certain fonts (e.g. Utopia).

2002-07-07  David Turner  <<EMAIL>>

	* src/base/ftglyph.c (FT_Glyph_To_Bitmap): Added code to return
	successfully when the function is called with a bitmap glyph (the
	previous code simply returned with an error).

	* docs/DEBUG.TXT: Adding debugging support documentation.

	* src/base/ftdebug.c (ft_debug_init), builds/win32/ftdebug.c
	(ft_debug_init), builds/amiga/src/ftdebug.c (ft_debug_init): Changed
	the syntax of the FT2_DEBUG environment variable used to control
	debugging output (i.e. logging and error messages).  It must now
	look like:

	  any:6 memory:4 io:3   or
	  any:6,memory:4,io:3   or
	  any:6;memory:4;io:3

2002-07-07  Owen Taylor  <<EMAIL>>

	* src/pshinter/pshglob.c (psh_blues_snap_stem): Adding support for
	blue fuzz.
	* src/pshinter/pshglob.h (PSH_BluesRec): Add `blue_fuzz' field.
	* src/type1/t1load.c (T1_Open_Face): Initialize `blue_fuzz'.

	Adding support for hinter-specific bit flags, and the new
	FT_Set_Hint_Flags high-level API.

	* include/freetype/freetype.h (FT_Set_Hint_Flags): New function.
	(FT_HINT_NO_INTEGER_STEM, FT_HINT_NO_HSTEM_ALIGN,
	FT_HINT_NO_VSTEM_ALIGN): New macros.

	* include/freetype/internal/ftobjs.h (FT_Face_InternalRec): Add
	`hint_flags' member.

	* src/base/ftobjs.c (FT_Set_Hint_Flags): New function.

	* include/freetype/internal/psaux.h (T1_DecoderRec): Add `hint_flags'
	member.

	* include/freetype/internal/pshints.h (T1_Hints_ApplyFunc,
	T2_Hints_ApplyFunc): Add parameter to pass hint flags.

	* src/psaux/t1decode.c (T1_Decoder_Parse_Charstrings,
	T1_Decoder_Init): Use decoder->hint_flags.
	* src/cff/cffgload.h (CFF_Builder): Add `hint_flags' field.
	* src/cff/cffgload.c (CFF_Builder_Init): Set builder->hint_flags.
	(CFF_Parse_CharStrings): Updated.
	* src/pshinter/pshalgo1.c (ps1_hints_apply): Add parameter to handle
	hint flags (unused).
	* src/pshinter/pshalgo1.h: Updated.
	* src/pshinter/pshalgo2.c (ps2_hints_apply): Add parameter to handle
	hint flags (unused).
	* src/pshinter/pshalgo2.h: Updated.
	* src/pshinter/pshalgo3.c (ps3_hints_apply): Add parameter to handle
	hint flags.
	* src/pshinter/pshalgo3.h: Updated.

2002-07-04  David Turner  <<EMAIL>>

	* src/pfr/pfrobjs.c (pfr_slot_load): Fixed a small bug that returned
	incorrect advances when the outline resolution was different from
	the metrics resolution.

	* src/autohint/ahhint.c: Removing compiler warnings.

	* src/autohint/ahglyph.c: s/FT_MEM_SET/FT_ZERO/ where appropriate.
	(ah_outline_link_segments): Slight improvements to the serif
	detection code.  More work is needed though.

2002-07-03  David Turner  <<EMAIL>>

	Small improvements to the automatic hinter.  Uneven stem-widths have
	now disappeared and everything looks much better, even if there are
	still issues with serifed fonts.

	* src/autohint/ahtypes.h (AH_Globals): Added `stds' array.
	* src/autohint/ahhint.c (OPTIM_STEM_SNAP): New #define.
	(ah_snap_width): Commented out.
	(ah_align_linked_edge): Renamed to...
	(ah_compute_stem_width): This.
	Don't allow uneven stem-widths.
	(ah_align_linked_edge): New function.
	(ah_align_serifed_edge): Don't strengthen serifs.
	(ah_hint_edges_3, ah_hinter_scale_globals): Updated.

2002-07-03  Owen Taylor  <<EMAIL>>

	Adding new algorithm based on Owen Taylor's recent work.

	* src/pshinter/pshalgo3.c, src/pshinter/pshalgo3.h: New files.
	* src/pshinter/pshalgo.h: Updated.
	Use pshalgo3 by default.
	* src/pshinter/pshinter.c: Include pshalgo3.c.

	* src/pshinter/Jamfile, src/pshinter/rules.mk: Updated.

2002-07-01  Owen Taylor  <<EMAIL>>

	* src/pshinter/pshalgo2.c (psh2_glyph_find_strong_points): Fix a bug
	where, if a glyph has more than hint mask, the second mask gets
	applied to points that should have been covered by the first mask.

2002-07-01  Keith Packard  <<EMAIL>>

	* src/sfnt/ttcmap0.c (tt_cmap8_char_next, tt_cmap12_char_next):
	Fixing the cmap 8 and 12 parsing routines.

2002-07-01  David Turner  <<EMAIL>>

	* src/base/ftsynth.c: Include FT_TRIGONOMETRY_H.
	(FT_Outline_Embolden): Renamed to...
	(FT_GlyphSlot_Embolden): This.
	Updated to new trigonometric functions.
	(FT_Outline_Oblique): Renamed to...
	(FT_GlyphSlot_Oblique): This.
	(ft_norm): Removed.
	* include/freetype/ftsynth.h: Updated.

2002-06-26  David Turner  <<EMAIL>>

	* include/freetype/internal/ftobject.h: Updating the object
	sub-system definitions (still experimental).

	* src/base/fthash.c (ft_hash_remove): Fixing a small reallocation
	bug.

	* src/base/fttrigon.c (FT_Vector_From_Polar, FT_Angle_Diff): New
	functions.
	* include/freetype/fttrigon.h: Updated.


	Adding path stroker component (work in progress).

	* include/freetype/ftstroker.h, src/base/ftstroker.c: New files.
	* src/base/Jamfile: Updated.

	* include/freetype/config/ftheader.h (FT_STROKER_H): New macro.


	* src/truetype/ttgload.c (TT_Load_Composite_Glyph),
	src/base/ftoutln.c (FT_Vector_Transform): Fixed Werner's latest fix.
	FT_Vector_Transform wasn't buggy, the TrueType composite loader was.

2002-06-24  Werner Lemberg  <<EMAIL>>

	* include/freetype/freetype.h (FREETYPE_PATCH): Set to 3.

2002-06-21  David Turner  <<EMAIL>>


	* Version 2.1.2 released.
	=========================


2002-06-21  Roberto Alameda  <<EMAIL>>.

	* include/freetype/internal/t42types.h (T42_Font): Removed since
	it is already in t42objs.h.
	(T42_Face): Use T1_FontRec.

	* src/base/fttype1.c (FT_Get_PS_Font_Info): Updated.
	(FT_Has_PS_Glyph_Names): Check for type42 driver name also.
	* src/type42/t42objs.h: Include FT_INTERNAL_TYPE42_TYPES_H.
	(T42_Face): Removed since it is already in t42types.h.

2002-06-21  Detlef Würkner  <<EMAIL>>

	* src/pfrgload.c (pfr_glyph_load_compound): Fix loading of composite
	glyphs.

2002-06-21  Sven Neumann  <<EMAIL>>

	* src/pfr/pfrtypes.h (PFR_KernPair): New structure.
	(PFR_PhyFont): Use it.
	(PFR_KernFlags): New enumeration.
	* src/pfr/pfrload.c (pfr_extra_item_load_kerning_pairs): New
	function.
	(pfr_phy_font_extra_items): Use it.
	(pfr_phy_font_done): Updated.
	* src/pfr/pfrobjs.c (pfr_face_init): Set kerning flag conditionally.
	(pfr_face_get_kerning): New function.
	* src/pfr/pfrobjs.h: Updated.
	* src/pfr/pfrdrivr.c (pfr_driver_class): Updated.

2002-06-21  David Turner  <<EMAIL>>

	* README, docs/CHANGES: Preparing the 2.1.2 release.

2002-06-19  Detlef Würkner  <<EMAIL>>

	* src/base/fttype1.c: Include FT_INTERNAL_TYPE42_TYPES_H.
	(t1_face_check_cast): Removed.
	(FT_Get_PS_Font_Info): Make it work with CID and Type 42 drivers
	also.

2002-06-19  Sebastien BARRE  <http://barre.nom.fr/contact.html#email>

	* src/type42/t42parse.c (t42_parse_sfnts): Fix compiler warning.

2002-06-19  Werner Lemberg  <<EMAIL>>

	* src/base/ftoutln.c (FT_Vector_Transform): Fix serious typo
	(xy <-> yx).
	* src/truetype/ttgload.c (load_truetype_glyph): Replace `|' with
	`||' to make code easier to read.

2002-06-18  Roberto Alameda  <<EMAIL>>.

	* src/type42/t42objs.c (t42_check_size_change): Removed.
	(T42_Size_SetChars, T42_Size_SetPixels): Use FT_Activate_Size
	instead.
	(T42_GlyphSlot_Load): Remove call to t42_check_size_change.

2002-06-18  Detlef Würkner  <<EMAIL>>

	* src/psaux/t1cmap.c (t1_cmap_custom_char_index,
	t1_cmap_custom_char_next): Fix index computation -- indices start
	with 0 and not with cmap->first.

	Provide default charmaps.

	* src/bdf/bdfdrivr.c (BDF_Face_Init), src/pcf/pcfdriver.c
	(PCF_Face_Init), src/pfr/pfrobjs.c (pfr_face_init),
	src/type1/t1objs (T1_Face_Init), src/winfonts/winfnt.c
	(FNT_Face_Init): Implement it.

2002-06-17  Sven Neumann  <<EMAIL>>

	* src/pfr/pfrobjs.c (pfr_face_init): Fix typo.

2002-06-16  Leonard Rosenthol  <<EMAIL>>

	Updated Win32/VC++ projects to include the new PFR driver.

	* builds/win32/visualc/freetype.dsp: Updated.

2002-06-16  Anthony Fok  <<EMAIL>>

	Install freetype2.m4.

	* builds/unix/install.mk (install, uninstall): Handle it.

2002-06-16  Detlef Würkner  <<EMAIL>>

	Same fix for PFR driver.

	* src/pfr/pfrcmap.c (pfr_cmap_char_index, pfr_cmap_char_next):
	Increase return value by 1.
	* src/pfr/pfrobjs.c (pfr_slot_load): Decrease index by 1.

2002-06-15  Detlef Würkner  <<EMAIL>>

	Fix glyph indices to make index zero always the undefined glyph.

	* src/bdf/bdfdrivr.c (bdf_cmap_init): Don't decrease
	cmap->num_encodings.
	(bdf_cmap_char_index, bdf_cmap_char_next, BDF_Get_Char_Index):
	Increase result by 1 for normal cases.
	(BDF_Glyph_Load): Decrease index by 1.

	* src/pcf/pcfdriver.c (pcf_cmap_char_index, pcf_cmap_char_next,
	PCF_Char_Get_Index): Increase result by 1 for normal cases.
	(PCF_Glyph_Load): Decrease index by 1.
	* src/pcf/pcfread.c (pcf_get_encodings): Don't decrease j for
	allocating `encoding'.

	* src/base/ftobjs.c (FT_Load_Glyph, FT_Get_Glyph_Name): Fix
	bounding tests.

2002-06-14  Detlef Würkner  <<EMAIL>>

	Add new cmap support to BDF driver.

	* src/bdf/bdfdrivr.c (BDF_CMapRec) [FT_CONFIG_OPTION_USE_CMAPS]:
	New structure.
	(bdf_cmap_init, bdf_cmap_done, bdf_cmap_char_index,
	bdf_cmap_char_next) [FT_CONFIG_OPTION_USE_CMAPS]: New functions.
	(BDF_Get_Char_Index) [!FT_CONFIG_OPTION_USE_CMAPS]: Use only
	conditionally.
	(BDF_Face_Init): Handle `AVERAGE_WIDTH' and `POINT_SIZE' keywords.
	Implement new cmap handling.
	(bdf_driver_class): Updated.

2002-06-14  Werner Lemberg  <<EMAIL>>

	* Makefile, configure, */*.mk, builds/unix/unix-def.in,
	docs/CHANGES, docs/INSTALL: s/TOP/TOP_DIR/.

2002-06-12  Werner Lemberg  <<EMAIL>>

	* src/bdf/bdflib.c: s/FT_Short/short/ for consistency.

2002-06-11  David Turner  <<EMAIL>>

	* builds/win32/ftdebug.c: Added a missing #endif.

	* src/sfnt/ttload.c, src/bdf/bdflib.c: Removing compiler warnings.

	Removed the bug in Type 42 driver that prevented un-hinted outlines
	to be loaded.

	* src/type42/t42objs.c (T42_Face_Init): Call FT_Done_Size.
	(T42_Size_Init): Call FT_Activate_Size.
	(t42_check_size_change): New function.
	(T42_Size_SetChars, T42_Size_SetPixels): Use it.
	(ft_glyphslot_clear): Replace FT_MEM_SET with FT_ZERO.
	(T42_GlyphSlot_Load): Use t42_check_size_change.
	Initialize more fields of `glyph'.

	* builds/win32/visualc/freetype.dsp: Updated.

2002-06-09  David Turner  <<EMAIL>>


	* Version 2.1.1 released.
	=========================


2002-06-08  Juliusz Chroboczek  <<EMAIL>>

	* include/freetype/internal/ftobjs.h, src/autohint/ahglyph.c,
	src/base/ftobjs.c, src/sfnt/ttcmap0.c, src/smooth/ftgrays.c: Don't
	use `setjmp', `longjmp', and `jmp_buf' but `ft_setjmp', `ft_longjmp',
	and `ft_jmp_buf'.
	Removed direct references to <stdio.h> and <setjmp.h> when
	appropriate, to eventually replace them with a
	FT_CONFIG_STANDARD_LIBRARY_H.  Useful for the XFree86 Font Server
	backend based on FT2.

	* src/base/fttype1.c (FT_Has_PS_Glyph_Names): Fix return value.

2002-06-08  David Turner  <<EMAIL>>

	* src/pcf/pcfdriver.c (pcf_cmap_char_next): Fixed a bug that caused
	the function to return invalid values.

	* src/cache/ftccache.i: Removing a typo that prevented
	the source's compilation.

	* src/cache/ftccache.c (ftc_node_hash_unlink): Fixed a
	bug that caused nasty memory overwrites.  The hash table's
	buckets array wasn't correctly resized when shrunk.

2002-06-08  Detlef Würkner  <<EMAIL>>

	* builds/amiga/smakefile, builds/amiga/makefile: Updated.

2002-06-08  Werner Lemberg  <<EMAIL>>

	* src/cache/ftccache.c (ftc_node_hash_unlink, ftc_node_hash_link)
	[FTC_CACHE_USE_LINEAR_HASHING]: Fix returned error code.
	Fix debugging messages.
	* src/cache/ftccache.i (GEN_CACHE_LOOKUP): Move declaration of
	`family' and `hash' up to make it compilable with g++.

	* src/type42/t42error.h: New file.
	* src/type42/t42drivr.c, src/type42/t42objs.c,
	src/type42/t42parse.c: Use t42 error codes.
	* src/type42/rules.mk: Updated.

	* src/base/ftnames.c: Include FT_INTERNAL_STREAM_H.

2002-06-08  David Turner  <<EMAIL>>

	* src/cache/ftccmap.c: GEN_CACHE_FAMILY_COMPARE,
	GEN_CACHE_NODE_COMPARE, GEN_CACHE_LOOKUP) [FTC_CACHE_USE_INLINE]:
	New macros.
	(ftc_cmap_cache_lookup) [!FTC_CACHE_USE_INLINE]: Typedef to
	ftc_cache_lookup.
	(FTC_CMapCache_Lookup): Updated.

	Adding various experimental optimizations to the cache manager.

	* include/freetype/cache/ftccache.h (FTC_CACHE_USE_INLINE,
	FTC_CACHE_USE_LINEAR_HASHING): New options.
	(FTC_CacheRec) [FTC_CACHE_USE_LINEAR_HASHING]: New elements `p',
	`mask', and `slack'.

	* src/cache/ftccache.c (FTC_HASH_MAX_LOAD, FTC_HASH_MIN_LOAD,
	FTC_HASH_SUB_LOAD) [FTC_CACHE_USE_LINEAR_HASHING,
	FTC_HASH_INITIAL_SIZE]: New macros.
	(ftc_node_mru_link, ftc_node_mru_up): Optimized.
	(ftc_node_hash_unlink, ftc_node_hash_link)
	[FTC_CACHE_USE_LINEAR_HASHING]: New variants.
	(FTC_PRIMES_MIN, FTC_PRIMES_MAX, ftc_primes, ftc_prime_closest,
	FTC_CACHE_RESIZE_TEST, ftc_cache_resize)
	[!FTC_CACHE_USE_LINEAR_HASHING]: Define it conditionally.
	(ftc_cache_init, ftc_cache_clear) [FTC_CACHE_USE_LINEAR_HASHING]:
	Updated.
	(ftc_cache_lookup) [FTC_CACHE_USE_LINEAR_HASHING]: Implement it.

	* src/cache/ftccache.i: New file.

	* src/cache/ftcsbits.c (GEN_CACHE_FAMILY_COMPARE,
	GEN_CACHE_NODE_COMPARE, GEN_CACHE_LOOKUP) [FTC_CACHE_USE_INLINE]:
	New macros.
	(ftc_sbit_cache_lookup) [!FTC_CACHE_USE_INLINE]: Typedef to
	ftc_cache_lookup.
	(FTC_SBitCache_Lookup): Updated.

	* src/type42/t42parse.c: Removing duplicate function.

2002-06-07  Graham Asher  <<EMAIL>>

	* src/base/ftobjs.c (FT_Render_Glyph_Internal): Changed definition
	from FT_EXPORT_DEF to FT_BASE_DEF.

2002-06-07  David Turner  <<EMAIL>>

	Fixed the bug that prevented the correct display of fonts with
	`ftview'.

	* src/type42/t42drivr.c: Split into...
	* src/type42/t42drivr.h, src/type42/t42parse.c,
	src/type42/t42parse.h, src/type42/t42objs.h, src/type42/t42objs.c,
	src/type42/type42.c: New files.

	(t42_get_glyph_name, t42_get_ps_name, t42_get_name_index): Use
	`face->type1'.

	(Get_Interface): Renamed to...
	(T42_Get_Interface): This.
	Updated.
	(T42_Open_Face, T42_Face_Done): Updated.
	(T42_Face_Init): Add new cmap support.
	Updated.
	(T42_Driver_Init, T42_Driver_Done, T42_Size_Init, T42_Size_Done,
	T42_GlyphSlot_Init, T42_GlyphSlot_Done): Updated.
	(Get_Char_Index, Get_Next_Char): Renamed to...
	(T42_CMap_CharIndex, T42_CMap_CharNext): This.
	Updated.
	(T42_Char_Size, T42_Pixel_Size): Renamed to...
	(T42_Size_SetChars, T42_Size_SetPixels): This.
	(T42_Load_Glyph): Renamed to...
	(T42_GlyphSlot_Load): This.

	(t42_init_loader, t42_done_loader): Renamed to...
	(t42_loader_init, t42_loader_done): This.
	(T42_New_Parser, T42_Finalize_Parser): Renamed to...
	(t42_parser_init, t42_parser_done): This.
	(parse_dict): Renamed to...
	(t42_parse_dict): This.
	(is_alpha, is_space, hexval): Renamed to...
	(t42_is_alpha, t42_is_space, t42_hexval): This.
	(parse_font_name, parse_font_bbox, parse_font_matrix,
	parse_encoding, parse_sfnts, parse_charstrings, parse_dict):
	Renamed to...
	(t42_parse_font_name, t42_parse_font_bbox, t42_parse_font_matrix,
	t42_parse_encoding, t42_parse_sfnts, t42_parse_charstrings,
	t42_parse_dict): This.
	Updated.

	(t42_keywords): Updated.

	* src/type42/Jamfile, src/type42/descrip.mms: Updated.

2002-06-03  Werner Lemberg  <<EMAIL>>

	Add 8bpp support to BDF driver.

	* src/bdf/bdflib.c (_bdf_parse_start): Handle 8bpp.
	* src/bdf/bdfdrivr.c (BDF_Glyph_Load): Ditto.
	* src/bdf/README: Updated.

2002-06-02  Detlef Würkner  <<EMAIL>>

	* src/pfr/pfrload.c (pfr_phy_font_done): Free `blue_values' array.

2002-05-29  Detlef Würkner  <<EMAIL>>

	* src/bdf/bdflib.c (_bdf_readstream): Allocate `buf' dynamically.
	(_bdf_parse_glyphs): Use correct size for allocating
	`font->unencoded'.
	(bdf_load_font): Free array conditionally.
	Return proper error code in case of failure.
	* src/bdf/bdfdrivr.c (BDF_Face_Init): Make it more robust against
	unusual fonts.

2002-05-29  Werner Lemberg  <<EMAIL>>

	* src/bdf/descrip.mms, src/type42/descrip.mms: New files.
	* descrip.mms (all): Updated.

	* src/bdf/bdflib.c (_bdf_parse_glyphs): Fix typo which prevented
	compilation.
	* src/pshglob.c (psh_blues_scale_zones): Fix compiler warning.

2002-05-28  Detlef Würkner  <<EMAIL>>

	* builds/amiga/makefile, builds/amiga/smakefile,
	amiga/include/freetype/config/ftmodule.h: Updated to include
	support for BDF and Type42 drivers.

	* docs/modules.txt: Updated.

2005-05-28  David Turner  <<EMAIL>>

	* docs/CHANGES: Updating file for next release (2.1.1).

	* src/bdf/bdflib.c: Removing compiler warnings.

	* include/freetype/ftxf86.h, src/base/ftxf86.c: New files.
	They provide a new API (FT_Get_X11_Font_Format) to retrieve an
	X11-compatible string describing the font format of a given face.
	This was put in a new optional base source file, corresponding to a
	new public header (named FT_XFREE86_H since this function should
	only be used within the XFree86 font server IMO).

	* include/freetype/config/ftheader.h (FT_XFREE86_H): New macro (not
	documented yet).

	* src/base/fttype1.c: New file, providing two new API functions
	(FT_Get_PS_Font_Info and FT_Has_PS_Glyph_Names).
	* include/freetype/t1tables.h: Updated.

	* src/base/Jamfile, src/base/rules.mk, src/base/descrip.mms:
	Updating build control files for the new files `ftxf86.c' and
	`fttype1.c' in src/base.

	* src/pshinter/pshglob.c (psh_blues_scale_zones): Fixed a bug that
	prevented family blue zones substitution from happening correctly.

	* include/freetype/ftbdf.h FT_Get_BDF_Charset_ID): Adding
	documentation comment.

2002-05-28  Werner Lemberg  <<EMAIL>>

	* src/base/ftnames.c (FT_Get_Sfnt_Name): Don't use FT_STREAM_READ_AT
	but FT_STREAM_READ.
	Declare `stream' variable.

	* src/bdf/bdflib.c (_bdf_parse_glyphs): Replace floating point math
	with calls to `FT_MulDiv'.

2002-05-28  David Turner  <<EMAIL>>

	Fixing the SFNT name table loader to support various buggy fonts.
	It now ignores empty name entries, entries with invalid pointer
	Offsets and certain fonts containing tables with broken
	`storageOffset' fields.

	Name strings are now loaded on demand, which reduces the memory
	requirements for a given FT_Face tremendously (for example, the name
	table of Arial.ttf is about 10Kb and contains 70 names).

	This is a temporary fix.  The whole name table loader and interface
	will be rewritten in a much more cleanly way shortly, once CSEH have
	been introduced in the sources.

	* include/freetype/internal/tttypes.h (TT_NameEntryRec): Change
	type of `stringOffset' to FT_ULong.
	(TT_NameTableRec): Change type of `numNameRecords' and
	`storageOffset' to FT_UInt.
	Replace `storage' with `stream'.
	* src/base/ftnames.c (FT_Get_Sfnt_Name): Load name on demand.
	* src/sfnt/sfdriver.c (get_sfnt_postscript_name): Ditto.
	Make code more robust.
	* src/sfnt/sfobjs.c (TT_NameEntry_ConvertFunc): New typedef.
	(tt_face_get_name): Use it.
	Make code more robust.
	* src/sfnt/ttload.c (TT_Load_Names): Use `static' for arrays.
	Handle invalid `storageOffset' data better.
	Set length fields to zero for invalid or ignored data.
	Remove code within FT_DEBUG_LEVEL_TRACE.
	(TT_Free_Names): Updated.

2002-05-24  Tim Mooney  <<EMAIL>>

	* builds/unix/ft-munmap.m4: New file, extracted FT_MUNMAP_DECL and
	FT_MUNMAP_PARAM from aclocal.m4 into here, so aclocal.m4 can be
	rebuilt from sources.  Set macro serial to 1, and use third argument
	to AC_DEFINE for our two custom symbols, so ftconfig.in could one day
	be rebuilt with autoheader (not recommended now, ftconfig.in is a
	custom source file)

2002-05-22  Werner Lemberg  <<EMAIL>>

	* include/freetype/config/ftheader.h (FT_BEZIER_H): Removed.
	(FT_BDF_H): New macro for accessing `ftbdf.h'.

	* src/type42/t42drivr.c (hexval): Fix typo.

2002-05-21  Martin Muskens  <<EMAIL>>

	* src/psaux/psobjs.c (T1Radix): New function.
	(t1_toint): Use it to handle numbers in radix format.

	* src/psaux/t1decode.c (T1_Decoder_Parse_Charstrings): Add dummy
	for undocumented, obsolete opcode 15.

2002-05-21  David Turner  <<EMAIL>>

	* src/bdf/bdflib.c: Removed compiler warning, and changed all tables
	to the `static const' storage specifier (instead of simply
	`static').

	* src/type42/t42drivr.c (hexval): Use more efficient code.
	Removing compiler warnings.
	* src/bdf/bdfdrivr.c: Removing compiler warnings.

	* include/freetype/internal/ftbdf.h, src/base/ftbdf.c,
	src/base/descrip.mms, src/base/Jamfile, src/base/rules.mk
	(FT_Get_BDF_Charset_ID): New API to retrieve BDF-specific strings
	from a face.  This is much cleaner than accessing the internal types
	`BDF_Public_Face' defined in FT_INTERNAL_BDF_TYPES_H.

2002-05-21  Werner Lemberg  <<EMAIL>>

	* src/bdf/README: Mention Microsoft's SBIT tool.

	* src/cff/cffdrivr.c, src/cid/cidriver.c, src/pcf/pcfdriver.c,
	src/truetype/ttdriver.c, src/type1/t1driver.c,
	src/winfonts/winfnt.c, src/type42/t42drivr.c, src/bdf/bdfdrivr.c
	[FT_CONFIG_OPTION_DYNAMIC_DRIVERS]: Completely removed.  It has
	been never used.

2002-05-21  Roberto Alameda  <<EMAIL>>.

	* src/type42/t42drivr.c: s/T42_ENCODING_TYPE_/T1_ENCODING_TYPE_/.
	(parse_font_matrix): Remove unnecessary code.
	(parse_sfnts): Initialize some variables.
	(t42_driver_class) [TT_CONFIG_OPTION_BYTECODE_INTERPRETER]: Use
	ft_module_driver_has_hinter conditionally.
	Moved some type 42 specific structure definitions to...
	* include/freetype/internal/t42types.h: New file.
	* include/freetype/internal/internal.h (FT_INTERNAL_T42_TYPES_H):
	New macro.

2002-05-20  Werner Lemberg  <<EMAIL>>

	* include/freetype/cache/ftcsbits.h (FTC_SBit): Added a new field
	`num_grays' for specifying the number of used gray levels.
	* src/cache/ftcsbits.c (ftc_sbit_node_load): Initialize it.

2002-05-19  Werner Lemberg  <<EMAIL>>

	Adding a driver for BDF fonts written by Francesco Zappa Nardelli
	<<EMAIL>>.  Heavily modified by me to
	better adapt it to FreeType, removing unneeded stuff.  Additionally,
	it now supports Mark Leisher's BDF extension for anti-aliased
	bitmap glyphs with 2 and 4 bpp.

	* src/bdf/*: New driver.
	* include/freetype/internal/bdftypes.h: New file.
	* include/freetype/internal/fttrace.h: Added BDF driver components.
	* include/freetype/fterrdef.h: Added error codes for BDF driver.
	* include/freetype/config/ftmodule.h, src/Jamfile: Updated.
	* include/freetype/internal/internal.h (FT_INTERNAL_BDF_TYPES_H):
	New macro.

	* include/freetype/config/ftstdlib.h (ft_sprintf): New alias for
	sprintf.

2002-05-18  Werner Lemberg  <<EMAIL>>

	* include/freetype/internal/fttrace.h: Added Type 42 driver
	component.
	* src/type42/t42drivr.c: Use it.

	* include/freetype/internal/internal.h (FT_INTERNAL_PCF_TYPES_H):
	New macro.

2002-05-17  Werner Lemberg  <<EMAIL>>

	* src/type42/Jamfile: New file.

2002-05-14  Werner Lemberg  <<EMAIL>>

	Adding a driver for Type42 fonts written by Roberto Alameda
	<<EMAIL>>.

	* src/type42/*: New driver.
	* include/freetype/config/ftmodule.h, src/Jamfile: Updated.
	* include/freetype/config/ftstdlib.h (ft_xdigit, ft_memcmp,
	ft_atoi): New aliases for xdigit, memcmp, and atoi, respectively.

2002-05-12  Owen Taylor  <<EMAIL>>

	* src/sfnt/ttload.c (TT_LookUp_Table): Protect against tables
	with a zero length value.

2002-05-12  Michael Pfeiffer  <<EMAIL>>

	* builds/beos/beos.mk: Include `link-std.mk'.

2002-05-12  Werner Lemberg  <<EMAIL>>

	* src/type1/t1load.h (T1_Loader): Renamed to...
	(T1_LoaderRec): This.
	(T1_Loader): Now pointer to T1_LoaderRec.
	* src/type1/t1load.c: Updated.

	* include/freetype/internal/t1types.h, src/type1/t1load.c,
	src/type1/t1objs.c:
	s/T1_ENCODING_TYPE_EXPORT/T1_ENCODING_TYPE_EXPERT/.

2002-05-06  Werner Lemberg  <<EMAIL>>

	* README: Add a note regarding libttf vs. libfreetype.

2002-05-05  Werner Lemberg  <<EMAIL>>

	FreeType 2 can now be built in an external directory with the
	configure script also.

	* builds/freetype.mk (INCLUDES): Add `OBJ_DIR'.

	* builds/unix/detect.mk (have_mk): New variable to test for
	external build.
	(unix-def.mk): Defined according to value of `have_mk'.
	* builds/unix/unix.mk (have_mk): New variable to test for
	external build.
	Select include paths for unix-def.mk and unix-cc.mk according
	to value of `have_mk'.
	* builds/unix/unix-def.in (OBJ_BUILD): New variable.
	(DISTCLEAN): Use it.
	* builds/unix/unix-cc.in (LIBTOOL): Define default value only
	if not yet defined.
	* builds/unix/install.mk (install): Use `OBJ_BUILD' for installing
	freetype-config.

	* configure: Don't depend on bash features.
	(ft2_dir, abs_curr_dir, abs_ft2_dir): New variables (code
	partially taken from Autoconf).
	Build a dummy Makefile if not building in source tree.

	* docs/INSTALL: Document it.

2002-05-04  David Turner  <<EMAIL>>

	* src/truetype/ttgload.c (TT_Load_Glyph): Finally fixing the last
	bug that prevented FreeType 2.x and FreeType 1.x to produce
	bit-by-bit identical monochrome glyph bitmaps with native TrueType
	hinting.  The culprit was a single-bit flag that wasn't set
	correctly by the TrueType glyph loader.

	* src/otlayout/otlayout.h, src/otlayout/otlbase.c,
	src/otlayout/otlbase.h, src/otlayout/otlconf.h,
	src/otlayout/otlgdef.c, src/otlayout/otlgdef.h,
	src/otlayout/otlgpos.c, src/otlayout/otlgpos.h,
	src/otlayout/otlgsub.c, src/otlayout/otlgsub.h,
	src/otlayout/otljstf.c, src/otlayout/otljstf.h,
	src/otlayout/otltable.c, src/otlayout/otltable.h,
	src/otlayout/otltags.h: New OpenType Layout source files.  The
	module is still incomplete.

2002-05-02  Werner Lemberg  <<EMAIL>>

	* src/sfnt/ttcmap0.c (tt_cmap4_char_index): Fix serious typo
	(0xFFFU -> 0xFFFFU).

2002-05-01  Werner Lemberg  <<EMAIL>>

	* docs/INSTALL: Fix URL of makepp.

2002-05-01  David Turner  <<EMAIL>>

	* src/sfnt/sfobjs.c (tt_face_get_name): Fixing a bug that caused
	FreeType to crash when certain broken fonts (e.g. `hya6gb.ttf')
	were opened.

	* src/sfnt/ttload.c (TT_Load_Names): Applied a small work-around to
	manage fonts containing a broken name table (e.g. `hya6gb.ttf').

	* src/sfnt/ttcmap0.c (tt_cmap4_validate): Fixed over-restrictive
	validation test.  The charmap validator now accepts overlapping
	ranges in format 4 charmaps.

	* src/sfnt/ttcmap0.c (tt_cmap4_char_index): Switched to a binary
	search algorithm.  Certain fonts contain more than 170 distinct
	segments!

	* include/freetype/config/ftstdlib.h: Adding an alias for the `exit'
	function.  This will be used in the near future to panic in case of
	unexpected exception (which shouldn't happen in theory).

	* include/freetype/internal/fthash.h, src/base/fthash.c: New files.
	This is generic implementation of dynamic hash tables using a linear
	algorithm (to get rid of `stalls' during resizes).  In the future
	this will be used in at least three parts of the library: the cache
	sub-system, the object sub-system, and the memory debugger.

	* src/base/Jamfile: Updated.

	* include/freetype/internal/internal.h (FT_INTERNAL_HASH_H,
	FT_INTERNAL_OBJECT_H): New macros.

	* include/freetype/internal/ftcore.h: New file to group all new
	definitions related to exception handling and memory management.  It
	is very likely that this file will disappear or be renamed in the
	future.

	* include/freetype/internal/ftobject.h, include/freetype/ftsysmem.h:
	Adding comments to better explain the object sub-system as well as
	the new memory manager interface.

2002-04-30  Wenlin Institute (Tom Bishop)  <<EMAIL>>

	* src/base/ftmac.c (p2c_str): Removed.
	(file_spec_from_path) [TARGET_API_MAC_CARBON]: Added support for
	OS X.
	(is_dfont) [TARGET_API_MAC_CARBON]: Define only for OS X.
	Handle `nameLen' <= 6 also.
	(parse_fond): Remove unused variable `name_table'.
	Use functionality of old p2c_str directly.
	Add safety checks.
	(read_lwfn): Initialize `size_p'.
	Check for size_p == NULL.
	(new_memory_stream, open_face_from_buffer): Updated to FreeType 2.1.
	(FT_New_Face_From_LWFN): Remove unused variable `memory'.
	Remove some dead code.
	(FT_New_Face_From_SFNT): Remove unused variable `stream'.
	(FT_New_Face_From_dfont) [TARGET_API_MAC_CARBON]: Define only for
	OS X.
	(FT_New_Face_From_FOND): Remove unused variable `error'.
	(ResourceForkSize): New function.
	(FT_New_Face): Use it.
	Handle empty resource forks.
	Conditionalize some code for OS X.
	Add code to call normal loader as a fallback.

2002-04-30  Werner Lemberg  <<EMAIL>>

	`interface' is reserved on the Mac.

	* include/freetype/ftoutln.h, include/freetype/internal/sfnt.h,
	src/base/ftoutln.c: s/interface/func_interface/.
	* src/base/ftbbox.c (FT_Outline_Get_BBox):
	s/interface/bbox_interface/.
	* src/cff/cffdrivr.c: s/interface/module_interface/.
	* src/cff/cffload.c, src/cff/cffload.h:
	s/interface/psnames_interface/.
	* src/cid/cidriver.c: s/interface/cid_interface/.
	* src/sfnt/sfdriver.c: s/interface/module_interface/.
	* src/smooth/ftgrays.c: s/interface/func_interface/.
	* src/truetype/ttdriver.c: s/interface/tt_interface/.
	* src/type1/t1driver.c: s/interface/t1_interface/.

	Some more variable renames to avoid troubles on the Mac.

	* src/raster/ftraster.c:
	s/Unknown|Ascending|Descending|Flat/\1_State/.
	* src/smooth/ftgrays.c: s/TScan/TCoord/.

	Other changes for the Mac.

	* include/freetype/config/ftconfig.h: Define FT_MACINTOSH for
	Mac platforms.
	* src/base/ftobjs.c: s/macintosh/FT_MACINTOSH/.

	* src/raster/ftrend1.c (ft_raster1_render): Make `pitch' always
	an even number.

2002-04-29  Jouk Jansen  <<EMAIL>>

	* descrip.mms (all): Add pfr driver.

2002-04-28  Werner Lemberg  <<EMAIL>>

	* src/pfr/pfrerror.h: New file.
	* include/freetype/ftmoderr.h: Add PFR error codes.
	* src/pfr/pfrgload.c: Include pfrerror.h.
	Use PCF error codes.
	(pfr_extra_item_load_stem_snaps): Fix debug message.
	* src/pfr/pfrgload.c: Include pfrerror.h.
	Use PCF error codes.
	(pfr_extra_item_load_bitmap_info, pfr_glyph_load_simple,
	pfr_glyph_load_compound): Fix debug message.
	* src/pfr/pfrobjs.c: Include pfrerror.h.
	Use PCF error codes.
	(pfr_face_init): Return PFR_Err_Unknown_File_Format.
	* src/pfr/rules.mk (PFR_DRV_H): Include pfrerror.h.

	* src/pcf/pcfdriver.c (PCF_Face_Init) [!FT_CONFIG_OPTION_USE_CMAPS]:
	`root' -> `face->root'.
	* src/sfnt/ttcmap0.c (TT_Build_CMaps) [!FT_CONFIG_OPTION_USE_CMAPS]:
	Removed.
	* src/sfnt/ttcmap0.c: Declare TT_Build_CMaps only for
	FT_CONFIG_OPTION_USE_CMAPS.

2002-04-27  Werner Lemberg  <<EMAIL>>

	* src/cache/ftccache.c (ftc_cache_lookup),
	src/cache/ftccmap.c (ftc_cmap_family_init),
	src/cache/ftcmanag.c (ftc_family_table_alloc),
	src/cache/ftcsbits.c (FTC_SBit_Cache_Lookup): Use FTC_Err_*.
	src/cache/ftcimage.c (FTC_Image_Cache_Lookup): Use FTC_Err_*.
	(FTC_ImageCache_Lookup): Fix handling of invalid arguments.

2002-04-22  Werner Lemberg  <<EMAIL>>

	* builds/unix/configure.ac: Set `version_info' to 9:1:3 (FT2
	version 2.0.9 has 9:0:3).
	* builds/unix/configure: Regenerated (using autoconf 2.53).

2002-04-19  Werner Lemberg  <<EMAIL>>

	* src/pfr/pfrload.c (pfr_extra_items_parse): Fix debug message.
	(pfr_phy_font_load): s/size/Size/ for local variable to avoid
	compiler warning.
	* src/pfr/pfrobjs.c (pfr_face_init): Fix debug message.
	(pfr_slot_load): Remove redundant local variable.

2002-04-19  David Turner  <<EMAIL>>

	Adding a PFR font driver to the FreeType sources.  Note that it
	doesn't support embedded bitmaps or kerning tables yet.

	src/pfr/*: New files.

	* include/freetype/config/ftmodule.h,
	include/freetype/internal/fttrace.h, src/Jamfile: Updated.

	* src/type1/t1gload.h (T1_Load_Glyph), src/type1/t1gload.c
	(T1_Load_Glyph): Fixed incorrect parameter sign-ness in callback
	function.

	* include/freetype/internal/ftmemory.h (FT_MEM_ZERO, FT_ZERO): New
	macros.

	* include/freetype/internal/ftstream.h (FT_NEXT_OFF3, FT_NEXT_UOFF3,
	FT_NEXT_OFF3_LE, FT_NEXT_UOFF3_LE): New macros to parse in-memory
	24-bit integers.

2002-04-18  David Turner  <<EMAIL>>

	* src/base/ftobjs.c, builds/win32/ftdebug.c,
	builds/amiga/src/base/ftdebug.c: Version 2.1.0 couldn't be linked
	against applications in Win32 and Amiga builds due to changes to
	`src/base/ftdebug.c' that were not properly propagated to
	`builds/win32' and `builds/amiga'.  This has been fixed.

	* include/freetype/internal/ftobject.h,
	include/freetype/internal/ftexcept.h, include/freetype/ftsysmem.h,
	include/freetype/ftsysio.h, src/base/ftsysmem.c, src/base/ftsysio.c:
	New experimental files.

2002-04-17  David Turner  <<EMAIL>>


	* Version 2.1.0 released.
	=========================


2002-04-17  Michael Jansson  <<EMAIL>>

	* src/type1/t1gload.c (T1_Compute_Max_Advance): Fixed a small bug
	that prevented the function to return the correct value.

2002-04-16  Francesco Zappa Nardelli  <<EMAIL>>

	* src/pcf/pcfread.c (pcf_get_accel): Fix parsing of accelerator
	tables.

2002-04-15  David Turner  <<EMAIL>>

	* docs/FTL.txt: Formatting.

	* include/freetype/config/ftoption.h: Reduce the size of the
	render pool from 32kByte to 16kByte.

	* src/pcf/pcfread.c (pcf_seek_to_table_type): Remove compiler
	warning.

	* include/freetype/config/ftoption.h (FT_MAX_EXTENSIONS): Removed.

	* docs/CHANGES: Preparing 2.1.0 release.

2002-04-13  Werner LEMBERG  <<EMAIL>>

	* src/cff/cffgload.c (CFF_Parse_CharStrings): s/rand/Rand/ to avoid
	compiler warning.

2002-04-12  David Turner  <<EMAIL>>

	* README.UNX: Updated the Unix-specific quick-compilation guide to
	warn about the GNU Make requirement at compile time.

	* include/freetype/config/ftstdlib.h,
	include/freetype/config/ftconfig.h,
	include/freetype/config/ftheader.h,
	include/freetype/internal/ftmemory.h,
	include/freetype/internal/ftobjs.h,

	src/autohint/ahoptim.c,

	src/base/ftdbgmem.c, src/base/ftdebug.c, src/base/ftmac.c,
	src/base/ftobjs.c, src/base/ftsystem.c,

	src/cache/ftcimage.c, src/cache/ftcsbits.c,

	src/cff/cffdrivr.c, src/cff/cffload.c, src/cff/cffobjs.c,

	src/cid/cidload.c, src/cid/cidparse.c, src/cid/cidriver.c,

	src/pcf/pcfdriver.c, src/pcf/pcfread.c,

	src/psaux/t1cmap.c, src/psaux/t1decode.c,

	src/pshinter/pshalgo1.c, src/pshinter/pshalgo2.c,
	src/pshinter/pshrec.c,

	src/psnames/psmodule.c,

	src/raster/ftraster.c,

	src/sfnt/sfdriver.c, src/sfnt/ttload.c,

	src/smooth/ftgrays.c,

	src/type1/t1afm.c, src/type1/t1driver.c, src/type1/t1gload.c,
	src/type1/t1load.c, src/type1/t1objs.c, src/type1/t1parse.c,

	builds/unix/ftconfig.in, builds/vms/ftconfig.h,

	builds/amiga/src/base/ftdebug.c:

	Added the new configuration file `ftstdlib.h' used to define
	aliases for all ISO C library functions used by the engine
	(e.g. strlen, qsort, setjmp, etc.).

	This eases the porting of FreeType 2 to environments like
	XFree86 modules/extensions.

	Also removed many #include <string.h>, #include <stdlib.h>, etc.
	from the engine's sources where they are not needed.

	* src/sfnt/ttpost.c: Use macro name for psnames.h.

2002-04-12  Vincent Caron  <<EMAIL>>

	* configure, builds/detect.mk: Updated the build system to print
	a warning message in case GNU Make isn't used to build the library.

2002-04-11  David Turner  <<EMAIL>>

	* README, docs/CHANGES, Jamfile.in: Updates for the 2.1.0 release.

	* docs/FTL.txt: Updated license text to provide a preferred
	disclaimer and adjust copyright dates/extents.

	* include/freetype/cache/ftcglyph.h: Removing obsolete (and
	confusing) comment.

	* Jamfile.in: New file.

2002-04-11  Maxim Shemanarev  <<EMAIL>>

	* src/smooth/ftgrays.c (gray_hline): Minor optimization.

2002-04-02  Werner Lemberg  <<EMAIL>>

	Fixes from the stable branch:

	* include/freetype/config/ftoption.h (FT_CONFIG_OPTION_OLD_CALCS):
	Removed.
	[FT_CONFIG_OPTION_OLD_CALCS]: Removed.
	* include/freetype/internal/ftcalc.h, src/base/ftcalc.c
	[FT_CONFIG_OPTION_OLD_CALCS]: Removed.

	* src/base/fttrigon.c (FT_Vector_Length): Change algorithm to match
	output of FreeType 1.

	* src/pshinter/pshglob.c (psh_globals_scale_widths): Fixed a small
	bug that created un-even stem widths when hinting Postscript fonts.

	* src/type1/t1driver.c, src/type1/t1parse.c: 16bit fixes.

2002-04-01  Werner Lemberg  <<EMAIL>>

	* src/truetype/ttgload.c: 16bit fixes.
	(TT_Load_Simple_Glyph): Improve debug messages.
	(load_truetype_glyph): Remove dead code.
	* src/truetype/ttinterp.c: 16bit fixes.
	* src/truetype/ttobjs.c: Ditto.

	* include/freetype/ftsnames.h, include/freetype/internal/sfnt.h,
	src/cff/cffload.h, src/psaux/psobjs.h, src/truetype/ttinterp.[ch],
	src/sfnt/ttpost.h: s/index/idx/.

2002-03-31  Yao Zhang  <<EMAIL>>

	* src/truetype/ttobjs.c (TT_Size_Init): Fix typo.

2002-03-31  Werner Lemberg  <<EMAIL>>

	* src/otlayout/otlcommn.c, src/otlayout/otlcommn.h: s/index/idx/.
	* src/psaux/t1cmap.c: Ditto.
	* src/sfnt/ttcmap0.c: Ditto.

	* include/freetype/internal/tttypes.h,
	include/freetype/internal/sfnt.h (TT_Goto_Table_Func): Renamed to ...
	(TT_Loader_GotoTableFunc): This.
	* src/psaux/t1decode.c (T1_Decoder_Parse_Charstrings): Fix debug
	messages.
	* src/psnames/psmodule.c (psnames_interface)
	[!FT_CONFIG_OPTION_ADOBE_GLYPH_LIST]: Fix typo.
	* src/sfnt/sfdriver.c (get_sfnt_table): 16bit fix.
	* src/sfnt/ttcmap.c: 16bit fixes (0xFFFF -> 0xFFFFU).
	* src/sfnt/ttcmap0.c: 16bit fixes.
	(TT_Build_CMaps): Simplify debug messages.
	(tt_cmap12_char_next): Fix offset.
	* src/sfnt/ttload.c (TT_Load_Names, TT_Load_CMap): Fix debug
	messages.
	(TT_Load_OS2): 16bit fix.

2002-03-30  David Turner  <<EMAIL>>

	* include/freetype/internal/tttypes.h: Adding comments to some of
	the TT_FaceRec fields.

	* src/sfnt/ttcmap0.c (TT_Build_CMaps): Removed compiler warnings.

	* src/sfnt/sfobjs.c (tt_name_entry_ascii_from_{utf16,ucs4,other}:
	New functions.
	(tt_face_get_name): Use them to properly extract an ascii font name.

2002-03-30  Werner Lemberg  <<EMAIL>>

	* include/freetype/t1tables.h (t1_blend_max): Fix typo.
	* src/base/ftstream.c: Simplify FT_ERROR calls.
	* src/cff/cffdrivr.c (cff_get_glyph_name): Fix debug message.

	* src/cff/cffobjs.c (CFF_Driver_Init, CFF_Driver_Done)
	[TT_CONFIG_OPTION_EXTEND_ENGINE]: Removed.
	* src/cff/sfobjs.c (SFNT_Load_Face)
	[TT_CONFIG_OPTION_EXTEND_ENGINE]: Ditto.
	* src/truetype/ttobjs.c (TT_Init_Driver, TT_Done_Driver)
	[TT_CONFIG_OPTION_EXTEND_ENGINE]: Ditto.

	* src/truetype/ttdriver.c, src/truetype/ttobjs.c,
	src/truetype/ttobjs.h: Renaming driver functions to the
	FT_<Subject>_<Action> scheme:

	  TT_Init_Driver => TT_Driver_Init
	  TT_Done_Driver => TT_Driver_Done
	  TT_Init_Face   => TT_Face_Init
	  TT_Done_Face   => TT_Face_Done
	  TT_Init_Size   => TT_Size_Init
	  TT_Done_Size   => TT_Size_Done
	  TT_Reset_Size  => TT_Size_Reset

2002-03-29  Werner Lemberg  <<EMAIL>>

	* builds/vms/ftconfig.h: Rename LOCAL_DEF and LOCAL_FUNC to
	FT_LOCAL and FT_LOCAL_DEF, respectively, as with other ftconfig.h
	files.
	* builds/unix/ftconfig.in: Add argument to FT_LOCAL and
	FT_LOCAL_DEF.
	* src/truetype/ttinterp.c: s/FT_Assert/FT_ASSERT/.
	* builds/unix/configure.ac: Temporarily deactivate creation of
	../../Jamfile.
	* builds/unix/configure: Updated.

2002-03-28  KUSANO Takayuki  <<EMAIL>>

	* src/sfnt/sfdriver.c (get_sfnt_postscript_name): Fix serious typos.

2002-03-28  Werner Lemberg  <<EMAIL>>

	* include/freetype/internal/psaux.h (PSAux_ServiceRec): Fix
	compiler warnings.
	* include/freetype/internal/t1types.h (T1_FaceRec): Use `const' for
	some members.
	* src/base/ftapi.c (FT_New_Memory_Stream): Fix typos.
	* src/psaux/t1cmap.c (t1_cmap_std_init, t1_cmap_unicode_init): Add
	cast.
	(t1_cmap_{standard,expert,custom,unicode}_class_rec): Use
	`FT_CALLBACK_TABLE_DEF'.
	* src/psaux/t1cmap.h: Updated.
	* src/sfnt/ttcmap0.c (TT_Build_CMaps): Use `ft_encoding_none'
	instead of zero.
	* src/type1/t1objs.c (T1_Face_Init): Use casts.

2002-03-26  David Turner  <<EMAIL>>

	* src/sfnt/sfdriver.c, src/sfnt/sfobjs.c, src/sfnt/ttcmap0.c:
	Fixed a small bug in the FT_CMaps support code.

2002-03-25  David Turner  <<EMAIL>>

	* src/truetype/ttinterp.c (Norm): Replaced with...
	(TT_VecLen): This.
	(TT_MulFix14, TT_DotFix14): New functions.
	(Project, Dual_Project, Free_Project, Compute_Point_Displacement,
	Ins_SHPIX, Ins_MIAP, Ins_MIRP): Use them.
	[FT_CONFIG_OPTION_OLD_CALCS]: Removed all code.

2002-03-22  David Turner  <<EMAIL>>

	* src/base/ftobjs.c, src/sfnt/ttcmap0.c, src/type1/t1objs.c:
	Various fixes to make the FT_CMaps support work correctly (more
	tests are still needed).

	* include/freetype/internal/ftobjs.h, src/sfnt/Jamfile,
	src/sfnt/rules.mk, src/sfnt/sfnt.c, src/sfnt/sfobjs.c,
	src/sfnt/ttload.c, src/sfnt/ttcmap0.c, src/sfnt/ttcmap0.h: Updated
	the SFNT charmap support to use FT_CMaps.

	* include/freetype/fterrdef.h: New file.
	* include/freetype/fterrors.h: Include it.  It contains all error
	codes.
	* include/freetype/config/ftheader.h (FT_ERROR_DEFINITIONS_H): New
	macro.

	* include/freetype/internal/ftmemory.h, and a lot of other files:
	Changed the names of memory macros.  Examples:

	  MEM_Set   => FT_MEM_SET
	  MEM_Copy  => FT_MEM_COPY
	  MEM_Move  => FT_MEM_MOVE

	  ALLOC     => FT_ALLOC
	  FREE      => FT_FREE
	  REALLOC   = >FT_REALLOC

	FT_NEW was introduced to allocate a new object from a _typed_
	pointer.

	Note that ALLOC_ARRAY and REALLOC_ARRAY have been replaced by
	FT_NEW_ARRAY and FT_RENEW_ARRAY which take _typed_ pointer
	arguments.

	This results in _lots_ of sources being changed, but makes the code
	more generic and less error-prone.

	* include/freetype/internal/ftstream.h, src/base/ftstream.c,
	src/cff/cffload.c, src/pcf/pcfread.c, src/sfnt/ttcmap.c,
	src/sfnt/ttcmap0.c, src/sfnt/ttload.c, src/sfnt/ttpost.c,
	src/sfnt/ttsbit.c, src/truetype/ttgload.c, src/truetype/ttpload.c,
	src/winfonts/winfnt.c: Changed the definitions of stream macros.
	Examples:

	  NEXT_Byte     => FT_NEXT_BYTE
	  NEXT_Short    => FT_NEXT_SHORT
	  NEXT_UShortLE => FT_NEXT_USHORT_LE
	  READ_Short    => FT_READ_SHORT
	  GET_Long      => FT_GET_LONG
	  etc.

	Also introduced the FT_PEEK_XXXX functions.

	* src/cff/cffobjs.c (CFF_Build_Unicode_Charmap): Removed commented
	out function.
	(find_encoding): Removed.
	(CFF_Face_Init): Remove charmap support.

	* include/freetype/config/ftoption.h (FT_CONFIG_OPTION_USE_CMAPS,
	TT_CONFIG_CMAP_FORMAT{0,2,4,6,8,10,12}): New macros to fine-tune
	support of cmaps.

2002-03-21  David Turner  <<EMAIL>>

	* src/base/ftobjs.c, src/pcf/pcfdriver.c, src/pcf/pcfread.c: Updated
	to new FT_CMap definitions.

	* src/psaux/t1cmap.h, src/psaux/t1cmap.c, src/type1/t1cmap.h,
	src/type1/t1cmap.c: Updating and moving the Type 1 FT_CMap support
	from `src/type1' to `src/psaux' since it is going to be shared by
	the Type 1 and CID font drivers.

	* src/psaux/Jamfile, src/psaux/psaux.c, src/psaux/psauxmod.c,
	src/psaux/rules.mk, include/freetype/internal/psaux.h: Added support
	for Type 1 FT_CMaps.

2002-03-20  David Turner  <<EMAIL>>

	* src/base/ftgloadr.c (FT_GlyphLoader_CheckSubGlyphs): Fixed a
	memory allocation bug that was due to un-careful renaming of the
	FT_SubGlyph type.

	* src/base/ftdbgmem.c (ft_mem_table_destroy): Fixed a small bug that
	caused the library to crash with Electric Fence when memory
	debugging is used.

	* Renaming stream macros.  Examples:

	  FILE_Skip    => FT_STREAM_SKIP
	  FILE_Read    => FT_STREAM_READ
	  ACCESS_Frame => FT_FRAME_ENTER
	  FORGET_Frame => FT_FRAME_EXIT
	  etc.

	* src/sfnt/sfdriver.c (get_sfnt_postscript_name): Fixed memory leak.

	* include/freetype/internal/ftobjs.h: Changing the definition of
	FT_CMap_CharNextFunc slightly.

	* src/cff/*.c: Updating CFF type definitions.

2002-03-14  David Turner  <<EMAIL>>

	* include/freetype/internal/autohint.h, src/autohint/ahmodule.c,
	src/base/ftapi.c, src/base/ftobjs.c: Updating the type definitions
	for the auto-hinter module.

	  FT_AutoHinter_Interface  => FT_AutoHinter_ServiceRec
	  FT_AutoHinter_Interface* => FT_AutoHinter_Service
	  etc.

	  FT_AutoHinter_Get_Global_Func  => FT_AutoHinter_GlobalGetFunc
	  FT_AutoHinter_Done_Global_Func => FT_AutoHinter_GlobalDoneFunc
	  etc.

	* ahloader.h [_STANDALONE_]: Removed all conditional code.

	* include/freetype/internal/cfftypes.h, src/cff/*.c: Updating the
	type definitions of the CFF font driver.

	  CFF_Font  => CFF_FontRec
	  CFF_Font* => CFF_Font
	  etc.

	* include/freetype/internal/fnttypes.h, src/winfonts/*.c: Updating
	type definitions of the Windows FNT font driver.

	* include/freetype/internal/ftdriver.h,
	include/freetype/internal/ftobjs.h, src/base/ftapi.c,
	src/base/ftobjs.c, src/cff/cffdrivr.c, src/cff/cffdrivr.h,
	src/cid/cidriver.c, src/cid/cidriver.h, src/pcf/pcfdriver.c,
	src/pcf/pcfdriver.h, src/truetype/ttdriver.c,
	src/truetype/ttdriver.h, src/type1/t1driver.c, src/type1/t1driver.h,
	src/winfonts/winfnt.c, src/winfonts/winfnt.h: Updating type
	definitions for font drivers.

	  FTDriver_initFace      => FT_Face_InitFunc
	  FTDriver_initGlyphSlot => FT_Slot_InitFunc
	  etc.

	* src/cid/cidobjs.c (CID_Face_Init): Remove dead code.

	* include/freetype/internal/ftobjs.h, src/base/ftobjs.c: Updated a
	few face method definitions:

	  FT_PSName_Requester     => FT_Face_GetPostscriptNameFunc
	  FT_Glyph_Name_Requester => FT_Face_GetGlyphNameFunc
	  FT_Name_Index_Requester => FT_Face_GetGlyphNameIndexFunc

	* src/base/ftapi.c: New file.  It contains backward compatibility
	functions.

	* include/freetype/internal/psaux.h, src/cid/cidload.c,
	src/cidtoken.h, src/psaux/psobjs.c, src/psaux/psobjs.h,
	src/psaux/t1decode.c, src/type1/t1load.c, src/type1/t1tokens.h:
	Updated common PostScript type definitions.
	Renamed all enumeration values like to uppercase variants:

	  t1_token_any      => T1_TOKEN_TYPE_ANY
	  t1_field_cid_info => T1_FIELD_LOCATION_CID_INFO
	  etc.

	* include/freetype/internal/psglobal.h: Removed.
	* include/freetype/internal/pshints.h, src/pshinter/pshglob.h:
	Updated.

	* include/freetype/internal/tttypes.h,
	include/freetype/internal/sfnt.h, src/base/ftnames.c,
	src/cff/cffdrivr.c, src/sfnt/*.c, src/truetype/*.c: Updated
	SFNT/TrueType type definitions.

	* include/freetype/freetype.h, include/freetype/internal/ftgloadr.h:
	Updating type definitions for the glyph loader.

2002-03-13  Antoine Leca  <<EMAIL>>

	* include/freetype/config/ftoption.h: Changed the automatic
	detection of Microsoft C compilers to automatically support 64-bit
	integers only since revision 9.00 (i.e. >= Visual C++ 2.0).

2002-03-08  Werner Lemberg  <<EMAIL>>

	* src/base/ftutil.c (FT_Realloc): Use MEM_Set instead of memset.

2002-03-07  Werner Lemberg  <<EMAIL>>

	* src/base/ftdbgmem.c (ft_mem_table_resize, ft_mem_table_new,
	ft_mem_table_set, ft_mem_debug_alloc, ft_mem_debug_free,
	ft_mem_debug_realloc, ft_mem_debug_done, FT_Alloc_Debug,
	FT_Realloc_Debug, FT_Free_Debug): Fix compiler warnings.
	* src/base/ftcalc.c (FT_MulFix): Ditto.
	* src/cff/cffdrivr.c (cff_get_name_index): Ditto.
	* src/cff/cffobjs.c (CFF_Size_Get_Globals_Funcs, CFF_Size_Init,
	CFF_GlyphSlot_Init): Ditto.
	* src/cid/cidobjs.c (CID_GlyphSlot_Init,
	CID_Size_Get_Globals_Funcs): Ditto.
	* src/type1/t1objs.c (T1_Size_Get_Globals_Funcs, T1_GlyphSlot_Init):
	Ditto.
	* src/pshinter/pshmod.c (pshinter_interface): Use `static const'.
	* src/winfonts/winfnt.c (FNT_Get_Next_Char): Remove unused
	variables.

	* include/freetype/internal/psaux.h (T1_Builder_Funcs): Renamed
	to...
	(T1_Builder_FuncsRec): This.
	(T1_Builder_Funcs): New typedef.
	(PSAux_Interface): Remove compiler warnings.
	* src/psaux/psauxmod.c (t1_builder_funcs), src/psaux/psobjs.h
	(t1_builder_funcs): Updated.

	* src/pshinter/pshglob.h (PSH_Blue_Align): Replaced with ...
	(PSH_BLUE_ALIGN_{NONE,TOP,BOT}): New defines.
	(PSH_AlignmentRec): Updated.

	* include/freetype/internal/ftstream.h (GET_Char, GET_Byte): Fix
	typo.
	* include/freetype/internal/ftgloadr.h (FT_SubGlyph): Ditto.
	* src/base/ftstream (FT_Get_Char): Rename to...
	(FT_Stream_Get_Char): This.

	* src/base/ftnames.c (FT_Get_Sfnt_Name): s/index/idx/ -- `index' is
	a built-in function in gcc, causing warning messages with gcc 3.0.
	* src/autohint/ahglyph.c (ah_outline_load): Ditto.
	* src/autohint/ahglobal.c (ah_hinter_compute_blues): Ditto.
	* src/cache/ftcmanag.c (ftc_family_table_alloc,
	ftc_family_table_free, FTC_Manager_Done, FTC_Manager_Register_Cache):
	Ditto.
	* src/cff/cffload.c (cff_new_index, cff_done_index,
	cff_explicit_index, CFF_Access_Element, CFF_Forget_Element,
	CFF_Get_Name, CFF_Get_String, CFF_Load_SubFont, CFF_Load_Font,
	CFF_Done_Font): Ditto.
	* src/psaux/psobjs.c (PS_Table_Add, PS_Parser_LoadField): Ditto.
	* src/psaux/t1decode.c (T1_Decoder_Parse_Charstrings): Ditto.
	* src/pshinter/pshrec.c (ps_mask_test_bit, ps_mask_clear_bit,
	ps_mask_set_bit, ps_dimension_add_t1stem, ps_hints_t1stem3,
	* src/pshinter/pshalgo1.c (psh1_hint_table_record,
	psh1_hint_table_record_mask, psh1_hint_table_activate_mask): Ditto.
	* src/pshinter/pshalgo2.c (psh2_hint_table_record,
	psh2_hint_table_record_mask, psh2_hint_table_activate_mask): Ditto.
	* src/sfnt/ttpost.c (Load_Format_20, Load_Format_25,
	TT_Get_PS_Name): Ditto.
	* src/truetype/ttgload.c (TT_Get_Metrics, Get_HMetrics,
	load_truetype_glyph): Ditto.
	* src/type1/t1load.c (parse_subrs, T1_Open_Face): Ditto.
	* src/type1/t1afm.c (T1_Get_Kerning): Ditto.
	* include/freetype/cache/ftcmanag.h (ftc_family_table_free): Ditto.

2002-03-06  David Turner  <<EMAIL>>

	* src/type1/t1objs.c (T1_Face_Init), src/cid/cidobjs.c
	(CID_Face_Init): Fixed another bug related to the
	ascender/descender/text height of Postscript fonts.

	* src/pshinter/pshalgo2.c (print_zone): Renamed to ...
	(psh2_print_zone): This.
	* src/pshinter/pshalgo1.c (print_zone): Renamed to ...
	(psh1_print_zone): This.

	* include/freetype/freetype.h, include/freetype/internal/ftobjs.h,
	src/base/ftobjs.c: Adding the new FT_Library_Version API to return
	the library's current version in dynamic links.
	* src/base/ftinit.c (FT_Init_FreeType): Updated.

2002-03-06  Werner Lemberg  <<EMAIL>>

	* src/pshinter/pshglob.h (PSH_DimensionRec): s/std/stdw/.
	* src/pshinter/pshglob.c (psh_global_scale_widths,
	psh_dimension_snap_width, psh_globals_destroy, psh_globals_new):
	Ditto.

2002-03-05  David Turner  <<EMAIL>>

	* src/type1/t1objs.c (T1_Face_Init), src/cff/cffobjs.c
	(CFF_Face_Init), src/cid/cidobjs.c (CID_Face_Init): Removing the bug
	that returned global BBox values in 16.16 fixed format (instead of
	integer font units).

	* src/cid/cidriver.c (cid_get_postscript_name): Fixed a bug that
	caused the CID driver to return Postscript font names with a leading
	slash (`/') as in `/MOEKai-Regular'.

	* src/sfnt/ttload.c (TT_Load_Names), src/sfnt/sfobjs.c (Get_Name),
	src/sfnt/sfdriver.c (get_sfnt_postscript_name): Fixed the loader so
	that it accepts broken fonts like `foxjump.ttf', which made FreeType
	crash when trying to load them.

	Also improved the name table parser to be able to load
	Windows-encoded entries before Macintosh or Unicode ones, since it
	seems some fonts don't have reliable values here anyway.

	* include/freetype/internal/psnames.h: Add typedef for
	`PSNames_Service'.

2002-03-05  Werner Lemberg  <<EMAIL>>

	* builds/unix/aclocal.m4, builds/unix/ltmain.sh: Update to libtool
	1.4.2.
	Apply a small patch for AIX to make shared libraries work (this
	patch is already in the CVS version of libtool).

	* builds/unix/config.sub, builds/unix/config.guess: Updated to
	recent versions.

	* builds/unix/configure.ac: Fix typo
	(AC_CONFIG_FILE->AC_CONFIG_FILES).

	* builds/unix/configure: Regenerated.

2002-02-28  David Turner  <<EMAIL>>

	* include/freetype/ftconfig.h: Changed `FT_LOCAL xxxx' to
	`FT_LOCAL( xxxx )' everywhere in the source.  The same goes for
	`FT_LOCAL_DEF xxxx' which is translated to `FT_LOCAL_DEF( xxxxx )'.

	* include/freetype/freetype.h (FREETYPE_MINOR, FREETYPE_PATCH):
	Changing version to 2.1.0 to indicate an unstable branch.
	Added the declarations of FT_Get_First_Char and FT_Get_Next_Char.

	* src/base/ftobjs.c: Implement FT_Get_First_Char and
	FT_Get_Next_Char.

	* include/freetype/t1tables.h: Renaming structure types.  This

	  typedef  T1_Struct_
	  {
	  } T1_Struct;

	becomes

	  typedef  PS_StructRec_
	  {
	  } PS_StructRec, *PS_Struct;

	  typedef PS_StructRec  T1_Struct;  /* backward compatibility */

	Hence, we increase the coherency of the source code by effectively
	using the `Rec' prefix for structure types.

2002-02-27  David Turner  <<EMAIL>>

	* src/sfnt/ttload.c (TT_Load_Names): Simplifying and securing the
	names table loader.  Invalid individual name entries are now handled
	correctly.  This allows the loading of very buggy fonts like
	`foxjump.ttf' without allocating tons of memory and causing crashes.

	* src/otlayout/otlcommon.h, src/otlayout/otlcommon.c: Adding (still
	experimental) code for OpenType Layout tables validation and
	parsing.

	* src/type1/t1cmap.h, src/type1/t1cmap.c: Adding (still
	experimental) code for Type 1 charmap processing.

	* src/sfnt/ttcmap0.c: New file.  It contains a new, still
	experimental SFNT charmap processing support.

	* include/freetype/internal/ftobjs.h: Adding validation support as
	well as internal charmap object definitions (FT_CMap != FT_CharMap).

2002-02-24  David Turner  <<EMAIL>>

	* Renaming stream functions to the FT_<Subject>_<Action> scheme:

	  FT_Seek_Stream    => FT_Stream_Seek
	  FT_Skip_Stream    => FT_Stream_Skip
	  FT_Read_Stream    => FT_Stream_Read
	  FT_Read_Stream_At => FT_Stream_Read_At
	  FT_Access_Frame   => FT_Stream_Enter_Frame
	  FT_Forget_Frame   => FT_Stream_Exit_Frame
	  FT_Extract_Frame  => FT_Stream_Extract_Frame
	  FT_Release_Frame  => FT_Stream_Release_Frame
	  FT_Get_XXXX       => FT_Stream_Get_XXXX
	  FT_Read_XXXX      => FT_Stream_Read_XXXX

	  FT_New_Stream( filename, stream ) =>
	    FT_Stream_Open( stream, filename )

	    (The function doesn't create the FT_Stream structure, it simply
	    initializes it for reading.)

	  FT_New_Memory_Stream( library, FT_Byte*  base, size, stream ) =>
	    FT_Stream_Open_Memory( stream, const FT_Byte* base, size )

	  FT_Done_Stream  => FT_Stream_Close
	  FT_Stream_IO    => FT_Stream_IOFunc
	  FT_Stream_Close => FT_Stream_CloseFunc

	  ft_close_stream => ft_ansi_stream_close (in base/ftsystem.c only)
	  ft_io_stream    => ft_ansi_stream_io    (in base/ftsystem.c only)

	* src/base/ftutil.c: New file.  Contains all memory and list
	management code (previously in `ftobjs.c' and `ftlist.c',
	respectively).

	* include/freetype/internal/ftobjs.h: Moving all code related to
	glyph loaders to ...
	* include/freetype/internal/ftgloadr.h: This new file.
	`FT_GlyphLoader' is now a pointer to the structure
	`FT_GlyphLoaderRec'.
	(ft_glyph_own_bitmap): Renamed to ...
	(FT_GLYPH_OWN_BITMAP): This.
	* src/base/ftobjs.c: Moving all code related to glyph loaders
	to ...
	* src/base/ftgloadr.c: This new file.

2002-02-22  Werner Lemberg  <<EMAIL>>

	* include/freetype/internal/ftdebug.h (FT_Trace): Remove comma in
	enum to avoid compiler warnings.

2002-02-21  David Turner  <<EMAIL>>

	Modified the debug sub-system initialization.  Trace levels can now
	be specified within the `FT2_DEBUG' environment variable.  See the
	comments within `ftdebug.c' for more details.

	* src/base/ftdebug.c: (FT_SetTraceLevel): Removed.
	(ft_debug_init): New function.
	(ft_debug_dummy): Removed.
	Updated to changes in ftdebug.h

	* include/freetype/internal/ftdebug.h: Always define
	FT_DEBUG_LEVEL_ERROR if FT_DEBUG_LEVEL_TRACE is defined.
	(FT_Assert): Renamed to ...
	(FT_ASSERT): This.
	Some stuff from ftdebug.h has been moved to ...

	* include/freetype/internal/fttrace.h: New file, to define the trace
	levels used for debugging.  It is used both to define enums and
	toggle names for FT2_DEBUG.

	* include/freetype/internal/internal.h: Updated.

	* src/base/ftobjs.c, src/base/ftstream.c: Updated.

	* include/freetype/internal/ftextend.h, src/base/ftextend.c:
	Removed.  Both files are now completely obsolete.
	* src/base/Jamfile, src/base/rules.mk: Updated.

	* include/freetype/fterrors.h: Adding `#undef FT_ERR_CAT' and
	`#undef FT_ERR_XCAT' to avoid warnings with certain compilers (like
	LCC).

	* src/pshinter/pshalgo2.c (print_zone): Renamed to ...
	(psh2_print_zone): This to avoid errors during compilation of debug
	library.

	* src/smooth/ftgrays.c (FT_COMPONENT): Change definition to as
	`trace_smooth'.

2002-02-20  David Turner  <<EMAIL>>

	* README: Adding `<EMAIL>' address for bug reports.

2002-02-20  Werner Lemberg  <<EMAIL>>

	* builds/unix/install.mk (check): New dummy target.
	(.PHONY): Add it.

2002-02-19  Werner Lemberg  <<EMAIL>>

	* builds/freetype.mk (FT_CFLAGS): Use $(INCLUDE_FLAGS) first.

	* src/cache/ftccache.c (ftc_cache_resize): Mark `error' as unused
	to avoid compiler warning.
	* src/cff/cffload.c (CFF_Get_String): Ditto.
	* src/cff/cffobjs.c (CFF_StrCopy): Ditto.
	* src/psaux/psobjs.c (PS_Table_Done): Ditto.
	* src/pcf/pcfread.c (pcf_seek_to_table_type): Ditto.
	* src/sfnt/sfdriver.c (get_sfnt_postscript_name): Ditto.
	(pcf_get_bitmaps): The same for `sizebitmaps'.
	* src/psaux/t1decode.c (T1_Decoder_Parse_Charstrings): The same for
	`orig_y'.
	(t1operator_seac): Comment out more dead code.
	* src/pshinter/pshalgo2.c (ps2_hints_apply): Add `DEBUG_HINTER'
	conditional.
	* src/truetype/ttgload.c (TT_Process_Simple_Glyph,
	load_truetype_glyph): Add `TT_CONFIG_OPTION_BYTECODE_INTERPRETER'
	conditional.

2002-02-18  Werner Lemberg  <<EMAIL>>

	* src/autohint/ahglyph.c (ah_outline_link_segments): Remove unused
	variables.
	* src/autohint/ahhint.c (ah_align_serif_edge): Use FT_UNUSED instead
	of UNUSED.
	* src/autohint/ahmodule.c (ft_autohinter_reset): Ditto.
	* src/pshinter/pshrec.c (ps_mask_table_merge): Fix typo in variable
	swapping code.
	* src/pshinter/pshglob.h (PSH_Blue_Align): Add PSH_BLUE_ALIGN_NONE.
	* src/pshinter/pshglob.c (psh_blues_snap_stem): Use it.
	* src/pshinter/pshalgo1.c (psh1_hint_table_optimize): Ditto.
	* src/pshinter/pshalgo2.c (psh2_hint_align): Ditto.
	* include/freetype/internal/ftobjs.h (UNUSED): Removed.

2002-02-10  Roberto Alameda  <<EMAIL>>

	Add support for ISOLatin1 PS encoding.

	* include/freetype/freetype.h (ft_encoding_latin_1): New tag
	(`lat1').
	* include/freetype/internal/t1types.h (T1_Encoding_Type): Add
	`t1_encoding_isolatin1'.
	* src/type1/t1driver.c (Get_Char_Index, Get_Next_Char): Handle
	ft_encoding_latin_1.
	* src/type1/t1load.c (parse_encoding): Handle `ISOLatin1Encoding'.
	* src/type1/t1objs.c (T1_Face_Init): Handle `t1_encoding_isolatin1'.

----------------------------------------------------------------------------

Copyright 2002-2018 by
David Turner, Robert Wilhelm, and Werner Lemberg.

This file is part of the FreeType project, and may only be used, modified,
and distributed under the terms of the FreeType project license,
LICENSE.TXT.  By continuing to use, modify, or distribute this file you
indicate that you have read the license and understand and accept it
fully.


Local Variables:
version-control: never
coding: utf-8
End:

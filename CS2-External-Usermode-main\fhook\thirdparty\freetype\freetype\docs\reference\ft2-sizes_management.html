<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"https://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>FreeType-2.9.1 API Reference</title>
<style type="text/css">
  a:link { color: #0000EF; }
  a:visited { color: #51188E; }
  a:hover { color: #FF0000; }

  body { font-family: Verdana, Geneva, Arial, Helvetica, serif;
         color: #000000;
         background: #FFFFFF;
         width: 87%;
         margin: auto; }

  div.section { width: 75%;
                margin: auto; }
  div.section hr { margin: 4ex 0 1ex 0; }
  div.section h4 { background-color: #EEEEFF;
                   font-size: medium;
                   font-style: oblique;
                   font-weight: bold;
                   margin: 3ex 0 1.5ex 9%;
                   padding: 0.3ex 0 0.3ex 1%; }
  div.section p { margin: 1.5ex 0 1.5ex 10%; }
  div.section pre { margin: 3ex 0 3ex 9%;
                    background-color: #D6E8FF;
                    padding: 2ex 0 2ex 1%; }
  div.section table.fields { width: 90%;
                             margin: 1.5ex 0 1.5ex 10%; }
  div.section table.toc { width: 95%;
                          margin: 1.5ex 0 1.5ex 5%; }
  div.timestamp { text-align: center;
                  font-size: 69%;
                  margin: 1.5ex 0 1.5ex 0; }

  h1 { text-align: center; }
  h3 { font-size: medium;
       margin: 4ex 0 1.5ex 0; }

  p { text-align: justify; }

  pre.colored { color: blue; }

  span.keyword { font-family: monospace;
                 text-align: left;
                 white-space: pre;
                 color: darkblue; }

  table.fields td.val { font-weight: bold;
                        text-align: right;
                        width: 30%;
                        vertical-align: baseline;
                        padding: 1ex 1em 1ex 0; }
  table.fields td.desc { vertical-align: baseline;
                         padding: 1ex 0 1ex 1em; }
  table.fields td.desc p:first-child { margin: 0; }
  table.fields td.desc p { margin: 1.5ex 0 0 0; }
  table.index { margin: 6ex auto 6ex auto;
                border: 0;
                border-collapse: separate;
                border-spacing: 1em 0.3ex; }
  table.index tr { padding: 0; }
  table.index td { padding: 0; }
  table.index-toc-link { width: 100%;
                         border: 0;
                         border-spacing: 0;
                         margin: 1ex 0 1ex 0; }
  table.index-toc-link td.left { padding: 0 0.5em 0 0.5em;
                                 font-size: 83%;
                                 text-align: left; }
  table.index-toc-link td.middle { padding: 0 0.5em 0 0.5em;
                                   font-size: 83%;
                                   text-align: center; }
  table.index-toc-link td.right { padding: 0 0.5em 0 0.5em;
                                  font-size: 83%;
                                  text-align: right; }
  table.synopsis { margin: 6ex auto 6ex auto;
                   border: 0;
                   border-collapse: separate;
                   border-spacing: 2em 0.6ex; }
  table.synopsis tr { padding: 0; }
  table.synopsis td { padding: 0; }
  table.toc td.link { width: 30%;
                      text-align: right;
                      vertical-align: baseline;
                      padding: 1ex 1em 1ex 0; }
  table.toc td.desc { vertical-align: baseline;
                      padding: 1ex 0 1ex 1em;
                      text-align: left; }
  table.toc td.desc p:first-child { margin: 0;
                                    text-align: left; }
  table.toc td.desc p { margin: 1.5ex 0 0 0;
                        text-align: left; }

</style>
</head>
<body>

<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table>
<h1>FreeType-2.9.1 API Reference</h1>

<h1 id="sizes_management">Size Management</h1>
<h2>Synopsis</h2>
<table class="synopsis">
<tr><td><a href="#FT_New_Size">FT_New_Size</a></td><td><a href="#FT_Done_Size">FT_Done_Size</a></td><td><a href="#FT_Activate_Size">FT_Activate_Size</a></td><td></td></tr>
</table>


<p>When creating a new face object (e.g., with <a href="ft2-base_interface.html#FT_New_Face">FT_New_Face</a>), an <a href="ft2-base_interface.html#FT_Size">FT_Size</a> object is automatically created and used to store all pixel-size dependent information, available in the &lsquo;face-&gt;size&rsquo; field.</p>
<p>It is however possible to create more sizes for a given face, mostly in order to manage several character pixel sizes of the same font family and style. See <a href="ft2-sizes_management.html#FT_New_Size">FT_New_Size</a> and <a href="ft2-sizes_management.html#FT_Done_Size">FT_Done_Size</a>.</p>
<p>Note that <a href="ft2-base_interface.html#FT_Set_Pixel_Sizes">FT_Set_Pixel_Sizes</a> and <a href="ft2-base_interface.html#FT_Set_Char_Size">FT_Set_Char_Size</a> only modify the contents of the current &lsquo;active&rsquo; size; you thus need to use <a href="ft2-sizes_management.html#FT_Activate_Size">FT_Activate_Size</a> to change it.</p>
<p>99% of applications won't need the functions provided here, especially if they use the caching sub-system, so be cautious when using these.</p>

<div class="section">
<h3 id="FT_New_Size">FT_New_Size</h3>
<p>Defined in FT_SIZES_H (freetype/ftsizes.h).</p>
<pre>
  FT_EXPORT( <a href="ft2-basic_types.html#FT_Error">FT_Error</a> )
  <b>FT_New_Size</b>( <a href="ft2-base_interface.html#FT_Face">FT_Face</a>   face,
               <a href="ft2-base_interface.html#FT_Size">FT_Size</a>*  size );
</pre>

<p>Create a new size object from a given face object.</p>

<h4>input</h4>
<table class="fields">
<tr><td class="val" id="face">face</td><td class="desc">
<p>A handle to a parent face object.</p>
</td></tr>
</table>

<h4>output</h4>
<table class="fields">
<tr><td class="val" id="asize">asize</td><td class="desc">
<p>A handle to a new size object.</p>
</td></tr>
</table>

<h4>return</h4>
<p>FreeType error code. 0&nbsp;means success.</p>

<h4>note</h4>
<p>You need to call <a href="ft2-sizes_management.html#FT_Activate_Size">FT_Activate_Size</a> in order to select the new size for upcoming calls to <a href="ft2-base_interface.html#FT_Set_Pixel_Sizes">FT_Set_Pixel_Sizes</a>, <a href="ft2-base_interface.html#FT_Set_Char_Size">FT_Set_Char_Size</a>, <a href="ft2-base_interface.html#FT_Load_Glyph">FT_Load_Glyph</a>, <a href="ft2-base_interface.html#FT_Load_Char">FT_Load_Char</a>, etc.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_Done_Size">FT_Done_Size</h3>
<p>Defined in FT_SIZES_H (freetype/ftsizes.h).</p>
<pre>
  FT_EXPORT( <a href="ft2-basic_types.html#FT_Error">FT_Error</a> )
  <b>FT_Done_Size</b>( <a href="ft2-base_interface.html#FT_Size">FT_Size</a>  size );
</pre>

<p>Discard a given size object. Note that <a href="ft2-base_interface.html#FT_Done_Face">FT_Done_Face</a> automatically discards all size objects allocated with <a href="ft2-sizes_management.html#FT_New_Size">FT_New_Size</a>.</p>

<h4>input</h4>
<table class="fields">
<tr><td class="val" id="size">size</td><td class="desc">
<p>A handle to a target size object.</p>
</td></tr>
</table>

<h4>return</h4>
<p>FreeType error code. 0&nbsp;means success.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_Activate_Size">FT_Activate_Size</h3>
<p>Defined in FT_SIZES_H (freetype/ftsizes.h).</p>
<pre>
  FT_EXPORT( <a href="ft2-basic_types.html#FT_Error">FT_Error</a> )
  <b>FT_Activate_Size</b>( <a href="ft2-base_interface.html#FT_Size">FT_Size</a>  size );
</pre>

<p>Even though it is possible to create several size objects for a given face (see <a href="ft2-sizes_management.html#FT_New_Size">FT_New_Size</a> for details), functions like <a href="ft2-base_interface.html#FT_Load_Glyph">FT_Load_Glyph</a> or <a href="ft2-base_interface.html#FT_Load_Char">FT_Load_Char</a> only use the one that has been activated last to determine the &lsquo;current character pixel size&rsquo;.</p>
<p>This function can be used to &lsquo;activate&rsquo; a previously created size object.</p>

<h4>input</h4>
<table class="fields">
<tr><td class="val" id="size">size</td><td class="desc">
<p>A handle to a target size object.</p>
</td></tr>
</table>

<h4>return</h4>
<p>FreeType error code. 0&nbsp;means success.</p>

<h4>note</h4>
<p>If &lsquo;face&rsquo; is the size's parent face object, this function changes the value of &lsquo;face-&gt;size&rsquo; to the input size handle.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

</body>
</html>

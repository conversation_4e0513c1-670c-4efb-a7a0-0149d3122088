# FreeType 2 src/base Jamfile
#
# Copyright 2001-2018 by
# <PERSON>, <PERSON>, and <PERSON>.
#
# This file is part of the FreeType project, and may only be used, modified,
# and distributed under the terms of the FreeType project license,
# LICENSE.TXT.  By continuing to use, modify, or distribute this file you
# indicate that you have read the license and understand and accept it
# fully.

SubDir  FT2_TOP $(FT2_SRC_DIR) base ;


{
  local  _sources ;

  if $(FT2_MULTI)
  {
    _sources = basepic
               ftadvanc
               ftcalc
               ftdbgmem
               ftfntfmt
               ftgloadr
               fthash
               ftlcdfil
               ftobjs
               ftoutln
               ftpic
               ftpsprop
               ftrfork
               ftsnames
               ftstream
               fttrigon
               ftutil
               ;
  }
  else
  {
    _sources = ftbase ;
  }

  Library  $(FT2_LIB) : $(_sources).c ;
}

# Add the optional/replaceable files.
#
{
  local  _sources = ftapi
                    ftbbox
                    ftbdf
                    ftbitmap
                    ftcid
                    ftdebug
                    ftfstype
                    ftgasp
                    ftglyph
                    ftgxval
                    ftinit
                    ftmm
                    ftotval
                    ftpatent
                    ftpfr
                    ftstroke
                    ftsynth
                    ftsystem
                    fttype1
                    ftwinfnt
                    ;

  Library  $(FT2_LIB) : $(_sources).c ;
}

# Add Macintosh-specific file to the library when necessary.
#
if $(MAC)
{
  Library  $(FT2_LIB) : ftmac.c ;
}
else if $(OS) = MACOSX
{
  if $(FT2_MULTI)
  {
    Library  $(FT2_LIB) : ftmac.c ;
  }
}

# end of src/base Jamfile

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"https://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>FreeType-2.9.1 API Reference</title>
<style type="text/css">
  a:link { color: #0000EF; }
  a:visited { color: #51188E; }
  a:hover { color: #FF0000; }

  body { font-family: Verdana, Geneva, Arial, Helvetica, serif;
         color: #000000;
         background: #FFFFFF;
         width: 87%;
         margin: auto; }

  div.section { width: 75%;
                margin: auto; }
  div.section hr { margin: 4ex 0 1ex 0; }
  div.section h4 { background-color: #EEEEFF;
                   font-size: medium;
                   font-style: oblique;
                   font-weight: bold;
                   margin: 3ex 0 1.5ex 9%;
                   padding: 0.3ex 0 0.3ex 1%; }
  div.section p { margin: 1.5ex 0 1.5ex 10%; }
  div.section pre { margin: 3ex 0 3ex 9%;
                    background-color: #D6E8FF;
                    padding: 2ex 0 2ex 1%; }
  div.section table.fields { width: 90%;
                             margin: 1.5ex 0 1.5ex 10%; }
  div.section table.toc { width: 95%;
                          margin: 1.5ex 0 1.5ex 5%; }
  div.timestamp { text-align: center;
                  font-size: 69%;
                  margin: 1.5ex 0 1.5ex 0; }

  h1 { text-align: center; }
  h3 { font-size: medium;
       margin: 4ex 0 1.5ex 0; }

  p { text-align: justify; }

  pre.colored { color: blue; }

  span.keyword { font-family: monospace;
                 text-align: left;
                 white-space: pre;
                 color: darkblue; }

  table.fields td.val { font-weight: bold;
                        text-align: right;
                        width: 30%;
                        vertical-align: baseline;
                        padding: 1ex 1em 1ex 0; }
  table.fields td.desc { vertical-align: baseline;
                         padding: 1ex 0 1ex 1em; }
  table.fields td.desc p:first-child { margin: 0; }
  table.fields td.desc p { margin: 1.5ex 0 0 0; }
  table.index { margin: 6ex auto 6ex auto;
                border: 0;
                border-collapse: separate;
                border-spacing: 1em 0.3ex; }
  table.index tr { padding: 0; }
  table.index td { padding: 0; }
  table.index-toc-link { width: 100%;
                         border: 0;
                         border-spacing: 0;
                         margin: 1ex 0 1ex 0; }
  table.index-toc-link td.left { padding: 0 0.5em 0 0.5em;
                                 font-size: 83%;
                                 text-align: left; }
  table.index-toc-link td.middle { padding: 0 0.5em 0 0.5em;
                                   font-size: 83%;
                                   text-align: center; }
  table.index-toc-link td.right { padding: 0 0.5em 0 0.5em;
                                  font-size: 83%;
                                  text-align: right; }
  table.synopsis { margin: 6ex auto 6ex auto;
                   border: 0;
                   border-collapse: separate;
                   border-spacing: 2em 0.6ex; }
  table.synopsis tr { padding: 0; }
  table.synopsis td { padding: 0; }
  table.toc td.link { width: 30%;
                      text-align: right;
                      vertical-align: baseline;
                      padding: 1ex 1em 1ex 0; }
  table.toc td.desc { vertical-align: baseline;
                      padding: 1ex 0 1ex 1em;
                      text-align: left; }
  table.toc td.desc p:first-child { margin: 0;
                                    text-align: left; }
  table.toc td.desc p { margin: 1.5ex 0 0 0;
                        text-align: left; }

</style>
</head>
<body>

<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table>
<h1>FreeType-2.9.1 API Reference</h1>

<h1 id="system_interface">System Interface</h1>
<h2>Synopsis</h2>
<table class="synopsis">
<tr><td><a href="#FT_Memory">FT_Memory</a></td><td><a href="#FT_MemoryRec">FT_MemoryRec</a></td><td><a href="#FT_Stream_CloseFunc">FT_Stream_CloseFunc</a></td></tr>
<tr><td><a href="#FT_Alloc_Func">FT_Alloc_Func</a></td><td><a href="#FT_Stream">FT_Stream</a></td><td><a href="#FT_StreamRec">FT_StreamRec</a></td></tr>
<tr><td><a href="#FT_Free_Func">FT_Free_Func</a></td><td><a href="#FT_StreamDesc">FT_StreamDesc</a></td><td></td></tr>
<tr><td><a href="#FT_Realloc_Func">FT_Realloc_Func</a></td><td><a href="#FT_Stream_IoFunc">FT_Stream_IoFunc</a></td><td></td></tr>
</table>


<p>This section contains various definitions related to memory management and i/o access. You need to understand this information if you want to use a custom memory manager or you own i/o streams.</p>

<div class="section">
<h3 id="FT_Memory">FT_Memory</h3>
<p>Defined in FT_SYSTEM_H (freetype/ftsystem.h).</p>
<pre>
  <span class="keyword">typedef</span> <span class="keyword">struct</span> FT_MemoryRec_*  <b>FT_Memory</b>;
</pre>

<p>A handle to a given memory manager object, defined with an <a href="ft2-system_interface.html#FT_MemoryRec">FT_MemoryRec</a> structure.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_Alloc_Func">FT_Alloc_Func</h3>
<p>Defined in FT_SYSTEM_H (freetype/ftsystem.h).</p>
<pre>
  <span class="keyword">typedef</span> <span class="keyword">void</span>*
  (*<b>FT_Alloc_Func</b>)( <a href="ft2-system_interface.html#FT_Memory">FT_Memory</a>  memory,
                    <span class="keyword">long</span>       size );
</pre>

<p>A function used to allocate &lsquo;size&rsquo; bytes from &lsquo;memory&rsquo;.</p>

<h4>input</h4>
<table class="fields">
<tr><td class="val" id="memory">memory</td><td class="desc">
<p>A handle to the source memory manager.</p>
</td></tr>
<tr><td class="val" id="size">size</td><td class="desc">
<p>The size in bytes to allocate.</p>
</td></tr>
</table>

<h4>return</h4>
<p>Address of new memory block. 0&nbsp;in case of failure.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_Free_Func">FT_Free_Func</h3>
<p>Defined in FT_SYSTEM_H (freetype/ftsystem.h).</p>
<pre>
  <span class="keyword">typedef</span> <span class="keyword">void</span>
  (*<b>FT_Free_Func</b>)( <a href="ft2-system_interface.html#FT_Memory">FT_Memory</a>  memory,
                   <span class="keyword">void</span>*      block );
</pre>

<p>A function used to release a given block of memory.</p>

<h4>input</h4>
<table class="fields">
<tr><td class="val" id="memory">memory</td><td class="desc">
<p>A handle to the source memory manager.</p>
</td></tr>
<tr><td class="val" id="block">block</td><td class="desc">
<p>The address of the target memory block.</p>
</td></tr>
</table>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_Realloc_Func">FT_Realloc_Func</h3>
<p>Defined in FT_SYSTEM_H (freetype/ftsystem.h).</p>
<pre>
  <span class="keyword">typedef</span> <span class="keyword">void</span>*
  (*<b>FT_Realloc_Func</b>)( <a href="ft2-system_interface.html#FT_Memory">FT_Memory</a>  memory,
                      <span class="keyword">long</span>       cur_size,
                      <span class="keyword">long</span>       new_size,
                      <span class="keyword">void</span>*      block );
</pre>

<p>A function used to re-allocate a given block of memory.</p>

<h4>input</h4>
<table class="fields">
<tr><td class="val" id="memory">memory</td><td class="desc">
<p>A handle to the source memory manager.</p>
</td></tr>
<tr><td class="val" id="cur_size">cur_size</td><td class="desc">
<p>The block's current size in bytes.</p>
</td></tr>
<tr><td class="val" id="new_size">new_size</td><td class="desc">
<p>The block's requested new size.</p>
</td></tr>
<tr><td class="val" id="block">block</td><td class="desc">
<p>The block's current address.</p>
</td></tr>
</table>

<h4>return</h4>
<p>New block address. 0&nbsp;in case of memory shortage.</p>

<h4>note</h4>
<p>In case of error, the old block must still be available.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_MemoryRec">FT_MemoryRec</h3>
<p>Defined in FT_SYSTEM_H (freetype/ftsystem.h).</p>
<pre>
  <span class="keyword">struct</span>  FT_MemoryRec_
  {
    <span class="keyword">void</span>*            user;
    <a href="ft2-system_interface.html#FT_Alloc_Func">FT_Alloc_Func</a>    alloc;
    <a href="ft2-system_interface.html#FT_Free_Func">FT_Free_Func</a>     free;
    <a href="ft2-system_interface.html#FT_Realloc_Func">FT_Realloc_Func</a>  realloc;
  };
</pre>

<p>A structure used to describe a given memory manager to FreeType&nbsp;2.</p>

<h4>fields</h4>
<table class="fields">
<tr><td class="val" id="user">user</td><td class="desc">
<p>A generic typeless pointer for user data.</p>
</td></tr>
<tr><td class="val" id="alloc">alloc</td><td class="desc">
<p>A pointer type to an allocation function.</p>
</td></tr>
<tr><td class="val" id="free">free</td><td class="desc">
<p>A pointer type to an memory freeing function.</p>
</td></tr>
<tr><td class="val" id="realloc">realloc</td><td class="desc">
<p>A pointer type to a reallocation function.</p>
</td></tr>
</table>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_Stream">FT_Stream</h3>
<p>Defined in FT_SYSTEM_H (freetype/ftsystem.h).</p>
<pre>
  <span class="keyword">typedef</span> <span class="keyword">struct</span> FT_StreamRec_*  <b>FT_Stream</b>;
</pre>

<p>A handle to an input stream.</p>

<h4>also</h4>
<p>See <a href="ft2-system_interface.html#FT_StreamRec">FT_StreamRec</a> for the publicly accessible fields of a given stream object.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_StreamDesc">FT_StreamDesc</h3>
<p>Defined in FT_SYSTEM_H (freetype/ftsystem.h).</p>
<pre>
  <span class="keyword">typedef</span> <span class="keyword">union</span>  FT_StreamDesc_
  {
    <span class="keyword">long</span>   value;
    <span class="keyword">void</span>*  pointer;

  } <b>FT_StreamDesc</b>;
</pre>

<p>A union type used to store either a long or a pointer. This is used to store a file descriptor or a &lsquo;FILE*&rsquo; in an input stream.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_Stream_IoFunc">FT_Stream_IoFunc</h3>
<p>Defined in FT_SYSTEM_H (freetype/ftsystem.h).</p>
<pre>
  <span class="keyword">typedef</span> <span class="keyword">unsigned</span> <span class="keyword">long</span>
  (*<b>FT_Stream_IoFunc</b>)( <a href="ft2-system_interface.html#FT_Stream">FT_Stream</a>       stream,
                       <span class="keyword">unsigned</span> <span class="keyword">long</span>   offset,
                       <span class="keyword">unsigned</span> <span class="keyword">char</span>*  buffer,
                       <span class="keyword">unsigned</span> <span class="keyword">long</span>   count );
</pre>

<p>A function used to seek and read data from a given input stream.</p>

<h4>input</h4>
<table class="fields">
<tr><td class="val" id="stream">stream</td><td class="desc">
<p>A handle to the source stream.</p>
</td></tr>
<tr><td class="val" id="offset">offset</td><td class="desc">
<p>The offset of read in stream (always from start).</p>
</td></tr>
<tr><td class="val" id="buffer">buffer</td><td class="desc">
<p>The address of the read buffer.</p>
</td></tr>
<tr><td class="val" id="count">count</td><td class="desc">
<p>The number of bytes to read from the stream.</p>
</td></tr>
</table>

<h4>return</h4>
<p>The number of bytes effectively read by the stream.</p>

<h4>note</h4>
<p>This function might be called to perform a seek or skip operation with a &lsquo;count&rsquo; of&nbsp;0. A non-zero return value then indicates an error.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_Stream_CloseFunc">FT_Stream_CloseFunc</h3>
<p>Defined in FT_SYSTEM_H (freetype/ftsystem.h).</p>
<pre>
  <span class="keyword">typedef</span> <span class="keyword">void</span>
  (*<b>FT_Stream_CloseFunc</b>)( <a href="ft2-system_interface.html#FT_Stream">FT_Stream</a>  stream );
</pre>

<p>A function used to close a given input stream.</p>

<h4>input</h4>
<table class="fields">
<tr><td class="val" id="stream">stream</td><td class="desc">
<p>A handle to the target stream.</p>
</td></tr>
</table>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_StreamRec">FT_StreamRec</h3>
<p>Defined in FT_SYSTEM_H (freetype/ftsystem.h).</p>
<pre>
  <span class="keyword">typedef</span> <span class="keyword">struct</span>  FT_StreamRec_
  {
    <span class="keyword">unsigned</span> <span class="keyword">char</span>*       base;
    <span class="keyword">unsigned</span> <span class="keyword">long</span>        size;
    <span class="keyword">unsigned</span> <span class="keyword">long</span>        pos;

    <a href="ft2-system_interface.html#FT_StreamDesc">FT_StreamDesc</a>        descriptor;
    <a href="ft2-system_interface.html#FT_StreamDesc">FT_StreamDesc</a>        pathname;
    <a href="ft2-system_interface.html#FT_Stream_IoFunc">FT_Stream_IoFunc</a>     read;
    <a href="ft2-system_interface.html#FT_Stream_CloseFunc">FT_Stream_CloseFunc</a>  close;

    <a href="ft2-system_interface.html#FT_Memory">FT_Memory</a>            memory;
    <span class="keyword">unsigned</span> <span class="keyword">char</span>*       cursor;
    <span class="keyword">unsigned</span> <span class="keyword">char</span>*       limit;

  } <b>FT_StreamRec</b>;
</pre>

<p>A structure used to describe an input stream.</p>

<h4>input</h4>
<table class="fields">
<tr><td class="val" id="base">base</td><td class="desc">
<p>For memory-based streams, this is the address of the first stream byte in memory. This field should always be set to NULL for disk-based streams.</p>
</td></tr>
<tr><td class="val" id="size">size</td><td class="desc">
<p>The stream size in bytes.</p>
<p>In case of compressed streams where the size is unknown before actually doing the decompression, the value is set to 0x7FFFFFFF. (Note that this size value can occur for normal streams also; it is thus just a hint.)</p>
</td></tr>
<tr><td class="val" id="pos">pos</td><td class="desc">
<p>The current position within the stream.</p>
</td></tr>
<tr><td class="val" id="descriptor">descriptor</td><td class="desc">
<p>This field is a union that can hold an integer or a pointer. It is used by stream implementations to store file descriptors or &lsquo;FILE*&rsquo; pointers.</p>
</td></tr>
<tr><td class="val" id="pathname">pathname</td><td class="desc">
<p>This field is completely ignored by FreeType. However, it is often useful during debugging to use it to store the stream's filename (where available).</p>
</td></tr>
<tr><td class="val" id="read">read</td><td class="desc">
<p>The stream's input function.</p>
</td></tr>
<tr><td class="val" id="close">close</td><td class="desc">
<p>The stream's close function.</p>
</td></tr>
<tr><td class="val" id="memory">memory</td><td class="desc">
<p>The memory manager to use to preload frames. This is set internally by FreeType and shouldn't be touched by stream implementations.</p>
</td></tr>
<tr><td class="val" id="cursor">cursor</td><td class="desc">
<p>This field is set and used internally by FreeType when parsing frames.</p>
</td></tr>
<tr><td class="val" id="limit">limit</td><td class="desc">
<p>This field is set and used internally by FreeType when parsing frames.</p>
</td></tr>
</table>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

</body>
</html>

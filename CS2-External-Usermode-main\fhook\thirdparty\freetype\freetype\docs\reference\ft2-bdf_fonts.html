<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"https://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>FreeType-2.9.1 API Reference</title>
<style type="text/css">
  a:link { color: #0000EF; }
  a:visited { color: #51188E; }
  a:hover { color: #FF0000; }

  body { font-family: Verdana, Geneva, Arial, Helvetica, serif;
         color: #000000;
         background: #FFFFFF;
         width: 87%;
         margin: auto; }

  div.section { width: 75%;
                margin: auto; }
  div.section hr { margin: 4ex 0 1ex 0; }
  div.section h4 { background-color: #EEEEFF;
                   font-size: medium;
                   font-style: oblique;
                   font-weight: bold;
                   margin: 3ex 0 1.5ex 9%;
                   padding: 0.3ex 0 0.3ex 1%; }
  div.section p { margin: 1.5ex 0 1.5ex 10%; }
  div.section pre { margin: 3ex 0 3ex 9%;
                    background-color: #D6E8FF;
                    padding: 2ex 0 2ex 1%; }
  div.section table.fields { width: 90%;
                             margin: 1.5ex 0 1.5ex 10%; }
  div.section table.toc { width: 95%;
                          margin: 1.5ex 0 1.5ex 5%; }
  div.timestamp { text-align: center;
                  font-size: 69%;
                  margin: 1.5ex 0 1.5ex 0; }

  h1 { text-align: center; }
  h3 { font-size: medium;
       margin: 4ex 0 1.5ex 0; }

  p { text-align: justify; }

  pre.colored { color: blue; }

  span.keyword { font-family: monospace;
                 text-align: left;
                 white-space: pre;
                 color: darkblue; }

  table.fields td.val { font-weight: bold;
                        text-align: right;
                        width: 30%;
                        vertical-align: baseline;
                        padding: 1ex 1em 1ex 0; }
  table.fields td.desc { vertical-align: baseline;
                         padding: 1ex 0 1ex 1em; }
  table.fields td.desc p:first-child { margin: 0; }
  table.fields td.desc p { margin: 1.5ex 0 0 0; }
  table.index { margin: 6ex auto 6ex auto;
                border: 0;
                border-collapse: separate;
                border-spacing: 1em 0.3ex; }
  table.index tr { padding: 0; }
  table.index td { padding: 0; }
  table.index-toc-link { width: 100%;
                         border: 0;
                         border-spacing: 0;
                         margin: 1ex 0 1ex 0; }
  table.index-toc-link td.left { padding: 0 0.5em 0 0.5em;
                                 font-size: 83%;
                                 text-align: left; }
  table.index-toc-link td.middle { padding: 0 0.5em 0 0.5em;
                                   font-size: 83%;
                                   text-align: center; }
  table.index-toc-link td.right { padding: 0 0.5em 0 0.5em;
                                  font-size: 83%;
                                  text-align: right; }
  table.synopsis { margin: 6ex auto 6ex auto;
                   border: 0;
                   border-collapse: separate;
                   border-spacing: 2em 0.6ex; }
  table.synopsis tr { padding: 0; }
  table.synopsis td { padding: 0; }
  table.toc td.link { width: 30%;
                      text-align: right;
                      vertical-align: baseline;
                      padding: 1ex 1em 1ex 0; }
  table.toc td.desc { vertical-align: baseline;
                      padding: 1ex 0 1ex 1em;
                      text-align: left; }
  table.toc td.desc p:first-child { margin: 0;
                                    text-align: left; }
  table.toc td.desc p { margin: 1.5ex 0 0 0;
                        text-align: left; }

</style>
</head>
<body>

<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table>
<h1>FreeType-2.9.1 API Reference</h1>

<h1 id="bdf_fonts">BDF and PCF Files</h1>
<h2>Synopsis</h2>
<table class="synopsis">
<tr><td><a href="#BDF_PropertyType">BDF_PropertyType</a></td><td><a href="#BDF_PropertyRec">BDF_PropertyRec</a></td><td><a href="#FT_Get_BDF_Property">FT_Get_BDF_Property</a></td></tr>
<tr><td><a href="#BDF_Property">BDF_Property</a></td><td><a href="#FT_Get_BDF_Charset_ID">FT_Get_BDF_Charset_ID</a></td><td></td></tr>
</table>


<p>This section contains the declaration of functions specific to BDF and PCF fonts.</p>

<div class="section">
<h3 id="BDF_PropertyType">BDF_PropertyType</h3>
<p>Defined in FT_BDF_H (freetype/ftbdf.h).</p>
<pre>
  <span class="keyword">typedef</span> <span class="keyword">enum</span>  BDF_PropertyType_
  {
    <a href="ft2-bdf_fonts.html#BDF_PROPERTY_TYPE_NONE">BDF_PROPERTY_TYPE_NONE</a>     = 0,
    <a href="ft2-bdf_fonts.html#BDF_PROPERTY_TYPE_ATOM">BDF_PROPERTY_TYPE_ATOM</a>     = 1,
    <a href="ft2-bdf_fonts.html#BDF_PROPERTY_TYPE_INTEGER">BDF_PROPERTY_TYPE_INTEGER</a>  = 2,
    <a href="ft2-bdf_fonts.html#BDF_PROPERTY_TYPE_CARDINAL">BDF_PROPERTY_TYPE_CARDINAL</a> = 3

  } <b>BDF_PropertyType</b>;
</pre>

<p>A list of BDF property types.</p>

<h4>values</h4>
<table class="fields">
<tr><td class="val" id="BDF_PROPERTY_TYPE_NONE">BDF_PROPERTY_TYPE_NONE</td><td class="desc">
<p>Value&nbsp;0 is used to indicate a missing property.</p>
</td></tr>
<tr><td class="val" id="BDF_PROPERTY_TYPE_ATOM">BDF_PROPERTY_TYPE_ATOM</td><td class="desc">
<p>Property is a string atom.</p>
</td></tr>
<tr><td class="val" id="BDF_PROPERTY_TYPE_INTEGER">BDF_PROPERTY_TYPE_INTEGER</td><td class="desc">
<p>Property is a 32-bit signed integer.</p>
</td></tr>
<tr><td class="val" id="BDF_PROPERTY_TYPE_CARDINAL">BDF_PROPERTY_TYPE_CARDINAL</td><td class="desc">
<p>Property is a 32-bit unsigned integer.</p>
</td></tr>
</table>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="BDF_Property">BDF_Property</h3>
<p>Defined in FT_BDF_H (freetype/ftbdf.h).</p>
<pre>
  <span class="keyword">typedef</span> <span class="keyword">struct</span> BDF_PropertyRec_*  <b>BDF_Property</b>;
</pre>

<p>A handle to a <a href="ft2-bdf_fonts.html#BDF_PropertyRec">BDF_PropertyRec</a> structure to model a given BDF/PCF property.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="BDF_PropertyRec">BDF_PropertyRec</h3>
<p>Defined in FT_BDF_H (freetype/ftbdf.h).</p>
<pre>
  <span class="keyword">typedef</span> <span class="keyword">struct</span>  BDF_PropertyRec_
  {
    <a href="ft2-bdf_fonts.html#BDF_PropertyType">BDF_PropertyType</a>  type;
    <span class="keyword">union</span> {
      <span class="keyword">const</span> <span class="keyword">char</span>*     atom;
      <a href="ft2-basic_types.html#FT_Int32">FT_Int32</a>        integer;
      <a href="ft2-basic_types.html#FT_UInt32">FT_UInt32</a>       cardinal;

    } u;

  } <b>BDF_PropertyRec</b>;
</pre>

<p>This structure models a given BDF/PCF property.</p>

<h4>fields</h4>
<table class="fields">
<tr><td class="val" id="type">type</td><td class="desc">
<p>The property type.</p>
</td></tr>
<tr><td class="val" id="u.atom">u.atom</td><td class="desc">
<p>The atom string, if type is <a href="ft2-bdf_fonts.html#BDF_PropertyType">BDF_PROPERTY_TYPE_ATOM</a>. May be NULL, indicating an empty string.</p>
</td></tr>
<tr><td class="val" id="u.integer">u.integer</td><td class="desc">
<p>A signed integer, if type is <a href="ft2-bdf_fonts.html#BDF_PropertyType">BDF_PROPERTY_TYPE_INTEGER</a>.</p>
</td></tr>
<tr><td class="val" id="u.cardinal">u.cardinal</td><td class="desc">
<p>An unsigned integer, if type is <a href="ft2-bdf_fonts.html#BDF_PropertyType">BDF_PROPERTY_TYPE_CARDINAL</a>.</p>
</td></tr>
</table>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_Get_BDF_Charset_ID">FT_Get_BDF_Charset_ID</h3>
<p>Defined in FT_BDF_H (freetype/ftbdf.h).</p>
<pre>
  FT_EXPORT( <a href="ft2-basic_types.html#FT_Error">FT_Error</a> )
  <b>FT_Get_BDF_Charset_ID</b>( <a href="ft2-base_interface.html#FT_Face">FT_Face</a>       face,
                         <span class="keyword">const</span> <span class="keyword">char</span>*  *acharset_encoding,
                         <span class="keyword">const</span> <span class="keyword">char</span>*  *acharset_registry );
</pre>

<p>Retrieve a BDF font character set identity, according to the BDF specification.</p>

<h4>input</h4>
<table class="fields">
<tr><td class="val" id="face">face</td><td class="desc">
<p>A handle to the input face.</p>
</td></tr>
</table>

<h4>output</h4>
<table class="fields">
<tr><td class="val" id="acharset_encoding">acharset_encoding</td><td class="desc">
<p>Charset encoding, as a C&nbsp;string, owned by the face.</p>
</td></tr>
<tr><td class="val" id="acharset_registry">acharset_registry</td><td class="desc">
<p>Charset registry, as a C&nbsp;string, owned by the face.</p>
</td></tr>
</table>

<h4>return</h4>
<p>FreeType error code. 0&nbsp;means success.</p>

<h4>note</h4>
<p>This function only works with BDF faces, returning an error otherwise.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_Get_BDF_Property">FT_Get_BDF_Property</h3>
<p>Defined in FT_BDF_H (freetype/ftbdf.h).</p>
<pre>
  FT_EXPORT( <a href="ft2-basic_types.html#FT_Error">FT_Error</a> )
  <b>FT_Get_BDF_Property</b>( <a href="ft2-base_interface.html#FT_Face">FT_Face</a>           face,
                       <span class="keyword">const</span> <span class="keyword">char</span>*       prop_name,
                       <a href="ft2-bdf_fonts.html#BDF_PropertyRec">BDF_PropertyRec</a>  *aproperty );
</pre>

<p>Retrieve a BDF property from a BDF or PCF font file.</p>

<h4>input</h4>
<table class="fields">
<tr><td class="val" id="face">face</td><td class="desc">
<p>A handle to the input face.</p>
</td></tr>
<tr><td class="val" id="name">name</td><td class="desc">
<p>The property name.</p>
</td></tr>
</table>

<h4>output</h4>
<table class="fields">
<tr><td class="val" id="aproperty">aproperty</td><td class="desc">
<p>The property.</p>
</td></tr>
</table>

<h4>return</h4>
<p>FreeType error code. 0&nbsp;means success.</p>

<h4>note</h4>
<p>This function works with BDF <i>and</i> PCF fonts. It returns an error otherwise. It also returns an error if the property is not in the font.</p>
<p>A &lsquo;property&rsquo; is a either key-value pair within the STARTPROPERTIES ... ENDPROPERTIES block of a BDF font or a key-value pair from the &lsquo;info-&gt;props&rsquo; array within a &lsquo;FontRec&rsquo; structure of a PCF font.</p>
<p>Integer properties are always stored as &lsquo;signed&rsquo; within PCF fonts; consequently, <a href="ft2-bdf_fonts.html#BDF_PropertyType">BDF_PROPERTY_TYPE_CARDINAL</a> is a possible return value for BDF fonts only.</p>
<p>In case of error, &lsquo;aproperty-&gt;type&rsquo; is always set to <a href="ft2-bdf_fonts.html#BDF_PropertyType">BDF_PROPERTY_TYPE_NONE</a>.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

</body>
</html>

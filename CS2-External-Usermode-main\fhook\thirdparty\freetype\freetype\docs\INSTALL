
There are  several ways  to build the  FreeType library,  depending on
your system and the level of  customization you need.  Here is a short
overview of the documentation available:


I. Normal installation and upgrades
===================================

  1. Unix Systems (including Mac OS X, Cygwin, and MSys on Windows)

    Please read `INSTALL.UNIX' to install or upgrade FreeType  2 on  a
    Unix system.   Note  that  you  *need*  GNU  Make   for  automatic
    compilation,  since other make tools won't work (this includes BSD
    Make).

    GNU Make VERSION 3.81 OR NEWER IS NEEDED!

    [For `cmake' see below.]


  2. On VMS with the `mms' build tool

    See `INSTALL.VMS' for installation instructions on this platform.


  3. Other systems using GNU Make

    On non-Unix platforms,  it is possible to build  the library using
    GNU Make  utility.  Note that  *NO OTHER MAKE TOOL  WILL WORK*[1]!
    This  methods supports  several  compilers on  Windows, OS/2,  and
    BeOS, including MinGW, Visual C++, Borland C++, and more.

    Instructions are provided in the file `INSTALL.GNU'.


  4. With an IDE Project File (e.g., for Visual Studio or CodeWarrior)

    We provide a  small number of `project files'  for various IDEs to
    automatically build  the library as  well.  Note that  these files
    are  not supported  and only  sporadically maintained  by FreeType
    developers, so don't expect them to work in each release.

    To find them, have a  look at the content of the `builds/<system>'
    directory, where <system> stands for your OS or environment.


  5. Using cmake

    See the top-level `CMakeLists.txt' file for more information.


  6. From you own IDE, or own Makefiles

    If  you  want  to  create   your  own  project  file,  follow  the
    instructions   given  in  the   `INSTALL.ANY'  document   of  this
    directory.


II. Custom builds of the library
================================

  Customizing the compilation  of FreeType is easy, and  allows you to
  select only the components of  the font engine that you really need.
  For more details read the file `CUSTOMIZE'.


----------------------------------------------------------------------

[1] make++, a make tool written in Perl, has sufficient support of GNU
    make extensions to build FreeType.  See

      http://makepp.sourceforge.net

    for more information;  you need version 2.0 or newer, and you must
    pass option `--norc-substitution'.

----------------------------------------------------------------------

Copyright 2000-2018 by
David Turner, Robert Wilhelm, and Werner Lemberg.

This  file is  part of  the FreeType  project, and  may only  be used,
modified,  and distributed  under the  terms of  the  FreeType project
license,  LICENSE.TXT.  By  continuing to  use, modify,  or distribute
this file you  indicate that you have read  the license and understand
and accept it fully.


--- end of INSTALL ---

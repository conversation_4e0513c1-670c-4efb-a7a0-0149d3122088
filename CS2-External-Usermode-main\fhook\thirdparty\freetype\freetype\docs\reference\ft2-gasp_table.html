<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"https://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>FreeType-2.9.1 API Reference</title>
<style type="text/css">
  a:link { color: #0000EF; }
  a:visited { color: #51188E; }
  a:hover { color: #FF0000; }

  body { font-family: Verdana, Geneva, Arial, Helvetica, serif;
         color: #000000;
         background: #FFFFFF;
         width: 87%;
         margin: auto; }

  div.section { width: 75%;
                margin: auto; }
  div.section hr { margin: 4ex 0 1ex 0; }
  div.section h4 { background-color: #EEEEFF;
                   font-size: medium;
                   font-style: oblique;
                   font-weight: bold;
                   margin: 3ex 0 1.5ex 9%;
                   padding: 0.3ex 0 0.3ex 1%; }
  div.section p { margin: 1.5ex 0 1.5ex 10%; }
  div.section pre { margin: 3ex 0 3ex 9%;
                    background-color: #D6E8FF;
                    padding: 2ex 0 2ex 1%; }
  div.section table.fields { width: 90%;
                             margin: 1.5ex 0 1.5ex 10%; }
  div.section table.toc { width: 95%;
                          margin: 1.5ex 0 1.5ex 5%; }
  div.timestamp { text-align: center;
                  font-size: 69%;
                  margin: 1.5ex 0 1.5ex 0; }

  h1 { text-align: center; }
  h3 { font-size: medium;
       margin: 4ex 0 1.5ex 0; }

  p { text-align: justify; }

  pre.colored { color: blue; }

  span.keyword { font-family: monospace;
                 text-align: left;
                 white-space: pre;
                 color: darkblue; }

  table.fields td.val { font-weight: bold;
                        text-align: right;
                        width: 30%;
                        vertical-align: baseline;
                        padding: 1ex 1em 1ex 0; }
  table.fields td.desc { vertical-align: baseline;
                         padding: 1ex 0 1ex 1em; }
  table.fields td.desc p:first-child { margin: 0; }
  table.fields td.desc p { margin: 1.5ex 0 0 0; }
  table.index { margin: 6ex auto 6ex auto;
                border: 0;
                border-collapse: separate;
                border-spacing: 1em 0.3ex; }
  table.index tr { padding: 0; }
  table.index td { padding: 0; }
  table.index-toc-link { width: 100%;
                         border: 0;
                         border-spacing: 0;
                         margin: 1ex 0 1ex 0; }
  table.index-toc-link td.left { padding: 0 0.5em 0 0.5em;
                                 font-size: 83%;
                                 text-align: left; }
  table.index-toc-link td.middle { padding: 0 0.5em 0 0.5em;
                                   font-size: 83%;
                                   text-align: center; }
  table.index-toc-link td.right { padding: 0 0.5em 0 0.5em;
                                  font-size: 83%;
                                  text-align: right; }
  table.synopsis { margin: 6ex auto 6ex auto;
                   border: 0;
                   border-collapse: separate;
                   border-spacing: 2em 0.6ex; }
  table.synopsis tr { padding: 0; }
  table.synopsis td { padding: 0; }
  table.toc td.link { width: 30%;
                      text-align: right;
                      vertical-align: baseline;
                      padding: 1ex 1em 1ex 0; }
  table.toc td.desc { vertical-align: baseline;
                      padding: 1ex 0 1ex 1em;
                      text-align: left; }
  table.toc td.desc p:first-child { margin: 0;
                                    text-align: left; }
  table.toc td.desc p { margin: 1.5ex 0 0 0;
                        text-align: left; }

</style>
</head>
<body>

<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table>
<h1>FreeType-2.9.1 API Reference</h1>

<h1 id="gasp_table">Gasp Table</h1>
<h2>Synopsis</h2>
<table class="synopsis">
<tr><td><a href="#FT_GASP_XXX">FT_GASP_XXX</a></td><td><a href="#FT_Get_Gasp">FT_Get_Gasp</a></td><td></td><td></td><td></td><td></td></tr>
</table>


<p>The function <a href="ft2-gasp_table.html#FT_Get_Gasp">FT_Get_Gasp</a> can be used to query a TrueType or OpenType font for specific entries in its &lsquo;gasp&rsquo; table, if any. This is mainly useful when implementing native TrueType hinting with the bytecode interpreter to duplicate the Windows text rendering results.</p>

<div class="section">
<h3 id="FT_GASP_XXX">FT_GASP_XXX</h3>
<p>Defined in FT_GASP_H (freetype/ftgasp.h).</p>
<pre>
#define <a href="ft2-gasp_table.html#FT_GASP_NO_TABLE">FT_GASP_NO_TABLE</a>               -1
#define <a href="ft2-gasp_table.html#FT_GASP_DO_GRIDFIT">FT_GASP_DO_GRIDFIT</a>           0x01
#define <a href="ft2-gasp_table.html#FT_GASP_DO_GRAY">FT_GASP_DO_GRAY</a>              0x02
#define <a href="ft2-gasp_table.html#FT_GASP_SYMMETRIC_GRIDFIT">FT_GASP_SYMMETRIC_GRIDFIT</a>    0x04
#define <a href="ft2-gasp_table.html#FT_GASP_SYMMETRIC_SMOOTHING">FT_GASP_SYMMETRIC_SMOOTHING</a>  0x08
</pre>

<p>A list of values and/or bit-flags returned by the <a href="ft2-gasp_table.html#FT_Get_Gasp">FT_Get_Gasp</a> function.</p>

<h4>values</h4>
<table class="fields">
<tr><td class="val" id="FT_GASP_NO_TABLE">FT_GASP_NO_TABLE</td><td class="desc">
<p>This special value means that there is no GASP table in this face. It is up to the client to decide what to do.</p>
</td></tr>
<tr><td class="val" id="FT_GASP_DO_GRIDFIT">FT_GASP_DO_GRIDFIT</td><td class="desc">
<p>Grid-fitting and hinting should be performed at the specified ppem. This <b>really</b> means TrueType bytecode interpretation. If this bit is not set, no hinting gets applied.</p>
</td></tr>
<tr><td class="val" id="FT_GASP_DO_GRAY">FT_GASP_DO_GRAY</td><td class="desc">
<p>Anti-aliased rendering should be performed at the specified ppem. If not set, do monochrome rendering.</p>
</td></tr>
<tr><td class="val" id="FT_GASP_SYMMETRIC_SMOOTHING">FT_GASP_SYMMETRIC_SMOOTHING</td><td class="desc">
<p>If set, smoothing along multiple axes must be used with ClearType.</p>
</td></tr>
<tr><td class="val" id="FT_GASP_SYMMETRIC_GRIDFIT">FT_GASP_SYMMETRIC_GRIDFIT</td><td class="desc">
<p>Grid-fitting must be used with ClearType's symmetric smoothing.</p>
</td></tr>
</table>

<h4>note</h4>
<p>The bit-flags &lsquo;FT_GASP_DO_GRIDFIT&rsquo; and &lsquo;FT_GASP_DO_GRAY&rsquo; are to be used for standard font rasterization only. Independently of that, &lsquo;FT_GASP_SYMMETRIC_SMOOTHING&rsquo; and &lsquo;FT_GASP_SYMMETRIC_GRIDFIT&rsquo; are to be used if ClearType is enabled (and &lsquo;FT_GASP_DO_GRIDFIT&rsquo; and &lsquo;FT_GASP_DO_GRAY&rsquo; are consequently ignored).</p>
<p>&lsquo;ClearType&rsquo; is Microsoft's implementation of LCD rendering, partly protected by patents.</p>

<h4>since</h4>
<p>2.3.0</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_Get_Gasp">FT_Get_Gasp</h3>
<p>Defined in FT_GASP_H (freetype/ftgasp.h).</p>
<pre>
  FT_EXPORT( <a href="ft2-basic_types.html#FT_Int">FT_Int</a> )
  <b>FT_Get_Gasp</b>( <a href="ft2-base_interface.html#FT_Face">FT_Face</a>  face,
               <a href="ft2-basic_types.html#FT_UInt">FT_UInt</a>  ppem );
</pre>

<p>For a TrueType or OpenType font file, return the rasterizer behaviour flags from the font's &lsquo;gasp&rsquo; table corresponding to a given character pixel size.</p>

<h4>input</h4>
<table class="fields">
<tr><td class="val" id="face">face</td><td class="desc">
<p>The source face handle.</p>
</td></tr>
<tr><td class="val" id="ppem">ppem</td><td class="desc">
<p>The vertical character pixel size.</p>
</td></tr>
</table>

<h4>return</h4>
<p>Bit flags (see <a href="ft2-gasp_table.html#FT_GASP_XXX">FT_GASP_XXX</a>), or <a href="ft2-gasp_table.html#FT_GASP_XXX">FT_GASP_NO_TABLE</a> if there is no &lsquo;gasp&rsquo; table in the face.</p>

<h4>note</h4>
<p>If you want to use the MM functionality of OpenType variation fonts (i.e., using <a href="ft2-multiple_masters.html#FT_Set_Var_Design_Coordinates">FT_Set_Var_Design_Coordinates</a> and friends), call this function <b>after</b> setting an instance since the return values can change.</p>

<h4>since</h4>
<p>2.3.0</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

</body>
</html>

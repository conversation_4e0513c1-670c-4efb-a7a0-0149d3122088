/***************************************************************************/
/*                                                                         */
/* This file defines the structure of the FreeType reference.              */
/* It is used by the python script that generates the HTML files.          */
/*                                                                         */
/***************************************************************************/


/***************************************************************************/
/*                                                                         */
/* <Chapter>                                                               */
/*    general_remarks                                                      */
/*                                                                         */
/* <Title>                                                                 */
/*    General Remarks                                                      */
/*                                                                         */
/* <Sections>                                                              */
/*    header_inclusion                                                     */
/*    user_allocation                                                      */
/*                                                                         */
/***************************************************************************/


/***************************************************************************/
/*                                                                         */
/* <Chapter>                                                               */
/*    core_api                                                             */
/*                                                                         */
/* <Title>                                                                 */
/*    Core API                                                             */
/*                                                                         */
/* <Sections>                                                              */
/*    version                                                              */
/*    basic_types                                                          */
/*    base_interface                                                       */
/*    glyph_variants                                                       */
/*    glyph_management                                                     */
/*    mac_specific                                                         */
/*    sizes_management                                                     */
/*    header_file_macros                                                   */
/*                                                                         */
/***************************************************************************/


/***************************************************************************/
/*                                                                         */
/* <Chapter>                                                               */
/*    format_specific                                                      */
/*                                                                         */
/* <Title>                                                                 */
/*    Format-Specific API                                                  */
/*                                                                         */
/* <Sections>                                                              */
/*    multiple_masters                                                     */
/*    truetype_tables                                                      */
/*    type1_tables                                                         */
/*    sfnt_names                                                           */
/*    bdf_fonts                                                            */
/*    cid_fonts                                                            */
/*    pfr_fonts                                                            */
/*    winfnt_fonts                                                         */
/*    font_formats                                                         */
/*    gasp_table                                                           */
/*                                                                         */
/***************************************************************************/


/***************************************************************************/
/*                                                                         */
/* <Chapter>                                                               */
/*    module_specific                                                      */
/*                                                                         */
/* <Title>                                                                 */
/*    Controlling FreeType Modules                                         */
/*                                                                         */
/* <Sections>                                                              */
/*    auto_hinter                                                          */
/*    cff_driver                                                           */
/*    t1_cid_driver                                                        */
/*    tt_driver                                                            */
/*    pcf_driver                                                           */
/*    properties                                                           */
/*    parameter_tags                                                       */
/*                                                                         */
/***************************************************************************/


/***************************************************************************/
/*                                                                         */
/* <Chapter>                                                               */
/*    cache_subsystem                                                      */
/*                                                                         */
/* <Title>                                                                 */
/*    Cache Sub-System                                                     */
/*                                                                         */
/* <Sections>                                                              */
/*    cache_subsystem                                                      */
/*                                                                         */
/***************************************************************************/


/***************************************************************************/
/*                                                                         */
/* <Chapter>                                                               */
/*    support_api                                                          */
/*                                                                         */
/* <Title>                                                                 */
/*    Support API                                                          */
/*                                                                         */
/* <Sections>                                                              */
/*    computations                                                         */
/*    list_processing                                                      */
/*    outline_processing                                                   */
/*    quick_advance                                                        */
/*    bitmap_handling                                                      */
/*    raster                                                               */
/*    glyph_stroker                                                        */
/*    system_interface                                                     */
/*    module_management                                                    */
/*    gzip                                                                 */
/*    lzw                                                                  */
/*    bzip2                                                                */
/*    lcd_filtering                                                        */
/*                                                                         */
/***************************************************************************/

/***************************************************************************/
/*                                                                         */
/* <Chapter>                                                               */
/*    error_codes                                                          */
/*                                                                         */
/* <Title>                                                                 */
/*    Error Codes                                                          */
/*                                                                         */
/* <Sections>                                                              */
/*    error_enumerations                                                   */
/*    error_code_values                                                    */
/*                                                                         */
/***************************************************************************/

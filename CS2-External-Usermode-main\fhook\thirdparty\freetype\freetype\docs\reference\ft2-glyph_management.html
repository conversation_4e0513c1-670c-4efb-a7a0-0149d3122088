<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"https://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>FreeType-2.9.1 API Reference</title>
<style type="text/css">
  a:link { color: #0000EF; }
  a:visited { color: #51188E; }
  a:hover { color: #FF0000; }

  body { font-family: Verdana, Geneva, Arial, Helvetica, serif;
         color: #000000;
         background: #FFFFFF;
         width: 87%;
         margin: auto; }

  div.section { width: 75%;
                margin: auto; }
  div.section hr { margin: 4ex 0 1ex 0; }
  div.section h4 { background-color: #EEEEFF;
                   font-size: medium;
                   font-style: oblique;
                   font-weight: bold;
                   margin: 3ex 0 1.5ex 9%;
                   padding: 0.3ex 0 0.3ex 1%; }
  div.section p { margin: 1.5ex 0 1.5ex 10%; }
  div.section pre { margin: 3ex 0 3ex 9%;
                    background-color: #D6E8FF;
                    padding: 2ex 0 2ex 1%; }
  div.section table.fields { width: 90%;
                             margin: 1.5ex 0 1.5ex 10%; }
  div.section table.toc { width: 95%;
                          margin: 1.5ex 0 1.5ex 5%; }
  div.timestamp { text-align: center;
                  font-size: 69%;
                  margin: 1.5ex 0 1.5ex 0; }

  h1 { text-align: center; }
  h3 { font-size: medium;
       margin: 4ex 0 1.5ex 0; }

  p { text-align: justify; }

  pre.colored { color: blue; }

  span.keyword { font-family: monospace;
                 text-align: left;
                 white-space: pre;
                 color: darkblue; }

  table.fields td.val { font-weight: bold;
                        text-align: right;
                        width: 30%;
                        vertical-align: baseline;
                        padding: 1ex 1em 1ex 0; }
  table.fields td.desc { vertical-align: baseline;
                         padding: 1ex 0 1ex 1em; }
  table.fields td.desc p:first-child { margin: 0; }
  table.fields td.desc p { margin: 1.5ex 0 0 0; }
  table.index { margin: 6ex auto 6ex auto;
                border: 0;
                border-collapse: separate;
                border-spacing: 1em 0.3ex; }
  table.index tr { padding: 0; }
  table.index td { padding: 0; }
  table.index-toc-link { width: 100%;
                         border: 0;
                         border-spacing: 0;
                         margin: 1ex 0 1ex 0; }
  table.index-toc-link td.left { padding: 0 0.5em 0 0.5em;
                                 font-size: 83%;
                                 text-align: left; }
  table.index-toc-link td.middle { padding: 0 0.5em 0 0.5em;
                                   font-size: 83%;
                                   text-align: center; }
  table.index-toc-link td.right { padding: 0 0.5em 0 0.5em;
                                  font-size: 83%;
                                  text-align: right; }
  table.synopsis { margin: 6ex auto 6ex auto;
                   border: 0;
                   border-collapse: separate;
                   border-spacing: 2em 0.6ex; }
  table.synopsis tr { padding: 0; }
  table.synopsis td { padding: 0; }
  table.toc td.link { width: 30%;
                      text-align: right;
                      vertical-align: baseline;
                      padding: 1ex 1em 1ex 0; }
  table.toc td.desc { vertical-align: baseline;
                      padding: 1ex 0 1ex 1em;
                      text-align: left; }
  table.toc td.desc p:first-child { margin: 0;
                                    text-align: left; }
  table.toc td.desc p { margin: 1.5ex 0 0 0;
                        text-align: left; }

</style>
</head>
<body>

<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table>
<h1>FreeType-2.9.1 API Reference</h1>

<h1 id="glyph_management">Glyph Management</h1>
<h2>Synopsis</h2>
<table class="synopsis">
<tr><td><a href="#FT_Glyph">FT_Glyph</a></td><td><a href="#FT_OutlineGlyphRec">FT_OutlineGlyphRec</a></td><td><a href="#FT_Glyph_Get_CBox">FT_Glyph_Get_CBox</a></td></tr>
<tr><td><a href="#FT_GlyphRec">FT_GlyphRec</a></td><td><a href="#FT_Get_Glyph">FT_Get_Glyph</a></td><td><a href="#FT_Glyph_To_Bitmap">FT_Glyph_To_Bitmap</a></td></tr>
<tr><td><a href="#FT_BitmapGlyph">FT_BitmapGlyph</a></td><td><a href="#FT_Glyph_Copy">FT_Glyph_Copy</a></td><td><a href="#FT_Done_Glyph">FT_Done_Glyph</a></td></tr>
<tr><td><a href="#FT_BitmapGlyphRec">FT_BitmapGlyphRec</a></td><td><a href="#FT_Glyph_Transform">FT_Glyph_Transform</a></td><td></td></tr>
<tr><td><a href="#FT_OutlineGlyph">FT_OutlineGlyph</a></td><td><a href="#FT_Glyph_BBox_Mode">FT_Glyph_BBox_Mode</a></td><td></td></tr>
</table>


<p>This section contains definitions used to manage glyph data through generic FT_Glyph objects. Each of them can contain a bitmap, a vector outline, or even images in other formats.</p>

<div class="section">
<h3 id="FT_Glyph">FT_Glyph</h3>
<p>Defined in FT_GLYPH_H (freetype/ftglyph.h).</p>
<pre>
  <span class="keyword">typedef</span> <span class="keyword">struct</span> FT_GlyphRec_*  <b>FT_Glyph</b>;
</pre>

<p>Handle to an object used to model generic glyph images. It is a pointer to the <a href="ft2-glyph_management.html#FT_GlyphRec">FT_GlyphRec</a> structure and can contain a glyph bitmap or pointer.</p>

<h4>note</h4>
<p>Glyph objects are not owned by the library. You must thus release them manually (through <a href="ft2-glyph_management.html#FT_Done_Glyph">FT_Done_Glyph</a>) <i>before</i> calling <a href="ft2-base_interface.html#FT_Done_FreeType">FT_Done_FreeType</a>.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_GlyphRec">FT_GlyphRec</h3>
<p>Defined in FT_GLYPH_H (freetype/ftglyph.h).</p>
<pre>
  <span class="keyword">typedef</span> <span class="keyword">struct</span>  FT_GlyphRec_
  {
    <a href="ft2-base_interface.html#FT_Library">FT_Library</a>             library;
    <span class="keyword">const</span> FT_Glyph_Class*  clazz;
    <a href="ft2-basic_types.html#FT_Glyph_Format">FT_Glyph_Format</a>        format;
    <a href="ft2-basic_types.html#FT_Vector">FT_Vector</a>              advance;

  } <b>FT_GlyphRec</b>;
</pre>

<p>The root glyph structure contains a given glyph image plus its advance width in 16.16 fixed-point format.</p>

<h4>fields</h4>
<table class="fields">
<tr><td class="val" id="library">library</td><td class="desc">
<p>A handle to the FreeType library object.</p>
</td></tr>
<tr><td class="val" id="clazz">clazz</td><td class="desc">
<p>A pointer to the glyph's class. Private.</p>
</td></tr>
<tr><td class="val" id="format">format</td><td class="desc">
<p>The format of the glyph's image.</p>
</td></tr>
<tr><td class="val" id="advance">advance</td><td class="desc">
<p>A 16.16 vector that gives the glyph's advance width.</p>
</td></tr>
</table>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_BitmapGlyph">FT_BitmapGlyph</h3>
<p>Defined in FT_GLYPH_H (freetype/ftglyph.h).</p>
<pre>
  <span class="keyword">typedef</span> <span class="keyword">struct</span> FT_BitmapGlyphRec_*  <b>FT_BitmapGlyph</b>;
</pre>

<p>A handle to an object used to model a bitmap glyph image. This is a sub-class of <a href="ft2-glyph_management.html#FT_Glyph">FT_Glyph</a>, and a pointer to <a href="ft2-glyph_management.html#FT_BitmapGlyphRec">FT_BitmapGlyphRec</a>.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_BitmapGlyphRec">FT_BitmapGlyphRec</h3>
<p>Defined in FT_GLYPH_H (freetype/ftglyph.h).</p>
<pre>
  <span class="keyword">typedef</span> <span class="keyword">struct</span>  FT_BitmapGlyphRec_
  {
    <a href="ft2-glyph_management.html#FT_GlyphRec">FT_GlyphRec</a>  root;
    <a href="ft2-basic_types.html#FT_Int">FT_Int</a>       left;
    <a href="ft2-basic_types.html#FT_Int">FT_Int</a>       top;
    <a href="ft2-basic_types.html#FT_Bitmap">FT_Bitmap</a>    bitmap;

  } <b>FT_BitmapGlyphRec</b>;
</pre>

<p>A structure used for bitmap glyph images. This really is a &lsquo;sub-class&rsquo; of <a href="ft2-glyph_management.html#FT_GlyphRec">FT_GlyphRec</a>.</p>

<h4>fields</h4>
<table class="fields">
<tr><td class="val" id="root">root</td><td class="desc">
<p>The root <a href="ft2-glyph_management.html#FT_Glyph">FT_Glyph</a> fields.</p>
</td></tr>
<tr><td class="val" id="left">left</td><td class="desc">
<p>The left-side bearing, i.e., the horizontal distance from the current pen position to the left border of the glyph bitmap.</p>
</td></tr>
<tr><td class="val" id="top">top</td><td class="desc">
<p>The top-side bearing, i.e., the vertical distance from the current pen position to the top border of the glyph bitmap. This distance is positive for upwards&nbsp;y!</p>
</td></tr>
<tr><td class="val" id="bitmap">bitmap</td><td class="desc">
<p>A descriptor for the bitmap.</p>
</td></tr>
</table>

<h4>note</h4>
<p>You can typecast an <a href="ft2-glyph_management.html#FT_Glyph">FT_Glyph</a> to <a href="ft2-glyph_management.html#FT_BitmapGlyph">FT_BitmapGlyph</a> if you have &lsquo;glyph-&gt;format == FT_GLYPH_FORMAT_BITMAP&rsquo;. This lets you access the bitmap's contents easily.</p>
<p>The corresponding pixel buffer is always owned by <a href="ft2-glyph_management.html#FT_BitmapGlyph">FT_BitmapGlyph</a> and is thus created and destroyed with it.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_OutlineGlyph">FT_OutlineGlyph</h3>
<p>Defined in FT_GLYPH_H (freetype/ftglyph.h).</p>
<pre>
  <span class="keyword">typedef</span> <span class="keyword">struct</span> FT_OutlineGlyphRec_*  <b>FT_OutlineGlyph</b>;
</pre>

<p>A handle to an object used to model an outline glyph image. This is a sub-class of <a href="ft2-glyph_management.html#FT_Glyph">FT_Glyph</a>, and a pointer to <a href="ft2-glyph_management.html#FT_OutlineGlyphRec">FT_OutlineGlyphRec</a>.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_OutlineGlyphRec">FT_OutlineGlyphRec</h3>
<p>Defined in FT_GLYPH_H (freetype/ftglyph.h).</p>
<pre>
  <span class="keyword">typedef</span> <span class="keyword">struct</span>  FT_OutlineGlyphRec_
  {
    <a href="ft2-glyph_management.html#FT_GlyphRec">FT_GlyphRec</a>  root;
    <a href="ft2-outline_processing.html#FT_Outline">FT_Outline</a>   outline;

  } <b>FT_OutlineGlyphRec</b>;
</pre>

<p>A structure used for outline (vectorial) glyph images. This really is a &lsquo;sub-class&rsquo; of <a href="ft2-glyph_management.html#FT_GlyphRec">FT_GlyphRec</a>.</p>

<h4>fields</h4>
<table class="fields">
<tr><td class="val" id="root">root</td><td class="desc">
<p>The root <a href="ft2-glyph_management.html#FT_Glyph">FT_Glyph</a> fields.</p>
</td></tr>
<tr><td class="val" id="outline">outline</td><td class="desc">
<p>A descriptor for the outline.</p>
</td></tr>
</table>

<h4>note</h4>
<p>You can typecast an <a href="ft2-glyph_management.html#FT_Glyph">FT_Glyph</a> to <a href="ft2-glyph_management.html#FT_OutlineGlyph">FT_OutlineGlyph</a> if you have &lsquo;glyph-&gt;format == FT_GLYPH_FORMAT_OUTLINE&rsquo;. This lets you access the outline's content easily.</p>
<p>As the outline is extracted from a glyph slot, its coordinates are expressed normally in 26.6 pixels, unless the flag <a href="ft2-base_interface.html#FT_LOAD_XXX">FT_LOAD_NO_SCALE</a> was used in <a href="ft2-base_interface.html#FT_Load_Glyph">FT_Load_Glyph</a>() or <a href="ft2-base_interface.html#FT_Load_Char">FT_Load_Char</a>().</p>
<p>The outline's tables are always owned by the object and are destroyed with it.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_Get_Glyph">FT_Get_Glyph</h3>
<p>Defined in FT_GLYPH_H (freetype/ftglyph.h).</p>
<pre>
  FT_EXPORT( <a href="ft2-basic_types.html#FT_Error">FT_Error</a> )
  <b>FT_Get_Glyph</b>( <a href="ft2-base_interface.html#FT_GlyphSlot">FT_GlyphSlot</a>  slot,
                <a href="ft2-glyph_management.html#FT_Glyph">FT_Glyph</a>     *aglyph );
</pre>

<p>A function used to extract a glyph image from a slot. Note that the created <a href="ft2-glyph_management.html#FT_Glyph">FT_Glyph</a> object must be released with <a href="ft2-glyph_management.html#FT_Done_Glyph">FT_Done_Glyph</a>.</p>

<h4>input</h4>
<table class="fields">
<tr><td class="val" id="slot">slot</td><td class="desc">
<p>A handle to the source glyph slot.</p>
</td></tr>
</table>

<h4>output</h4>
<table class="fields">
<tr><td class="val" id="aglyph">aglyph</td><td class="desc">
<p>A handle to the glyph object.</p>
</td></tr>
</table>

<h4>return</h4>
<p>FreeType error code. 0&nbsp;means success.</p>

<h4>note</h4>
<p>Because &lsquo;*aglyph-&gt;advance.x&rsquo; and '*aglyph-&gt;advance.y' are 16.16 fixed-point numbers, &lsquo;slot-&gt;advance.x&rsquo; and &lsquo;slot-&gt;advance.y&rsquo; (which are in 26.6 fixed-point format) must be in the range ]-32768;32768[.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_Glyph_Copy">FT_Glyph_Copy</h3>
<p>Defined in FT_GLYPH_H (freetype/ftglyph.h).</p>
<pre>
  FT_EXPORT( <a href="ft2-basic_types.html#FT_Error">FT_Error</a> )
  <b>FT_Glyph_Copy</b>( <a href="ft2-glyph_management.html#FT_Glyph">FT_Glyph</a>   source,
                 <a href="ft2-glyph_management.html#FT_Glyph">FT_Glyph</a>  *target );
</pre>

<p>A function used to copy a glyph image. Note that the created <a href="ft2-glyph_management.html#FT_Glyph">FT_Glyph</a> object must be released with <a href="ft2-glyph_management.html#FT_Done_Glyph">FT_Done_Glyph</a>.</p>

<h4>input</h4>
<table class="fields">
<tr><td class="val" id="source">source</td><td class="desc">
<p>A handle to the source glyph object.</p>
</td></tr>
</table>

<h4>output</h4>
<table class="fields">
<tr><td class="val" id="target">target</td><td class="desc">
<p>A handle to the target glyph object. 0&nbsp;in case of error.</p>
</td></tr>
</table>

<h4>return</h4>
<p>FreeType error code. 0&nbsp;means success.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_Glyph_Transform">FT_Glyph_Transform</h3>
<p>Defined in FT_GLYPH_H (freetype/ftglyph.h).</p>
<pre>
  FT_EXPORT( <a href="ft2-basic_types.html#FT_Error">FT_Error</a> )
  <b>FT_Glyph_Transform</b>( <a href="ft2-glyph_management.html#FT_Glyph">FT_Glyph</a>    glyph,
                      <a href="ft2-basic_types.html#FT_Matrix">FT_Matrix</a>*  matrix,
                      <a href="ft2-basic_types.html#FT_Vector">FT_Vector</a>*  delta );
</pre>

<p>Transform a glyph image if its format is scalable.</p>

<h4>inout</h4>
<table class="fields">
<tr><td class="val" id="glyph">glyph</td><td class="desc">
<p>A handle to the target glyph object.</p>
</td></tr>
</table>

<h4>input</h4>
<table class="fields">
<tr><td class="val" id="matrix">matrix</td><td class="desc">
<p>A pointer to a 2x2 matrix to apply.</p>
</td></tr>
<tr><td class="val" id="delta">delta</td><td class="desc">
<p>A pointer to a 2d vector to apply. Coordinates are expressed in 1/64th of a pixel.</p>
</td></tr>
</table>

<h4>return</h4>
<p>FreeType error code (if not 0, the glyph format is not scalable).</p>

<h4>note</h4>
<p>The 2x2 transformation matrix is also applied to the glyph's advance vector.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_Glyph_BBox_Mode">FT_Glyph_BBox_Mode</h3>
<p>Defined in FT_GLYPH_H (freetype/ftglyph.h).</p>
<pre>
  <span class="keyword">typedef</span> <span class="keyword">enum</span>  FT_Glyph_BBox_Mode_
  {
    <a href="ft2-glyph_management.html#FT_GLYPH_BBOX_UNSCALED">FT_GLYPH_BBOX_UNSCALED</a>  = 0,
    <a href="ft2-glyph_management.html#FT_GLYPH_BBOX_SUBPIXELS">FT_GLYPH_BBOX_SUBPIXELS</a> = 0,
    <a href="ft2-glyph_management.html#FT_GLYPH_BBOX_GRIDFIT">FT_GLYPH_BBOX_GRIDFIT</a>   = 1,
    <a href="ft2-glyph_management.html#FT_GLYPH_BBOX_TRUNCATE">FT_GLYPH_BBOX_TRUNCATE</a>  = 2,
    <a href="ft2-glyph_management.html#FT_GLYPH_BBOX_PIXELS">FT_GLYPH_BBOX_PIXELS</a>    = 3

  } <b>FT_Glyph_BBox_Mode</b>;


  /* these constants are deprecated; use the corresponding */
  /* `<b>FT_Glyph_BBox_Mode</b>' values instead                   */
#define ft_glyph_bbox_unscaled   <a href="ft2-glyph_management.html#FT_GLYPH_BBOX_UNSCALED">FT_GLYPH_BBOX_UNSCALED</a>
#define ft_glyph_bbox_subpixels  <a href="ft2-glyph_management.html#FT_GLYPH_BBOX_SUBPIXELS">FT_GLYPH_BBOX_SUBPIXELS</a>
#define ft_glyph_bbox_gridfit    <a href="ft2-glyph_management.html#FT_GLYPH_BBOX_GRIDFIT">FT_GLYPH_BBOX_GRIDFIT</a>
#define ft_glyph_bbox_truncate   <a href="ft2-glyph_management.html#FT_GLYPH_BBOX_TRUNCATE">FT_GLYPH_BBOX_TRUNCATE</a>
#define ft_glyph_bbox_pixels     <a href="ft2-glyph_management.html#FT_GLYPH_BBOX_PIXELS">FT_GLYPH_BBOX_PIXELS</a>
</pre>

<p>The mode how the values of <a href="ft2-glyph_management.html#FT_Glyph_Get_CBox">FT_Glyph_Get_CBox</a> are returned.</p>

<h4>values</h4>
<table class="fields">
<tr><td class="val" id="FT_GLYPH_BBOX_UNSCALED">FT_GLYPH_BBOX_UNSCALED</td><td class="desc">
<p>Return unscaled font units.</p>
</td></tr>
<tr><td class="val" id="FT_GLYPH_BBOX_SUBPIXELS">FT_GLYPH_BBOX_SUBPIXELS</td><td class="desc">
<p>Return unfitted 26.6 coordinates.</p>
</td></tr>
<tr><td class="val" id="FT_GLYPH_BBOX_GRIDFIT">FT_GLYPH_BBOX_GRIDFIT</td><td class="desc">
<p>Return grid-fitted 26.6 coordinates.</p>
</td></tr>
<tr><td class="val" id="FT_GLYPH_BBOX_TRUNCATE">FT_GLYPH_BBOX_TRUNCATE</td><td class="desc">
<p>Return coordinates in integer pixels.</p>
</td></tr>
<tr><td class="val" id="FT_GLYPH_BBOX_PIXELS">FT_GLYPH_BBOX_PIXELS</td><td class="desc">
<p>Return grid-fitted pixel coordinates.</p>
</td></tr>
</table>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_Glyph_Get_CBox">FT_Glyph_Get_CBox</h3>
<p>Defined in FT_GLYPH_H (freetype/ftglyph.h).</p>
<pre>
  FT_EXPORT( <span class="keyword">void</span> )
  <b>FT_Glyph_Get_CBox</b>( <a href="ft2-glyph_management.html#FT_Glyph">FT_Glyph</a>  glyph,
                     <a href="ft2-basic_types.html#FT_UInt">FT_UInt</a>   bbox_mode,
                     <a href="ft2-basic_types.html#FT_BBox">FT_BBox</a>  *acbox );
</pre>

<p>Return a glyph's &lsquo;control box&rsquo;. The control box encloses all the outline's points, including Bezier control points. Though it coincides with the exact bounding box for most glyphs, it can be slightly larger in some situations (like when rotating an outline that contains Bezier outside arcs).</p>
<p>Computing the control box is very fast, while getting the bounding box can take much more time as it needs to walk over all segments and arcs in the outline. To get the latter, you can use the &lsquo;ftbbox&rsquo; component, which is dedicated to this single task.</p>

<h4>input</h4>
<table class="fields">
<tr><td class="val" id="glyph">glyph</td><td class="desc">
<p>A handle to the source glyph object.</p>
</td></tr>
<tr><td class="val" id="mode">mode</td><td class="desc">
<p>The mode that indicates how to interpret the returned bounding box values.</p>
</td></tr>
</table>

<h4>output</h4>
<table class="fields">
<tr><td class="val" id="acbox">acbox</td><td class="desc">
<p>The glyph coordinate bounding box. Coordinates are expressed in 1/64th of pixels if it is grid-fitted.</p>
</td></tr>
</table>

<h4>note</h4>
<p>Coordinates are relative to the glyph origin, using the y&nbsp;upwards convention.</p>
<p>If the glyph has been loaded with <a href="ft2-base_interface.html#FT_LOAD_XXX">FT_LOAD_NO_SCALE</a>, &lsquo;bbox_mode&rsquo; must be set to <a href="ft2-glyph_management.html#FT_Glyph_BBox_Mode">FT_GLYPH_BBOX_UNSCALED</a> to get unscaled font units in 26.6 pixel format. The value <a href="ft2-glyph_management.html#FT_Glyph_BBox_Mode">FT_GLYPH_BBOX_SUBPIXELS</a> is another name for this constant.</p>
<p>If the font is tricky and the glyph has been loaded with <a href="ft2-base_interface.html#FT_LOAD_XXX">FT_LOAD_NO_SCALE</a>, the resulting CBox is meaningless. To get reasonable values for the CBox it is necessary to load the glyph at a large ppem value (so that the hinting instructions can properly shift and scale the subglyphs), then extracting the CBox, which can be eventually converted back to font units.</p>
<p>Note that the maximum coordinates are exclusive, which means that one can compute the width and height of the glyph image (be it in integer or 26.6 pixels) as:</p>
<pre class="colored">
  width  = bbox.xMax - bbox.xMin;
  height = bbox.yMax - bbox.yMin;
</pre>
<p>Note also that for 26.6 coordinates, if &lsquo;bbox_mode&rsquo; is set to <a href="ft2-glyph_management.html#FT_Glyph_BBox_Mode">FT_GLYPH_BBOX_GRIDFIT</a>, the coordinates will also be grid-fitted, which corresponds to:</p>
<pre class="colored">
  bbox.xMin = FLOOR(bbox.xMin);
  bbox.yMin = FLOOR(bbox.yMin);
  bbox.xMax = CEILING(bbox.xMax);
  bbox.yMax = CEILING(bbox.yMax);
</pre>
<p>To get the bbox in pixel coordinates, set &lsquo;bbox_mode&rsquo; to <a href="ft2-glyph_management.html#FT_Glyph_BBox_Mode">FT_GLYPH_BBOX_TRUNCATE</a>.</p>
<p>To get the bbox in grid-fitted pixel coordinates, set &lsquo;bbox_mode&rsquo; to <a href="ft2-glyph_management.html#FT_Glyph_BBox_Mode">FT_GLYPH_BBOX_PIXELS</a>.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_Glyph_To_Bitmap">FT_Glyph_To_Bitmap</h3>
<p>Defined in FT_GLYPH_H (freetype/ftglyph.h).</p>
<pre>
  FT_EXPORT( <a href="ft2-basic_types.html#FT_Error">FT_Error</a> )
  <b>FT_Glyph_To_Bitmap</b>( <a href="ft2-glyph_management.html#FT_Glyph">FT_Glyph</a>*       the_glyph,
                      <a href="ft2-base_interface.html#FT_Render_Mode">FT_Render_Mode</a>  render_mode,
                      <a href="ft2-basic_types.html#FT_Vector">FT_Vector</a>*      origin,
                      <a href="ft2-basic_types.html#FT_Bool">FT_Bool</a>         destroy );
</pre>

<p>Convert a given glyph object to a bitmap glyph object.</p>

<h4>inout</h4>
<table class="fields">
<tr><td class="val" id="the_glyph">the_glyph</td><td class="desc">
<p>A pointer to a handle to the target glyph.</p>
</td></tr>
</table>

<h4>input</h4>
<table class="fields">
<tr><td class="val" id="render_mode">render_mode</td><td class="desc">
<p>An enumeration that describes how the data is rendered.</p>
</td></tr>
<tr><td class="val" id="origin">origin</td><td class="desc">
<p>A pointer to a vector used to translate the glyph image before rendering. Can be&nbsp;0 (if no translation). The origin is expressed in 26.6 pixels.</p>
</td></tr>
<tr><td class="val" id="destroy">destroy</td><td class="desc">
<p>A boolean that indicates that the original glyph image should be destroyed by this function. It is never destroyed in case of error.</p>
</td></tr>
</table>

<h4>return</h4>
<p>FreeType error code. 0&nbsp;means success.</p>

<h4>note</h4>
<p>This function does nothing if the glyph format isn't scalable.</p>
<p>The glyph image is translated with the &lsquo;origin&rsquo; vector before rendering.</p>
<p>The first parameter is a pointer to an <a href="ft2-glyph_management.html#FT_Glyph">FT_Glyph</a> handle, that will be <i>replaced</i> by this function (with newly allocated data). Typically, you would use (omitting error handling):</p>
<p></p>
<pre class="colored">
  FT_Glyph        glyph;
  FT_BitmapGlyph  glyph_bitmap;


  // load glyph
  error = FT_Load_Char( face, glyph_index, FT_LOAD_DEFAULT );

  // extract glyph image
  error = FT_Get_Glyph( face-&gt;glyph, &amp;glyph );

  // convert to a bitmap (default render mode + destroying old)
  if ( glyph-&gt;format != FT_GLYPH_FORMAT_BITMAP )
  {
    error = FT_Glyph_To_Bitmap( &amp;glyph, FT_RENDER_MODE_NORMAL,
                                0, 1 );
    if ( error ) // `glyph' unchanged
      ...
  }

  // access bitmap content by typecasting
  glyph_bitmap = (FT_BitmapGlyph)glyph;

  // do funny stuff with it, like blitting/drawing
  ...

  // discard glyph image (bitmap or not)
  FT_Done_Glyph( glyph );
</pre>
<p></p>
<p>Here another example, again without error handling:</p>
<p></p>
<pre class="colored">
  FT_Glyph  glyphs[MAX_GLYPHS]


  ...

  for ( idx = 0; i &lt; MAX_GLYPHS; i++ )
    error = FT_Load_Glyph( face, idx, FT_LOAD_DEFAULT ) ||
            FT_Get_Glyph ( face-&gt;glyph, &amp;glyph[idx] );

  ...

  for ( idx = 0; i &lt; MAX_GLYPHS; i++ )
  {
    FT_Glyph  bitmap = glyphs[idx];


    ...

    // after this call, `bitmap' no longer points into
    // the `glyphs' array (and the old value isn't destroyed)
    FT_Glyph_To_Bitmap( &amp;bitmap, FT_RENDER_MODE_MONO, 0, 0 );

    ...

    FT_Done_Glyph( bitmap );
  }

  ...

  for ( idx = 0; i &lt; MAX_GLYPHS; i++ )
    FT_Done_Glyph( glyphs[idx] );
</pre>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_Done_Glyph">FT_Done_Glyph</h3>
<p>Defined in FT_GLYPH_H (freetype/ftglyph.h).</p>
<pre>
  FT_EXPORT( <span class="keyword">void</span> )
  <b>FT_Done_Glyph</b>( <a href="ft2-glyph_management.html#FT_Glyph">FT_Glyph</a>  glyph );
</pre>

<p>Destroy a given glyph.</p>

<h4>input</h4>
<table class="fields">
<tr><td class="val" id="glyph">glyph</td><td class="desc">
<p>A handle to the target glyph object.</p>
</td></tr>
</table>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

</body>
</html>

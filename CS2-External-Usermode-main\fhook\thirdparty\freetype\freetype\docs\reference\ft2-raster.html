<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"https://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>FreeType-2.9.1 API Reference</title>
<style type="text/css">
  a:link { color: #0000EF; }
  a:visited { color: #51188E; }
  a:hover { color: #FF0000; }

  body { font-family: Verdana, Geneva, Arial, Helvetica, serif;
         color: #000000;
         background: #FFFFFF;
         width: 87%;
         margin: auto; }

  div.section { width: 75%;
                margin: auto; }
  div.section hr { margin: 4ex 0 1ex 0; }
  div.section h4 { background-color: #EEEEFF;
                   font-size: medium;
                   font-style: oblique;
                   font-weight: bold;
                   margin: 3ex 0 1.5ex 9%;
                   padding: 0.3ex 0 0.3ex 1%; }
  div.section p { margin: 1.5ex 0 1.5ex 10%; }
  div.section pre { margin: 3ex 0 3ex 9%;
                    background-color: #D6E8FF;
                    padding: 2ex 0 2ex 1%; }
  div.section table.fields { width: 90%;
                             margin: 1.5ex 0 1.5ex 10%; }
  div.section table.toc { width: 95%;
                          margin: 1.5ex 0 1.5ex 5%; }
  div.timestamp { text-align: center;
                  font-size: 69%;
                  margin: 1.5ex 0 1.5ex 0; }

  h1 { text-align: center; }
  h3 { font-size: medium;
       margin: 4ex 0 1.5ex 0; }

  p { text-align: justify; }

  pre.colored { color: blue; }

  span.keyword { font-family: monospace;
                 text-align: left;
                 white-space: pre;
                 color: darkblue; }

  table.fields td.val { font-weight: bold;
                        text-align: right;
                        width: 30%;
                        vertical-align: baseline;
                        padding: 1ex 1em 1ex 0; }
  table.fields td.desc { vertical-align: baseline;
                         padding: 1ex 0 1ex 1em; }
  table.fields td.desc p:first-child { margin: 0; }
  table.fields td.desc p { margin: 1.5ex 0 0 0; }
  table.index { margin: 6ex auto 6ex auto;
                border: 0;
                border-collapse: separate;
                border-spacing: 1em 0.3ex; }
  table.index tr { padding: 0; }
  table.index td { padding: 0; }
  table.index-toc-link { width: 100%;
                         border: 0;
                         border-spacing: 0;
                         margin: 1ex 0 1ex 0; }
  table.index-toc-link td.left { padding: 0 0.5em 0 0.5em;
                                 font-size: 83%;
                                 text-align: left; }
  table.index-toc-link td.middle { padding: 0 0.5em 0 0.5em;
                                   font-size: 83%;
                                   text-align: center; }
  table.index-toc-link td.right { padding: 0 0.5em 0 0.5em;
                                  font-size: 83%;
                                  text-align: right; }
  table.synopsis { margin: 6ex auto 6ex auto;
                   border: 0;
                   border-collapse: separate;
                   border-spacing: 2em 0.6ex; }
  table.synopsis tr { padding: 0; }
  table.synopsis td { padding: 0; }
  table.toc td.link { width: 30%;
                      text-align: right;
                      vertical-align: baseline;
                      padding: 1ex 1em 1ex 0; }
  table.toc td.desc { vertical-align: baseline;
                      padding: 1ex 0 1ex 1em;
                      text-align: left; }
  table.toc td.desc p:first-child { margin: 0;
                                    text-align: left; }
  table.toc td.desc p { margin: 1.5ex 0 0 0;
                        text-align: left; }

</style>
</head>
<body>

<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table>
<h1>FreeType-2.9.1 API Reference</h1>

<h1 id="raster">Scanline Converter</h1>
<h2>Synopsis</h2>
<table class="synopsis">
<tr><td><a href="#FT_Raster">FT_Raster</a></td><td>&nbsp;</td><td><a href="#FT_Raster_Funcs">FT_Raster_Funcs</a></td></tr>
<tr><td><a href="#FT_Span">FT_Span</a></td><td><a href="#FT_Raster_NewFunc">FT_Raster_NewFunc</a></td><td>&nbsp;</td></tr>
<tr><td><a href="#FT_SpanFunc">FT_SpanFunc</a></td><td><a href="#FT_Raster_DoneFunc">FT_Raster_DoneFunc</a></td><td><a href="#FT_Raster_BitTest_Func">FT_Raster_BitTest_Func</a></td></tr>
<tr><td>&nbsp;</td><td><a href="#FT_Raster_ResetFunc">FT_Raster_ResetFunc</a></td><td><a href="#FT_Raster_BitSet_Func">FT_Raster_BitSet_Func</a></td></tr>
<tr><td><a href="#FT_Raster_Params">FT_Raster_Params</a></td><td><a href="#FT_Raster_SetModeFunc">FT_Raster_SetModeFunc</a></td><td></td></tr>
<tr><td><a href="#FT_RASTER_FLAG_XXX">FT_RASTER_FLAG_XXX</a></td><td><a href="#FT_Raster_RenderFunc">FT_Raster_RenderFunc</a></td><td></td></tr>
</table>


<p>This section contains technical definitions.</p>

<div class="section">
<h3 id="FT_Raster">FT_Raster</h3>
<p>Defined in FT_IMAGE_H (freetype/ftimage.h).</p>
<pre>
  <span class="keyword">typedef</span> <span class="keyword">struct</span> FT_RasterRec_*  <b>FT_Raster</b>;
</pre>

<p>An opaque handle (pointer) to a raster object. Each object can be used independently to convert an outline into a bitmap or pixmap.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_Span">FT_Span</h3>
<p>Defined in FT_IMAGE_H (freetype/ftimage.h).</p>
<pre>
  <span class="keyword">typedef</span> <span class="keyword">struct</span>  FT_Span_
  {
    <span class="keyword">short</span>           x;
    <span class="keyword">unsigned</span> <span class="keyword">short</span>  len;
    <span class="keyword">unsigned</span> <span class="keyword">char</span>   coverage;

  } <b>FT_Span</b>;
</pre>

<p>A structure used to model a single span of gray pixels when rendering an anti-aliased bitmap.</p>

<h4>fields</h4>
<table class="fields">
<tr><td class="val" id="x">x</td><td class="desc">
<p>The span's horizontal start position.</p>
</td></tr>
<tr><td class="val" id="len">len</td><td class="desc">
<p>The span's length in pixels.</p>
</td></tr>
<tr><td class="val" id="coverage">coverage</td><td class="desc">
<p>The span color/coverage, ranging from 0 (background) to 255 (foreground).</p>
</td></tr>
</table>

<h4>note</h4>
<p>This structure is used by the span drawing callback type named <a href="ft2-raster.html#FT_SpanFunc">FT_SpanFunc</a> that takes the y&nbsp;coordinate of the span as a parameter.</p>
<p>The coverage value is always between 0 and 255. If you want less gray values, the callback function has to reduce them.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_SpanFunc">FT_SpanFunc</h3>
<p>Defined in FT_IMAGE_H (freetype/ftimage.h).</p>
<pre>
  <span class="keyword">typedef</span> <span class="keyword">void</span>
  (*<b>FT_SpanFunc</b>)( <span class="keyword">int</span>             y,
                  <span class="keyword">int</span>             count,
                  <span class="keyword">const</span> <a href="ft2-raster.html#FT_Span">FT_Span</a>*  spans,
                  <span class="keyword">void</span>*           user );

#define FT_Raster_Span_Func  <b>FT_SpanFunc</b>
</pre>

<p>A function used as a call-back by the anti-aliased renderer in order to let client applications draw themselves the gray pixel spans on each scan line.</p>

<h4>input</h4>
<table class="fields">
<tr><td class="val" id="y">y</td><td class="desc">
<p>The scanline's y&nbsp;coordinate.</p>
</td></tr>
<tr><td class="val" id="count">count</td><td class="desc">
<p>The number of spans to draw on this scanline.</p>
</td></tr>
<tr><td class="val" id="spans">spans</td><td class="desc">
<p>A table of &lsquo;count&rsquo; spans to draw on the scanline.</p>
</td></tr>
<tr><td class="val" id="user">user</td><td class="desc">
<p>User-supplied data that is passed to the callback.</p>
</td></tr>
</table>

<h4>note</h4>
<p>This callback allows client applications to directly render the gray spans of the anti-aliased bitmap to any kind of surfaces.</p>
<p>This can be used to write anti-aliased outlines directly to a given background bitmap, and even perform translucency.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_Raster_Params">FT_Raster_Params</h3>
<p>Defined in FT_IMAGE_H (freetype/ftimage.h).</p>
<pre>
  <span class="keyword">typedef</span> <span class="keyword">struct</span>  FT_Raster_Params_
  {
    <span class="keyword">const</span> <a href="ft2-basic_types.html#FT_Bitmap">FT_Bitmap</a>*        target;
    <span class="keyword">const</span> <span class="keyword">void</span>*             source;
    <span class="keyword">int</span>                     flags;
    <a href="ft2-raster.html#FT_SpanFunc">FT_SpanFunc</a>             gray_spans;
    <a href="ft2-raster.html#FT_SpanFunc">FT_SpanFunc</a>             black_spans;  /* unused */
    <a href="ft2-raster.html#FT_Raster_BitTest_Func">FT_Raster_BitTest_Func</a>  bit_test;     /* unused */
    <a href="ft2-raster.html#FT_Raster_BitSet_Func">FT_Raster_BitSet_Func</a>   bit_set;      /* unused */
    <span class="keyword">void</span>*                   user;
    <a href="ft2-basic_types.html#FT_BBox">FT_BBox</a>                 clip_box;

  } <b>FT_Raster_Params</b>;
</pre>

<p>A structure to hold the arguments used by a raster's render function.</p>

<h4>fields</h4>
<table class="fields">
<tr><td class="val" id="target">target</td><td class="desc">
<p>The target bitmap.</p>
</td></tr>
<tr><td class="val" id="source">source</td><td class="desc">
<p>A pointer to the source glyph image (e.g., an <a href="ft2-outline_processing.html#FT_Outline">FT_Outline</a>).</p>
</td></tr>
<tr><td class="val" id="flags">flags</td><td class="desc">
<p>The rendering flags.</p>
</td></tr>
<tr><td class="val" id="gray_spans">gray_spans</td><td class="desc">
<p>The gray span drawing callback.</p>
</td></tr>
<tr><td class="val" id="black_spans">black_spans</td><td class="desc">
<p>Unused.</p>
</td></tr>
<tr><td class="val" id="bit_test">bit_test</td><td class="desc">
<p>Unused.</p>
</td></tr>
<tr><td class="val" id="bit_set">bit_set</td><td class="desc">
<p>Unused.</p>
</td></tr>
<tr><td class="val" id="user">user</td><td class="desc">
<p>User-supplied data that is passed to each drawing callback.</p>
</td></tr>
<tr><td class="val" id="clip_box">clip_box</td><td class="desc">
<p>An optional clipping box. It is only used in direct rendering mode. Note that coordinates here should be expressed in <i>integer</i> pixels (and not in 26.6 fixed-point units).</p>
</td></tr>
</table>

<h4>note</h4>
<p>An anti-aliased glyph bitmap is drawn if the <a href="ft2-raster.html#FT_RASTER_FLAG_XXX">FT_RASTER_FLAG_AA</a> bit flag is set in the &lsquo;flags&rsquo; field, otherwise a monochrome bitmap is generated.</p>
<p>If the <a href="ft2-raster.html#FT_RASTER_FLAG_XXX">FT_RASTER_FLAG_DIRECT</a> bit flag is set in &lsquo;flags&rsquo;, the raster will call the &lsquo;gray_spans&rsquo; callback to draw gray pixel spans. This allows direct composition over a pre-existing bitmap through user-provided callbacks to perform the span drawing and composition. Not supported by the monochrome rasterizer.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_RASTER_FLAG_XXX">FT_RASTER_FLAG_XXX</h3>
<p>Defined in FT_IMAGE_H (freetype/ftimage.h).</p>
<pre>
#define <a href="ft2-raster.html#FT_RASTER_FLAG_DEFAULT">FT_RASTER_FLAG_DEFAULT</a>  0x0
#define <a href="ft2-raster.html#FT_RASTER_FLAG_AA">FT_RASTER_FLAG_AA</a>       0x1
#define <a href="ft2-raster.html#FT_RASTER_FLAG_DIRECT">FT_RASTER_FLAG_DIRECT</a>   0x2
#define <a href="ft2-raster.html#FT_RASTER_FLAG_CLIP">FT_RASTER_FLAG_CLIP</a>     0x4

  /* these constants are deprecated; use the corresponding */
  /* `<b>FT_RASTER_FLAG_XXX</b>' values instead                   */
#define ft_raster_flag_default  <a href="ft2-raster.html#FT_RASTER_FLAG_DEFAULT">FT_RASTER_FLAG_DEFAULT</a>
#define ft_raster_flag_aa       <a href="ft2-raster.html#FT_RASTER_FLAG_AA">FT_RASTER_FLAG_AA</a>
#define ft_raster_flag_direct   <a href="ft2-raster.html#FT_RASTER_FLAG_DIRECT">FT_RASTER_FLAG_DIRECT</a>
#define ft_raster_flag_clip     <a href="ft2-raster.html#FT_RASTER_FLAG_CLIP">FT_RASTER_FLAG_CLIP</a>
</pre>

<p>A list of bit flag constants as used in the &lsquo;flags&rsquo; field of a <a href="ft2-raster.html#FT_Raster_Params">FT_Raster_Params</a> structure.</p>

<h4>values</h4>
<table class="fields">
<tr><td class="val" id="FT_RASTER_FLAG_DEFAULT">FT_RASTER_FLAG_DEFAULT</td><td class="desc">
<p>This value is 0.</p>
</td></tr>
<tr><td class="val" id="FT_RASTER_FLAG_AA">FT_RASTER_FLAG_AA</td><td class="desc">
<p>This flag is set to indicate that an anti-aliased glyph image should be generated. Otherwise, it will be monochrome (1-bit).</p>
</td></tr>
<tr><td class="val" id="FT_RASTER_FLAG_DIRECT">FT_RASTER_FLAG_DIRECT</td><td class="desc">
<p>This flag is set to indicate direct rendering. In this mode, client applications must provide their own span callback. This lets them directly draw or compose over an existing bitmap. If this bit is not set, the target pixmap's buffer <i>must</i> be zeroed before rendering.</p>
<p>Direct rendering is only possible with anti-aliased glyphs.</p>
</td></tr>
<tr><td class="val" id="FT_RASTER_FLAG_CLIP">FT_RASTER_FLAG_CLIP</td><td class="desc">
<p>This flag is only used in direct rendering mode. If set, the output will be clipped to a box specified in the &lsquo;clip_box&rsquo; field of the <a href="ft2-raster.html#FT_Raster_Params">FT_Raster_Params</a> structure.</p>
<p>Note that by default, the glyph bitmap is clipped to the target pixmap, except in direct rendering mode where all spans are generated if no clipping box is set.</p>
</td></tr>
</table>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_Raster_NewFunc">FT_Raster_NewFunc</h3>
<p>Defined in FT_IMAGE_H (freetype/ftimage.h).</p>
<pre>
  <span class="keyword">typedef</span> <span class="keyword">int</span>
  (*<b>FT_Raster_NewFunc</b>)( <span class="keyword">void</span>*       memory,
                        <a href="ft2-raster.html#FT_Raster">FT_Raster</a>*  raster );

#define FT_Raster_New_Func  <b>FT_Raster_NewFunc</b>
</pre>

<p>A function used to create a new raster object.</p>

<h4>input</h4>
<table class="fields">
<tr><td class="val" id="memory">memory</td><td class="desc">
<p>A handle to the memory allocator.</p>
</td></tr>
</table>

<h4>output</h4>
<table class="fields">
<tr><td class="val" id="raster">raster</td><td class="desc">
<p>A handle to the new raster object.</p>
</td></tr>
</table>

<h4>return</h4>
<p>Error code. 0&nbsp;means success.</p>

<h4>note</h4>
<p>The &lsquo;memory&rsquo; parameter is a typeless pointer in order to avoid un-wanted dependencies on the rest of the FreeType code. In practice, it is an <a href="ft2-system_interface.html#FT_Memory">FT_Memory</a> object, i.e., a handle to the standard FreeType memory allocator. However, this field can be completely ignored by a given raster implementation.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_Raster_DoneFunc">FT_Raster_DoneFunc</h3>
<p>Defined in FT_IMAGE_H (freetype/ftimage.h).</p>
<pre>
  <span class="keyword">typedef</span> <span class="keyword">void</span>
  (*<b>FT_Raster_DoneFunc</b>)( <a href="ft2-raster.html#FT_Raster">FT_Raster</a>  raster );

#define FT_Raster_Done_Func  <b>FT_Raster_DoneFunc</b>
</pre>

<p>A function used to destroy a given raster object.</p>

<h4>input</h4>
<table class="fields">
<tr><td class="val" id="raster">raster</td><td class="desc">
<p>A handle to the raster object.</p>
</td></tr>
</table>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_Raster_ResetFunc">FT_Raster_ResetFunc</h3>
<p>Defined in FT_IMAGE_H (freetype/ftimage.h).</p>
<pre>
  <span class="keyword">typedef</span> <span class="keyword">void</span>
  (*<b>FT_Raster_ResetFunc</b>)( <a href="ft2-raster.html#FT_Raster">FT_Raster</a>       raster,
                          <span class="keyword">unsigned</span> <span class="keyword">char</span>*  pool_base,
                          <span class="keyword">unsigned</span> <span class="keyword">long</span>   pool_size );

#define FT_Raster_Reset_Func  <b>FT_Raster_ResetFunc</b>
</pre>

<p>FreeType used to provide an area of memory called the &lsquo;render pool&rsquo; available to all registered rasterizers. This was not thread safe, however, and now FreeType never allocates this pool.</p>
<p>This function is called after a new raster object is created.</p>

<h4>input</h4>
<table class="fields">
<tr><td class="val" id="raster">raster</td><td class="desc">
<p>A handle to the new raster object.</p>
</td></tr>
<tr><td class="val" id="pool_base">pool_base</td><td class="desc">
<p>Previously, the address in memory of the render pool. Set this to NULL.</p>
</td></tr>
<tr><td class="val" id="pool_size">pool_size</td><td class="desc">
<p>Previously, the size in bytes of the render pool. Set this to 0.</p>
</td></tr>
</table>

<h4>note</h4>
<p>Rasterizers should rely on dynamic or stack allocation if they want to (a handle to the memory allocator is passed to the rasterizer constructor).</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_Raster_SetModeFunc">FT_Raster_SetModeFunc</h3>
<p>Defined in FT_IMAGE_H (freetype/ftimage.h).</p>
<pre>
  <span class="keyword">typedef</span> <span class="keyword">int</span>
  (*<b>FT_Raster_SetModeFunc</b>)( <a href="ft2-raster.html#FT_Raster">FT_Raster</a>      raster,
                            <span class="keyword">unsigned</span> <span class="keyword">long</span>  mode,
                            <span class="keyword">void</span>*          args );

#define FT_Raster_Set_Mode_Func  <b>FT_Raster_SetModeFunc</b>
</pre>

<p>This function is a generic facility to change modes or attributes in a given raster. This can be used for debugging purposes, or simply to allow implementation-specific &lsquo;features&rsquo; in a given raster module.</p>

<h4>input</h4>
<table class="fields">
<tr><td class="val" id="raster">raster</td><td class="desc">
<p>A handle to the new raster object.</p>
</td></tr>
<tr><td class="val" id="mode">mode</td><td class="desc">
<p>A 4-byte tag used to name the mode or property.</p>
</td></tr>
<tr><td class="val" id="args">args</td><td class="desc">
<p>A pointer to the new mode/property to use.</p>
</td></tr>
</table>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_Raster_RenderFunc">FT_Raster_RenderFunc</h3>
<p>Defined in FT_IMAGE_H (freetype/ftimage.h).</p>
<pre>
  <span class="keyword">typedef</span> <span class="keyword">int</span>
  (*<b>FT_Raster_RenderFunc</b>)( <a href="ft2-raster.html#FT_Raster">FT_Raster</a>                raster,
                           <span class="keyword">const</span> <a href="ft2-raster.html#FT_Raster_Params">FT_Raster_Params</a>*  params );

#define FT_Raster_Render_Func  <b>FT_Raster_RenderFunc</b>
</pre>

<p>Invoke a given raster to scan-convert a given glyph image into a target bitmap.</p>

<h4>input</h4>
<table class="fields">
<tr><td class="val" id="raster">raster</td><td class="desc">
<p>A handle to the raster object.</p>
</td></tr>
<tr><td class="val" id="params">params</td><td class="desc">
<p>A pointer to an <a href="ft2-raster.html#FT_Raster_Params">FT_Raster_Params</a> structure used to store the rendering parameters.</p>
</td></tr>
</table>

<h4>return</h4>
<p>Error code. 0&nbsp;means success.</p>

<h4>note</h4>
<p>The exact format of the source image depends on the raster's glyph format defined in its <a href="ft2-raster.html#FT_Raster_Funcs">FT_Raster_Funcs</a> structure. It can be an <a href="ft2-outline_processing.html#FT_Outline">FT_Outline</a> or anything else in order to support a large array of glyph formats.</p>
<p>Note also that the render function can fail and return a &lsquo;FT_Err_Unimplemented_Feature&rsquo; error code if the raster used does not support direct composition.</p>
<p>XXX: For now, the standard raster doesn't support direct composition but this should change for the final release (see the files &lsquo;demos/src/ftgrays.c&rsquo; and &lsquo;demos/src/ftgrays2.c&rsquo; for examples of distinct implementations that support direct composition).</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_Raster_Funcs">FT_Raster_Funcs</h3>
<p>Defined in FT_IMAGE_H (freetype/ftimage.h).</p>
<pre>
  <span class="keyword">typedef</span> <span class="keyword">struct</span>  FT_Raster_Funcs_
  {
    <a href="ft2-basic_types.html#FT_Glyph_Format">FT_Glyph_Format</a>        glyph_format;

    <a href="ft2-raster.html#FT_Raster_NewFunc">FT_Raster_NewFunc</a>      raster_new;
    <a href="ft2-raster.html#FT_Raster_ResetFunc">FT_Raster_ResetFunc</a>    raster_reset;
    <a href="ft2-raster.html#FT_Raster_SetModeFunc">FT_Raster_SetModeFunc</a>  raster_set_mode;
    <a href="ft2-raster.html#FT_Raster_RenderFunc">FT_Raster_RenderFunc</a>   raster_render;
    <a href="ft2-raster.html#FT_Raster_DoneFunc">FT_Raster_DoneFunc</a>     raster_done;

  } <b>FT_Raster_Funcs</b>;
</pre>

<p>A structure used to describe a given raster class to the library.</p>

<h4>fields</h4>
<table class="fields">
<tr><td class="val" id="glyph_format">glyph_format</td><td class="desc">
<p>The supported glyph format for this raster.</p>
</td></tr>
<tr><td class="val" id="raster_new">raster_new</td><td class="desc">
<p>The raster constructor.</p>
</td></tr>
<tr><td class="val" id="raster_reset">raster_reset</td><td class="desc">
<p>Used to reset the render pool within the raster.</p>
</td></tr>
<tr><td class="val" id="raster_render">raster_render</td><td class="desc">
<p>A function to render a glyph into a given bitmap.</p>
</td></tr>
<tr><td class="val" id="raster_done">raster_done</td><td class="desc">
<p>The raster destructor.</p>
</td></tr>
</table>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_Raster_BitTest_Func">FT_Raster_BitTest_Func</h3>
<p>Defined in FT_IMAGE_H (freetype/ftimage.h).</p>
<pre>
  <span class="keyword">typedef</span> <span class="keyword">int</span>
  (*<b>FT_Raster_BitTest_Func</b>)( <span class="keyword">int</span>    y,
                             <span class="keyword">int</span>    x,
                             <span class="keyword">void</span>*  user );
</pre>

<p>Deprecated, unimplemented.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

<div class="section">
<h3 id="FT_Raster_BitSet_Func">FT_Raster_BitSet_Func</h3>
<p>Defined in FT_IMAGE_H (freetype/ftimage.h).</p>
<pre>
  <span class="keyword">typedef</span> <span class="keyword">void</span>
  (*<b>FT_Raster_BitSet_Func</b>)( <span class="keyword">int</span>    y,
                            <span class="keyword">int</span>    x,
                            <span class="keyword">void</span>*  user );
</pre>

<p>Deprecated, unimplemented.</p>

<hr>
<table class="index-toc-link"><tr><td class="left">[<a href="ft2-index.html">Index</a>]</td><td class="middle">[<a href="#">Top</a>]</td><td class="right">[<a href="ft2-toc.html">TOC</a>]</td></tr></table></div>

</body>
</html>

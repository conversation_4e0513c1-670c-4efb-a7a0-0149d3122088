/***************************************************************************/
/*                                                                         */
/*  ftver.rc                                                               */
/*                                                                         */
/*    FreeType VERSIONINFO resource for Windows DLLs.                      */
/*                                                                         */
/*  Copyright 2018 by                                                      */
/*  <PERSON>, <PERSON>, and <PERSON>.                      */
/*                                                                         */
/*  This file is part of the FreeType project, and may only be used,       */
/*  modified, and distributed under the terms of the FreeType project      */
/*  license, LICENSE.TXT.  By continuing to use, modify, or distribute     */
/*  this file you indicate that you have read the license and              */
/*  understand and accept it fully.                                        */
/*                                                                         */
/***************************************************************************/


#include<windows.h>

#define FT_VERSION      2,9,1,0
#define FT_VERSION_STR  "2.9.1"

VS_VERSION_INFO      VERSIONINFO
FILEVERSION          FT_VERSION
PRODUCTVERSION       FT_VERSION
FILEFLAGSMAS<PERSON>        VS_FFI_FILEFLAGSMASK
#ifdef _DEBUG
FILEFLAGS            VS_FF_DEBUG
#endif
#ifdef _DLL
FILETYPE             VFT_DLL
#define FT_FILENAME  "freetype.dll"
#else
FILETYPE             VFT_STATIC_LIB
#define FT_FILENAME  "freetype.lib"
#endif
BEGIN
  BLOCK "StringFileInfo"
  BEGIN
    BLOCK "040904E4"
    BEGIN
      VALUE "CompanyName",      "The FreeType Project"
      VALUE "FileDescription",  "Font Rendering Library"
      VALUE "FileVersion",      FT_VERSION_STR
      VALUE "ProductName",      "FreeType"
      VALUE "ProductVersion",   FT_VERSION_STR
      VALUE "LegalCopyright",   "\251 2018 The FreeType Project www.freetype.org. All rights reserved."
      VALUE "InternalName",     "freetype"
      VALUE "OriginalFilename", FT_FILENAME
    END
  END

  BLOCK "VarFileInfo"
  BEGIN
    /* The following line should only be modified for localized versions.  */
    /* It consists of any number of WORD,WORD pairs, with each pair        */
    /* describing a "language,codepage" combination supported by the file. */
    VALUE "Translation", 0x409, 1252
  END
END

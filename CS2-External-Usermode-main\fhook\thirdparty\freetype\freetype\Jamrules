# FreeType 2 JamRules.
#
# Copyright 2001-2018 by
# <PERSON>, <PERSON>, and <PERSON>.
#
# This file is part of the FreeType project, and may only be used, modified,
# and distributed under the terms of the FreeType project license,
# LICENSE.TXT.  By continuing to use, modify, or distribute this file you
# indicate that you have read the license and understand and accept it
# fully.


# This file contains the Jam rules needed to build the FreeType 2 library.
# It is shared by all Jamfiles and is included only once in the build
# process.
#


# Call SubDirHdrs on a list of directories.
#
rule AddSubDirHdrs
{
  local x ;

  for x in $(<)
  {
    SubDirHdrs $(x) ;
  }
}


# Determine prefix of library file.  We must use "libxxxxx" on Unix systems,
# while all other simply use the real name.
#
if $(UNIX)
{
  LIBPREFIX ?= lib ;
}
else
{
  LIBPREFIX ?= "" ;
}

# FT2_TOP contains the location of the FreeType source directory.  You can
# set it to a specific value if you want to compile the library as part of a
# larger project.
#
FT2_TOP ?= $(DOT) ;

# Define a new rule used to declare a sub directory of the Nirvana source
# tree.
#
rule FT2_SubDir
{
  if $(FT2_TOP) = $(DOT)
  {
    return [ FDirName  $(<) ] ;
  }
  else
  {
    return [ FDirName  $(FT2_TOP) $(<) ] ;
  }
}

# We also set ALL_LOCATE_TARGET in order to place all object and library
# files in "objs".
#
ALL_LOCATE_TARGET ?= [ FT2_SubDir  objs ] ;


# end of Jamrules

/* ATTENTION: This file doesn't compile.  It is only here as a reference */
/*            of an alternative latin hinting algorithm that was always  */
/*            marked as experimental.                                    */


/***************************************************************************/
/*                                                                         */
/*  aflatin2.h                                                             */
/*                                                                         */
/*    Auto-fitter hinting routines for latin writing system                */
/*    (specification).                                                     */
/*                                                                         */
/*  Copyright 2003-2018 by                                                 */
/*  <PERSON>, <PERSON>, and <PERSON>.                      */
/*                                                                         */
/*  This file is part of the FreeType project, and may only be used,       */
/*  modified, and distributed under the terms of the FreeType project      */
/*  license, LICENSE.TXT.  By continuing to use, modify, or distribute     */
/*  this file you indicate that you have read the license and              */
/*  understand and accept it fully.                                        */
/*                                                                         */
/***************************************************************************/


#ifndef AFLATIN2_H_
#define AFLATIN2_H_

#include "afhints.h"


FT_BEGIN_HEADER


  /* the `latin' writing system */

  AF_DECLARE_WRITING_SYSTEM_CLASS( af_latin2_writing_system_class )


/* */

FT_END_HEADER

#endif /* AFLATIN_H_ */


/* END */

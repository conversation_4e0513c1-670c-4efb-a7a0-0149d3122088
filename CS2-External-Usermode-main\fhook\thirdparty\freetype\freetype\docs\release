How to prepare a new release
----------------------------

. include/freetype/freetype.h:  Update FREETYPE_MAJOR, FREETYPE_MINOR,
  and FREETYPE_PATCH.

. Update version numbers in all files where necessary (for example, do
  a grep for both `2.3.1' and `231' for release 2.3.1).

. builds/unix/configure.raw: Update `version_info'.

. docs/CHANGES: Document differences to last release.

. README: Update.

. docs/VERSIONS.TXT: Document changed `version_info'.

. ChangeLog:   Announce  new  release   (both  in  the  freetype2  and
  freetype2-demos modules).

. Clone the git archive to another directory with

    git clone -l -s . ../freetype2.test

  or something like this and run

    make distclean; make devel; make
    make distclean; make devel; make multi
    make distclean; make devel CC=g++; make CC=g++
    make distclean; make devel CC=g++; make multi CC=g++

    sh autogen.sh
    make distclean; ./configure; make
    make distclean; ./configure CC=g++; make

  in the cloned repository to test compilation with both gcc and g++.

. Test C++ compilation  for freetype2-demos too  (using `git clone' as
  above).

. Run  src/tools/chktrcmp.py  and check  that there  are no  undefined
  trace_XXXX macros.

. After pushing the new release,  tag the git repositories (freetype2,
  freetype2-demos) with

    git tag VER-<version> -m "" -u <committer>

  and push the tags with

    git push --tags

. Check with

    git clean -ndx

  that the git directory is really clean  (and remove extraneous files
  if necessary).

. Say `make  dist' in both the  freetype2 and freetype2-demos  modules
  to generate the .tar.gz, .tar.bz2, and .zip files.

. Create     the     doc    bundles    (freetype-doc-<version>.tar.gz,
  freetype-doc-<version>.tar.bz2,    ftdoc<version>.zip).    This   is
  everything in

    <freetype-web git repository>/freetype2/docs

  except the `reference' subdirectory.   Do *not* use option `-l' from
  zip!

. Run the following script (with updated `$VERSION', `$SAVANNAH_USER',
  and $SOURCEFORGE_USER  variables) to sign and upload the  bundles to
  both Savannah and SourceForge.  The signing code has been taken from
  the `gnupload' script (part of the automake bundle).

    #!/bin/sh

    VERSION=2.5.1
    SAVANNAH_USER=wl
    SOURCEFORGE_USER=wlemb

    #####################################################################

    GPG='/usr/bin/gpg --batch --no-tty'

    version=`echo $VERSION | sed "s/\\.//g"`

    FREETYPE_PACKAGES="freetype-$VERSION.tar.gz \
                       freetype-$VERSION.tar.bz2 \
                       ft$version.zip"
    FT2DEMOS_PACKAGES="ft2demos-$VERSION.tar.gz \
                       ft2demos-$VERSION.tar.bz2 \
                       ftdmo$version.zip"
    FTDOC_PACKAGES="freetype-doc-$VERSION.tar.gz \
                    freetype-doc-$VERSION.tar.bz2 \
                    ftdoc$version.zip"

    PACKAGE_LIST="$FREETYPE_PACKAGES \
                  $FT2DEMOS_PACKAGES \
                  $FTDOC_PACKAGES"

    set -e
    unset passphrase

    PATH=/empty echo -n "Enter GPG passphrase: "
    stty -echo
    read -r passphrase
    stty echo
    echo

    for f in $PACKAGE_LIST; do
      if test ! -f $f; then
        echo "$0: Cannot find \`$f'" 1>&2
        exit 1
      else
        :
      fi
    done

    for f in $PACKAGE_LIST; do
      echo "Signing $f..."
      rm -f $f.sig
      echo $passphrase | $GPG --passphrase-fd 0 -ba -o $f.sig $f
    done

    FREETYPE_SIGNATURES=
    for i in $FREETYPE_PACKAGES; do
      FREETYPE_SIGNATURES="$FREETYPE_SIGNATURES $i.sig"
    done

    FT2DEMOS_SIGNATURES=
    for i in $FT2DEMOS_PACKAGES; do
      FT2DEMOS_SIGNATURES="$FT2DEMOS_SIGNATURES $i.sig"
    done

    FTDOC_SIGNATURES=
    for i in $FTDOC_PACKAGES; do
      FTDOC_SIGNATURES="$FTDOC_SIGNATURES $i.sig"
    done

    SIGNATURE_LIST="$FREETYPE_SIGNATURES \
                    $FT2DEMOS_SIGNATURES \
                    $FTDOC_SIGNATURES"

    scp $PACKAGE_LIST $SIGNATURE_LIST \
      $<EMAIL>:/releases/freetype/

    rsync -avP -e ssh $FREETYPE_PACKAGES $FREETYPE_SIGNATURES \
      $SOURCEFORGE_USER,<EMAIL>:/home/<USER>/project/f/fr/freetype/freetype2/$VERSION/
    rsync -avP -e ssh $FT2DEMOS_PACKAGES $FT2DEMOS_SIGNATURES \
      $SOURCEFORGE_USER,<EMAIL>:/home/<USER>/project/f/fr/freetype/freetype-demos/$VERSION/
    rsync -avP -e ssh $FTDOC_PACKAGES $FTDOC_SIGNATURES \
      $SOURCEFORGE_USER,<EMAIL>:/home/<USER>/project/f/fr/freetype/freetype-docs/$VERSION/

    # EOF

. Prepare a  README for SourceForge  and upload it  with the following
  script (with updated `$VERSION' and $SOURCEFORGE_USER variables).

    #!/bin/sh

    VERSION=2.5.1
    SOURCEFORGE_USER=wlemb

    #####################################################################

    rsync -avP -e ssh README \
      $SOURCEFORGE_USER,<EMAIL>:/home/<USER>/project/f/fr/freetype/freetype2/$VERSION/

    # EOF

. On   SourceForge,   tag   the    just   uploaded   `ftXXX.zip'   and
  `freetype-XXX.tar.bz2'  files as the  default files to  download for
  `Windows' and `Others', respectively.

. Copy the reference files (generated by `make dist') to

    <freetype-web git repository>/freetype2/docs/reference

. Update the `freetype-web' repository.  `git push' then automatically
  triggers an update  of the public web pages  within ten minutes, due
  to a cron script (on <EMAIL>) that rsyncs with

    freedesktop.org://srv/freetype.freedesktop.org/www

. Announce new <NAME_EMAIL> and to relevant
  newsgroups.

----------------------------------------------------------------------

Copyright 2003-2018 by
David Turner, Robert Wilhelm, and Werner Lemberg.

This  file is  part of  the FreeType  project, and  may only  be used,
modified,  and distributed  under the  terms of  the  FreeType project
license,  LICENSE.TXT.  By  continuing to  use, modify,  or distribute
this file you  indicate that you have read  the license and understand
and accept it fully.


--- end of release ---

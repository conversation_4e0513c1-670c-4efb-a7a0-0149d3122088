﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;c++;cppm;ixx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hh;hpp;hxx;h++;hm;inl;inc;ipp;xsd</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="fhook\core\ctx\ctx.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="fhook\framework\config\cfg.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="fhook\framework\framework.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="fhook\hacks\ctx\hacks_ctx.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="fhook\hacks\features\esp\esp.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="fhook\hacks\features\flash_builder\flash_builder.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="fhook\hacks\features\grenade\grenades.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="fhook\hacks\features\legitbot\legitbot.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="fhook\hacks\features\ragebot\ragebot.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="fhook\hacks\features\shots\shots.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="fhook\hacks\features\shots\shots_hitsound.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="fhook\hacks\features\sound_system\sound.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="fhook\hacks\features\triggerbot\triggerbot.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="fhook\inc.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="fhook\sdk\animation_system\animation_system.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="fhook\sdk\classes\bone_system.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="fhook\sdk\classes\entity.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="fhook\sdk\classes\view.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="fhook\sdk\input_system\input_system.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="fhook\sdk\math\color_t.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="fhook\sdk\math\rect_t.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="fhook\sdk\math\str_t.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="fhook\sdk\memory\mem.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="fhook\sdk\offsets\offsets.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="fhook\sdk\process_manager\process_manager.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="fhook\sdk\render\render.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="fhook\sdk\render\render_fonts.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="fhook\sdk\render\render_sdk.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="fhook\sdk\reverse\reversed_funcs.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="fhook\thirdparty\bytes.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="fhook\thirdparty\custom.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="fhook\thirdparty\imconfig.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="fhook\thirdparty\imgui.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="fhook\thirdparty\imgui_freetype.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="fhook\thirdparty\imgui_impl_dx11.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="fhook\thirdparty\imgui_impl_dx9.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="fhook\thirdparty\imgui_impl_win32.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="fhook\thirdparty\imgui_internal.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="fhook\thirdparty\imstb_rectpack.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="fhook\thirdparty\imstb_textedit.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="fhook\thirdparty\imstb_truetype.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="fhook\core\core.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="fhook\core\ctx\ctx.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="fhook\framework\config\cfg.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="fhook\framework\framework.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="fhook\hacks\ctx\hacks_ctx.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="fhook\hacks\features\esp\esp.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="fhook\hacks\features\flash_builder\flash_builder.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="fhook\hacks\features\grenade\grenades.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="fhook\hacks\features\legitbot\legitbot.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="fhook\hacks\features\ragebot\ragebot.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="fhook\hacks\features\shots\shots.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="fhook\hacks\features\sound_system\sound.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="fhook\hacks\features\triggerbot\triggerbot.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="fhook\sdk\animation_system\animation_system.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="fhook\sdk\input_system\input_system.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="fhook\sdk\process_manager\process_manager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="fhook\sdk\render\render.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="fhook\sdk\reverse\reversed_funcs.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="fhook\thirdparty\custom.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="fhook\thirdparty\imgui.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="fhook\thirdparty\imgui_demo.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="fhook\thirdparty\imgui_draw.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="fhook\thirdparty\imgui_freetype.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="fhook\thirdparty\imgui_impl_dx11.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="fhook\thirdparty\imgui_impl_dx9.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="fhook\thirdparty\imgui_impl_win32.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="fhook\thirdparty\imgui_tables.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="fhook\thirdparty\imgui_widgets.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
</Project>
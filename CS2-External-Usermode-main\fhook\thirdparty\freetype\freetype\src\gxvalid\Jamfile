# FreeType 2 src/gxvalid Jamfile
#
# Copyright 2005-2018 by
# <PERSON><PERSON> to<PERSON>ya, <PERSON><PERSON><PERSON> and Red Hat K.K.
#
# This file is part of the FreeType project, and may only be used, modified,
# and distributed under the terms of the FreeType project license,
# LICENSE.TXT.  By continuing to use, modify, or distribute this file you
# indicate that you have read the license and understand and accept it
# fully.

SubDir  FT2_TOP $(FT2_SRC_DIR) gxvalid ;


{
  local  _sources ;

  if $(FT2_MULTI)
  {
    _sources = gxvbsln
               gxvcommn
               gxvfeat
               gxvjust
               gxvkern
               gxvlcar
               gxvmod
               gxvmort
               gxvmort0
               gxvmort1
               gxvmort2
               gxvmort4
               gxvmort5
               gxvmorx
               gxvmorx0
               gxvmorx1
               gxvmorx2
               gxvmorx4
               gxvmorx5
               gxvopbd
               gxvprop
               gxvtrak
               ;
  }
  else
  {
    _sources = gxvalid ;
  }

  Library  $(FT2_LIB) : $(_sources).c ;
}

# end of src/gxvalid Jamfile
